from django.urls import path
from . import views
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register("category", views.CategoryViewSet, basename='category')


urlpatterns = [
    path("create-property/", views.CreateFavoriteViewSetView.as_view()),
    path(r"properties/", views.RetrieveFavoriteViewSet.as_view()),
    path(r"properties/<int:pk>/", views.RetrieveFavoriteByUserIdViewSet.as_view()),
    path(r"activities/", views.AgentCustomerActivityViewSet.as_view()),
    path(r"activities/<int:pk>/", views.RetrieveCustomerAgentActivityViewSet.as_view()),
]


urlpatterns += router.urls
