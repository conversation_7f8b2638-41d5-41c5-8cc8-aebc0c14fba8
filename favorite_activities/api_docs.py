from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from favorite_activities.serializers import CreateFavoritePropertySerializer
FAVOURITES_SWAGGER_DOCS = swagger_auto_schema(
    operation_description="Password Reset",
    responses={},
    manual_parameters=[
        openapi.Parameter(
            "user_id",
            openapi.IN_QUERY,
            description="user ID",
            type=openapi.TYPE_STRING,

        ),
        openapi.Parameter(
            "uncategorized",
            openapi.IN_QUERY,
            description="uncategorized",
            type=openapi.TYPE_BOOLEAN,
        ),
        openapi.Parameter(
            "category",
            openapi.IN_QUERY,
            description="category",
            type=openapi.TYPE_NUMBER,
        ),
    ],
)


FAVOURITES_CREATE_SWAGGER_DOCS = swagger_auto_schema(
    responses={200: CreateFavoritePropertySerializer}
)
