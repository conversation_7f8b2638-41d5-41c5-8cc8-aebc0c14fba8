# Generated by Django 3.2.15 on 2022-11-10 16:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('favorite_activities', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AgentActivity',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('agent_id', models.IntegerField(blank=True, default=0)),
                ('customer_id', models.IntegerField(blank=True, default=0)),
                ('description', models.CharField(blank=True, max_length=225, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'agent_customer_activity',
            },
        ),
    ]
