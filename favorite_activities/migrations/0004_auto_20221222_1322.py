# Generated by Django 3.2.15 on 2022-12-22 13:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('favorite_activities', '0003_agentactivity_lead_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='favorite',
            name='address',
            field=models.CharField(blank=True, max_length=225, null=True),
        ),
        migrations.AddField(
            model_name='favorite',
            name='area',
            field=models.CharField(blank=True, max_length=225, null=True),
        ),
        migrations.AddField(
            model_name='favorite',
            name='baths',
            field=models.IntegerField(blank=0, default=0, null=True),
        ),
        migrations.AddField(
            model_name='favorite',
            name='bedrooms',
            field=models.IntegerField(blank=0, default=0, null=True),
        ),
        migrations.AddField(
            model_name='favorite',
            name='date_created',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name='favorite',
            name='image',
            field=models.ImageField(null=True, upload_to='favorite_images'),
        ),
        migrations.AddField(
            model_name='favorite',
            name='list_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='favorite',
            name='price',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='favorite',
            name='status',
            field=models.CharField(blank=True, max_length=225, null=True),
        ),
    ]
