# Generated by Django 3.2.15 on 2022-11-09 20:58

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Favorite',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('user_id', models.IntegerField(blank=0, default=0, null=True)),
                ('property_id', models.IntegerField(blank=0, default=0, null=True)),
            ],
            options={
                'db_table': 'customers_favorite_properties',
            },
        ),
    ]
