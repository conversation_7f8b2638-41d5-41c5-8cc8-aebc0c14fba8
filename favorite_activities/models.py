from django.db import models
from django.contrib.auth import get_user_model


class Category(models.Model):
    name = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    is_primary = models.BooleanField(default=False)
    created_by = models.ForeignKey(get_user_model(), on_delete=models.CASCADE)

    def save(self, *args, **kwargs):
        if self.is_primary:
            Category.objects.filter(created_by=self.created_by, is_primary=True).exclude(
                id=self.id).update(is_primary=False)

        super().save(*args, **kwargs)


class Favorite(models.Model):
    category = models.ManyToManyField(Category, blank=True)
    note = models.TextField(blank=True)
    id = models.AutoField(primary_key=True)
    user_id = models.IntegerField(default=0, null=True, blank=0)
    property_id = models.IntegerField(default=0, null=True, blank=0)
    address = models.CharField(max_length=225, null=True, blank=True)
    status = models.Char<PERSON>ield(max_length=225, null=True, blank=True)
    price = models.FloatField(null=True, blank=True)
    image = models.TextField(null=True, blank=True)
    bedrooms = models.IntegerField(default=0, null=True, blank=0)
    baths = models.IntegerField(default=0, null=True, blank=0)
    area = models.CharField(max_length=225, null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    list_date = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True, null=True, blank=True)

    class Meta:
        db_table = "customers_favorite_properties"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        if not self.category.exists():
            primary_category = Category.objects.filter(created_by_id=self.user_id, is_primary=True).first()

            if not primary_category:
                primary_category, _ = Category.objects.get_or_create(
                    name="My saved homes",
                    is_primary=True,
                    created_by_id=self.user_id
                )

            self.category.add(primary_category)


class AgentActivity(models.Model):
    id = models.AutoField(primary_key=True)
    agent_id = models.IntegerField(default=0, blank=True)
    customer_id = models.IntegerField(default=0, blank=True)
    lead_id = models.IntegerField(default=0, blank=True)
    description = models.CharField(max_length=225, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = "agent_customer_activity"
