from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import mixins, generics
from .errors import ResourceNotFound
from .models import Favorite, AgentActivity, Category
from .serializers import (
    RetrieveFavoritePropertySerializer,
    CreateFavoritePropertySerializer,
    CreateAgentCustomerActivitySerializer,
    RetrieveAgentCustomerActivitySerializer,
    CategorySerializer, UpdateFavoritePropertySerializer
)
from rest_framework.permissions import AllowAny
from rest_framework import status
from favorite_activities.api_docs import FAVOURITES_SWAGGER_DOCS, FAVOURITES_CREATE_SWAGGER_DOCS
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from core.models import User
from django.shortcuts import get_object_or_404


class CategoryViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = CategorySerializer

    def get_queryset(self):
        queryset = Category.objects.filter(created_by=self.request.user)

        return queryset


class CreateFavoriteViewSetView(generics.CreateAPIView):
    serializer_class = RetrieveFavoritePropertySerializer
    permission_classes = (AllowAny,)
    model = Favorite

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            data=request.data,
            context={
            },
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return serializer.data

    def post(self, request, *args, **kwargs):
        data = self.create(request, *args, **kwargs)
        return Response(data)


class RetrieveFavoriteViewSet(mixins.ListModelMixin, APIView):
    serializer_class = RetrieveFavoritePropertySerializer
    query_set = Favorite.objects.all()
    lookup_field = "pk"

    def get_object(self, pk):
        try:
            return Favorite.objects.get(pk=pk)
        except Favorite.DoesNotExist:
            raise ResourceNotFound

    def list(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    @FAVOURITES_SWAGGER_DOCS
    def get(self, request, *args, **kwargs):
        get_user_id = self.request.GET.get("user_id", None)
        uncategorized = self.request.GET.get("uncategorized", None)
        category = self.request.GET.get("category", None)

        if get_user_id:
            favorites = Favorite.objects.filter(user_id=get_user_id)

        if uncategorized and get_user_id:

            favorites = Favorite.objects.filter(category__isnull=True,
                                                user_id=get_user_id)

        if category:
            favorites = Favorite.objects.filter(category=category)

        else:
            favorites = Favorite.objects.all()

        serializer = RetrieveFavoritePropertySerializer(favorites, many=True)
        return Response(data=serializer.data, status=status.HTTP_200_OK)

    @FAVOURITES_CREATE_SWAGGER_DOCS
    def post(self, request, *args, **kwargs):
        serializer = CreateFavoritePropertySerializer(data=request.data)

        serializer.is_valid(raise_exception=True)

        user = request.data.get('user_id', None)

        user = get_object_or_404(User, id=user)

        serializer.save()

        return Response(data=serializer.data, status=status.HTTP_200_OK)


class RetrieveFavoriteByUserIdViewSet(APIView):
    serializer_class = RetrieveFavoritePropertySerializer
    query_set = Favorite.objects.all()
    lookup_field = "pk"

    def get_object(self, pk):
        try:
            return Favorite.objects.get(pk=pk)
        except Favorite.DoesNotExist:
            raise ResourceNotFound

    def get(self, request, pk, format=None):
        favorites = Favorite.objects.filter(user_id=pk).values("property_id")
        serializer = RetrieveFavoritePropertySerializer(data=favorites, many=True)
        property_list = []
        for property in favorites:
            property_list.append(property)

        response = {"favorites": property_list}
        return Response(data=response, status=status.HTTP_200_OK)

    def delete(self, request, pk, format=None):
        event = Favorite.objects.filter(property_id=pk)
        event.delete()
        return Response(
            {"message": f"Favorite {pk} deleted"}, status=status.HTTP_204_NO_CONTENT
        )

    @FAVOURITES_CREATE_SWAGGER_DOCS
    def patch(self, request, *args, **kwargs):
        instance = self.get_object(kwargs.get('pk'))

        serializer = UpdateFavoritePropertySerializer(
            instance=instance, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class AgentCustomerActivityViewSet(APIView):
    serializer_class = CreateAgentCustomerActivitySerializer
    queryset = AgentActivity.objects.all()
    lookup_field = "pk"

    def get_object(self, pk):
        try:
            return AgentActivity.objects.get(agent_id=pk)
        except AgentActivity.DoesNotExist:
            raise ResourceNotFound

    def get(self, request, *args, **kwargs):
        activities = AgentActivity.objects.all()
        serializer = RetrieveAgentCustomerActivitySerializer(activities, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, *args, **kwargs):
        serializer = CreateAgentCustomerActivitySerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(serializer.data, status=status.HTTP_400_BAD_REQUEST)

    def list(self, request, *args, **kwargs):
        return self.list(request, *args, **kwargs)


class RetrieveCustomerAgentActivityViewSet(APIView):
    serializer_class = RetrieveAgentCustomerActivitySerializer
    # query_set = Favorite.objects.all()
    lookup_field = "pk"

    def get_object(self, pk):
        try:
            return AgentActivity.objects.get(lead_id=pk)
        except AgentActivity.DoesNotExist:
            raise ResourceNotFound

    def get(self, request, pk, format=None):
        activities = AgentActivity.objects.filter(lead_id=pk)
        serializer = RetrieveAgentCustomerActivitySerializer(activities, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
