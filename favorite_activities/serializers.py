from rest_framework import serializers
from .models import Favorite, AgentActivity, Category
import random
from core.models import User


class CategorySerializer(serializers.ModelSerializer):
    favorite_images = serializers.SerializerMethodField()
    total = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = '__all__'
        read_only_fields = ['id', 'created_by']

    def create(self, validated_data):
        user: User = self.context["request"].user
        validated_data["created_by"] = user

        return super().create(validated_data)

    def get_favorite_images(self, obj: Category):
        favorites = Favorite.objects.filter(category=obj).values_list('image', flat=True)[:10]
        images = [favorite for favorite in favorites if favorite]

        random_images = random.sample(images, min(5, len(images)))

        return random_images

    def get_total(self, obj):
        return Favorite.objects.filter(category=obj).count()


class CreateFavoritePropertySerializer(serializers.ModelSerializer):

    class Meta:
        model = Favorite
        fields = '__all__'


class RetrieveFavoritePropertySerializer(serializers.ModelSerializer):

    class Meta:
        model = Favorite
        fields = '__all__'


class UpdateFavoritePropertySerializer(serializers.ModelSerializer):

    class Meta:
        model = Favorite
        fields = '__all__'


class CreateAgentCustomerActivitySerializer(serializers.ModelSerializer):

    class Meta:
        model = AgentActivity
        fields = "__all__"


class RetrieveAgentCustomerActivitySerializer(serializers.ModelSerializer):

    class Meta:
        model = AgentActivity
        fields = "__all__"

    def get(self, validated_data):
        instance = super(AgentActivity, self).get(validated_data)
        return instance
