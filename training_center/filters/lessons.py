

from django_filters.rest_framework import FilterSet, filters
from training_center.models import Lesson, Module
from django.db.models import Q


class LessonFilter(FilterSet):
    title = filters.CharFilter(lookup_expr='icontains', required=False)
    paragraph = filters.CharFilter(lookup_expr='icontains', required=False)
    overview = filters.CharFilter(lookup_expr='icontains', required=False)

    module = filters.ModelChoiceFilter(
        queryset=Module.objects.all(),
        method="filter_module",
        help_text="Filter by module",
        label="Filter by module",
        required=False
    )

    search = filters.CharFilter(
        method="filter_search", help_text="Search by  overview or title",
        label="Search by  overview or title", required=False
    )

    class Meta:
        model = Lesson
        fields = [
            "created_by",
            "module",
            "title",
            "paragraph",
            "overview",
        ]

    def filter_module(self, queryset, name, value):
        if value:
            return queryset.filter(module=value)
        return queryset

    def filter_search(self, queryset, name, value):
        if value:

            return queryset.filter(
                Q(title__icontains=value)
                | Q(overview__icontains=value)

            )
        return queryset
