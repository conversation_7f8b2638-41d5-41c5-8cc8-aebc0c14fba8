# Generated by Django 3.2.15 on 2025-06-13 14:22

import django.contrib.postgres.fields
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('training_center', '0010_trainingcenteruser_is_agent_admin'),
    ]

    operations = [
        migrations.AddField(
            model_name='zoom',
            name='days_of_month',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.PositiveSmallIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)]), blank=True, null=True, size=None),
        ),
    ]
