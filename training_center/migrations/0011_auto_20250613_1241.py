# Generated by Django 3.2.15 on 2025-06-13 12:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('training_center', '0010_trainingcenteruser_is_agent_admin'),
    ]

    operations = [
        migrations.AddField(
            model_name='zoom',
            name='end_condition',
            field=models.CharField(blank=True, choices=[('never', 'Never'), ('after_occurrences', 'After Occurrences'), ('until_date', 'Until Date')], max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='zoom',
            name='end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='zoom',
            name='occurrences',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
