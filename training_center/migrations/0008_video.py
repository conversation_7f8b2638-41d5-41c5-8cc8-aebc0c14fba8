# Generated by Django 3.2.15 on 2025-05-15 16:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('literals', '0004_state'),
        ('training_center', '0007_lessonfile_thumbnail'),
    ]

    operations = [
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='videos/%Y-%m-%d')),
                ('caption', models.TextField(blank=True, null=True)),
                ('thumbnail', models.FileField(blank=True, null=True, upload_to='')),
                ('link', models.URLField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='training_center.lesson')),
                ('target_audience', models.ManyToManyField(blank=True, to='literals.TargetUserGroup')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
