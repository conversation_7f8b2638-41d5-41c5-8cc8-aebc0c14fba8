import os
import subprocess
import shutil

# Database connection details
# USER = "postgres"
# PASSWORD = "jkm054"
# HOST = "localhost"
# PORT = "5432"
# DBNAME = "bpohomes"
# DBTABLE = 'imported_data1'

USER = "USER"
HOST = "HOST"
DBNAME = "DBNAME"
PORT = "PORT"
PASSWORD = "PASSWORD"
DBTABLE = 'DB TABLE TO EXTRACT THE DATA TO'
LIGHTBOX_PATH = "LIGHTBOX_FOLDER _PATH, eg, home/user/data/LIGHTBOX"

# Paths
LIGHTBOX_PATH = "/LIGHTBOX_FOLDER _PATH/" # /home/<USER>/Desktop/lightbox/

DONE_PATH = os.path.join(LIGHTBOX_PATH, "done") # moves all the completed lightbox to done

# Ensure the "done" directory exists
os.makedirs(DONE_PATH, exist_ok=True)

def is_geospatial_file(filename):
    """
    Check if a file is a geospatial file (shp, geojson, gdb, etc.).
    """
    geospatial_extensions = ['.shp', '.geojson', '.gdb', '.kml', '.gpkg']
    return any(filename.lower().endswith(ext) for ext in geospatial_extensions)

def extract_data():
    """
    Processes each valid geospatial file/folder in LIGHTBOX_PATH with ogr2ogr and moves it to DONE_PATH on success.
    """
    try:
        # List of files and folders in LIGHTBOX_PATH
        paths = os.listdir(LIGHTBOX_PATH)

        for path in paths:
            full_path = os.path.join(LIGHTBOX_PATH, path)

            # Process only geospatial directories or files
            if os.path.isdir(full_path) or is_geospatial_file(full_path):
                print(f"Processing: {path}")
                try:
                    # Run ogr2ogr command
                    subprocess.run(
                        f'ogr2ogr -progress -overwrite -skipfailures -f "PostgreSQL" '
                        f'PG:"host={HOST} port={PORT} user={USER} dbname={DBNAME} password={PASSWORD}" "{full_path}" -nln {DBTABLE}',
                        shell=True,
                        check=True,
                    )
                    print(f"Successfully imported: {path}")

                    # Move the processed file/folder to the "done" directory
                    destination = os.path.join(DONE_PATH, path)
                    shutil.move(full_path, destination)
                    print(f"Moved {path} to done folder.")

                except subprocess.CalledProcessError as e:
                    print(f"Error processing {path}: {e}")
            else:
                print(f"Skipping non-geospatial file: {path}")

    except Exception as e:
        print(f"Error: {e}")

# Run the extraction function
extract_data()
