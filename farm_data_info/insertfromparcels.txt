INSERT INTO public.farm_parcel (
    fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, 
    site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, site_county, site_state, 
    site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, 
    owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, 
    mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, 
    mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, 
    use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, 
    asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, 
    stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, 
    pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, 
    last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, 
    assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape,
    last_market_sale_seller_name, last_market_sale_seller_1_first_mid, last_market_sale_seller_1_last,
    last_market_sale_seller_1_code, last_market_sale_seller_2_first_mid, last_market_sale_seller_2_last
)
SELECT 
    fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, 
    site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, 
    site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, 
    owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, 
    mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, 
    mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, 
    use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, 
    asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, 
    stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, 
    pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, 
    last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, 
    assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape,
    last_market_sale_seller_name, last_market_sale_seller_1_first_mid, last_market_sale_seller_1_last,
    last_market_sale_seller_1_code, last_market_sale_seller_2_first_mid, last_market_sale_seller_2_last
FROM 
    public.imported_data1;
