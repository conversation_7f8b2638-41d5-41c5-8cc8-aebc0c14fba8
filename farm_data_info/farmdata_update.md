# Farm Data Update
## _This shows how to update the farm data_


The processes listed below would walk you through downloading the farm data from lightbox to updating the already existing data with the new farm data.

- It is assumed you have familiarity with Python, SQL and Linux servers and commands
- ✨ <PERSON><PERSON>,  <PERSON><PERSON><PERSON> and <PERSON>paeb<PERSON> ✨

#### Building for source

1. Download the lightbox data from the lightbox website:
   https://sftp.lightboxre.com/Logon.aspx
2. Extract the downloaded data into a folder using the GUI or the zip command in linux.
3. Run the `lightbox.py` script to format the downloaded data and upload them into your local database.
    > this runs the ogr2ogr command to convert data between file formats; It can also perform various operations during the process, such as spatial or attribute selection. this gives you chance to check for anomalitites and other information.
4. Copy the sql query in `insertfromparcels.text` and run inside PgAdmin's Query Tool.
5. Export the `farm_parcel`(the name of the table given when uploading it with lightbox.py) table data to generate a csv file, say, `farm_data_update.csv`.
6. Split the generated csv file into parts with each having at most 100000 rows using the parallel command:
    
    ```bash
     cat farm_data_update.csv | parallel --header : --pipe -N100000 --block 5M 'cat >/home/<USER>/SPLITFILES/file_{#}.csv'
    ```
7. Run the `farm_script.py` script with the splitfiles directory as the `home_path`;
    > this script will load all the filepaths inside the splitfiles directory. for each row in the file, it would construct an insert command with the appropriate values. the load_insert_commands() function will then connect to the remote server database and run the individual insert commands.
8. Inside the PgAdmin QueryTool, run the following command to list how many rows needs to be updated. 
    > it is assumed that the taxapn is distinct for every property owner

    ```sql
        SELECT COUNT(*) AS num_records FROM farm_parcel fm 
        INNER JOIN farm_parcel_dup fs 
        ON fm.taxapn = fs.taxapn;
    ```
9. Run this command to update the old farm data with the new farm data
    > farm_parcel is the old table; farm_data_update is the new table

    ```sql
    UPDATE farm_parcel
    SET 
    owner_name = fs.owner_name,
    owner_name_1 = fs.owner_name_1,
    owner_name_2 = fs.owner_name_2,
    owner_1_first = fs.owner_1_first,
    owner_1_last = fs.owner_1_last,
    owner_2_first = fs.owner_2_first,
    owner_2_last = fs.owner_2_last,
    last_market_sale_seller_name = fs.last_market_sale_seller_name,
    last_market_sale_seller_1_first_mid = fs.last_market_sale_seller_1_first_mid,
    last_market_sale_seller_1_last = fs.last_market_sale_seller_1_last,
    last_market_sale_seller_1_code = fs.last_market_sale_seller_1_code,
    last_market_sale_seller_2_first_mid = fs.last_market_sale_seller_2_first_mid,
    last_market_sale_seller_2_last = fs.last_market_sale_seller_2_last,
    owner_occupied = fs.owner_occupied
    FROM 
    farm_data_1 fs
    WHERE 
    farm_parcel.taxapn = fs.taxapn;
    ```
**Rejoice, For you have achieved a great feat! Hell Yeah!!**

![snoop approves](http://www.reactiongifs.com/wp-content/uploads/2013/03/snoop.gif)
