INSERT INTO public.farm_parcel(
	 fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, site_county, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape)
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_alameda"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_alpine"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_amador"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_butte"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_calaveras"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_colusa"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_contracosta"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_delnorte"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_eldorado"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_fresno"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_glenn"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_humboldt"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_imperial"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_inyo"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_kern"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_kings"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_lake"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_lassen"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_losangeles"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_madera"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_marin"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_mariposa"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_mendocino"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_merced"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_modoc"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_mono"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_monterey"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_napa"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_nevada"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_orange"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_placer"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_plumas"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_riverside"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sacramento"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sanbenito"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sanbernardino"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sandiego"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sanfrancisco"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sanjoaquin"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sanluisobispo"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sanmateo"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_santabarbara"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_santaclara"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_santacruz"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_shasta"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sierra"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_siskiyou"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_solano"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sonoma"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_stanislaus"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_sutter"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_tehama"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_trinity"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_tulare"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_tuolumne"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_ventura"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_yolo"
UNION ALL
SELECT
fips_code, parcel_apn, taxapn, site_addr, site_house_number, site_direction, site_street_name, site_mode, site_carrier_code, site_quadrant, site_unit_prefix, site_unit_number, site_city, NULL, site_state, site_zip, site_plus_4, _x_coord, _y_coord, addr_score, owner_name, owner_name_1, owner_name_2, owner_1_first, owner_1_last, owner_2_first, owner_2_last, mail_addr, mail_house_number, mail_direction, mail_street_name, mail_mode, mail_quadrant, mail_city, mail_state, mail_zip, mail_plus_4, mail_unit_prefix, mail_unit_number, owner_occupied, use_code_muni, use_code_muni_desc, use_code_std_lps, use_code_std_desc_lps, use_code_std_ctgr_lps, use_code_std_ctgr_desc_lps, assessee_owner_name_1, assessee_owner_name_2, asmt_val_transfer, zoning, lot_size_area, lot_size_area_unit, lot_size_area_orgn, yr_blt, building_sqft, stories_number, units_number, bedrooms, total_baths, garage_carport_type, garage_code_desc, parking_spaces, pool_indicator, construction_code_desc, price_per_sqft, last_sale_date_transfer, last_market_sale_date_transfer, last_market_sale_val_transfer, prior_sale_date_transfer, assessee_owner_2_indicator, assessee_owner_1_name_type, assessee_owner_2_name_type, yr_blt_effect, lot_width, lot_depth, location_id, address_id, parcel_dmp_id, shape
FROM
public."parcels_w_attributes_ca_yuba"
;
