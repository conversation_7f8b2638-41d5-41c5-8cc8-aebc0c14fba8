"""
#<PERSON><PERSON><PERSON><PERSON> write a brief description about this script
This scripts ...

# USE THE LINUX COMMAND TO count the rows in all the file
# wc -l *

This command splits the big farm data file into files consisting of 100000 rows.
# cat farm_data_duplicated.csv | parallel --header : --pipe -N100000 --block 5M
'cat >/home/<USER>/SPLITFILES/file_{#}.csv'
"""
import sys
import psycopg2
from psycopg2 import OperationalError, IntegrityError
import csv
from os import listdir
from os.path import isfile, join

# AWS credentials
aws_access_key_id = "aws_access_key_id"
aws_secret_access_key = "aws_secret_access_key"

# S3 bucket and file information
# This is the name of the bucket where the data is stored.
s3_bucket_name = "bpooperationsmain"
s3_file_key = "lightbox_farm_data/copyfarm.csv"

# RDS database information
# rds_host = "rds_host"
# rds_port = 5432
# rds_db_name = "rds_db_name"
# rds_user = "rds_user"
# rds_password = "rds_password"

rds_host = "bpohomes-postgis-prod.chhgno8yo6f1.us-west-1.rds.amazonaws.com"
rds_port = 5432
rds_db_name = "bpohomes"
rds_user = "prod_admin"
rds_password = "EraB7ASh69YSRdKQdO3Y77y79gVfSxyLAURTzQOF"


print("SET ALL REQUIRED ATTRIBUTES")


# This is a way to increase the size of the csv file that can be read.
maxInt = sys.maxsize
while True:
    # decrease the maxInt value by factor 10
    # as long as the OverflowError occurs.

    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt / 10)

# home_path = "/your_home_directory/splitfiles_directory"
home_path = "/home/<USER>/Desktop/farm/SPLITFILES"


def load_filepaths(mypath):
    """
    This function takes a path to a directory and returns a list of all the filepaths in that directory

    :param mypath: the path to the directory containing the images
    """
    onlyfiles = [f for f in listdir(mypath) if isfile(join(mypath, f))]
    return onlyfiles


import csv

def load_commands(file):
    """
    It loads commands from a file and prints out fields that cause issues during the execution.
    
    :param file: The file to load the commands from
    """
    with open(file, "r") as csvfile:
        reader = csv.DictReader(csvfile)
        commands = []

        columns = [
            "id", "fips_code", "parcel_apn", "taxapn", "site_addr", "site_house_number", 
            "site_direction", "site_street_name", "site_mode", "site_carrier_code", 
            "site_quadrant", "site_unit_prefix", "site_unit_number", "site_city", 
            "site_county", "site_state", "site_zip", "site_plus_4", "_x_coord", 
            "_y_coord", "addr_score", "owner_name", "owner_name_1", "owner_name_2", 
            "owner_1_first", "owner_1_last", "owner_2_first", "owner_2_last", 
            "mail_addr", "mail_house_number", "mail_direction", "mail_street_name", 
            "mail_mode", "mail_quadrant", "mail_city", "mail_state", "mail_zip", 
            "mail_plus_4", "mail_unit_prefix", "mail_unit_number", "owner_occupied", 
            "use_code_muni", "use_code_muni_desc", "use_code_std_lps", 
            "use_code_std_desc_lps", "use_code_std_ctgr_lps", 
            "use_code_std_ctgr_desc_lps", "assessee_owner_name_1", 
            "assessee_owner_name_2", "asmt_val_transfer", "zoning", "lot_size_area", 
            "lot_size_area_unit", "lot_size_area_orgn", "yr_blt", "building_sqft", 
            "stories_number", "units_number", "bedrooms", "total_baths", 
            "garage_carport_type", "garage_code_desc", "parking_spaces", 
            "pool_indicator", "construction_code_desc", "price_per_sqft", 
            "last_sale_date_transfer", "last_market_sale_date_transfer", 
            "last_market_sale_val_transfer", "prior_sale_date_transfer", 
            "assessee_owner_2_indicator", "assessee_owner_1_name_type", 
            "assessee_owner_2_name_type", "yr_blt_effect", "lot_width", 
            "lot_depth", "location_id", "address_id", "parcel_dmp_id", "shape", 
            "territory_id", "last_market_sale_seller_name", 
            "last_market_sale_seller_1_first_mid", "last_market_sale_seller_1_last", 
            "last_market_sale_seller_1_code", "last_market_sale_seller_2_first_mid", 
            "last_market_sale_seller_2_last"
        ]

        for row in reader:
            values = []
            for col in columns:
                value = row.get(col, None)
                if value == "" or value is None:
                    values.append(None)  # Assign None (NULL in the database)
                else:
                    # Convert integer fields when necessary
                    if col in ["id", "fips_code", "parcel_apn", "building_sqft", "stories_number", "units_number", "bedrooms", "total_baths", "price_per_sqft", "lot_width", "lot_depth"]:
                        try:
                            values.append(value)  # Convert to integer
                        except ValueError:
                            values.append(None)  # If conversion fails, append None
                    else:
                        values.append(value)

            # Generate the SQL insert command
            command = (
                f"INSERT INTO farm_data_1 ({', '.join(columns)}) "
                f"VALUES ({', '.join(['%s'] * len(values))})"
            )
            
            # Try to execute the command and catch any errors
            try:
                commands.append((command, values))
            except Exception as e:
                # Print the specific error, field, and value causing the issue
                for i, col in enumerate(columns):
                    value = values[i]
                    if value and isinstance(value, str) and len(value) > 50:
                        print(f"Error: Value '{value}' in field '{col}' exceeds maximum length of 50 characters.")
                # Re-raise the exception after logging it
                raise

    return commands


    
def load_insert_commands(commands, file):
    """
    This function takes a list of commands and a file name, and inserts the commands into the database.
    If a duplicate key error occurs, it skips that command and continues with the next.
    """
    # Try to connect to the RDS database
    try:
        conn = psycopg2.connect(
            host=rds_host,
            port=rds_port,
            database=rds_db_name,
            user=rds_user,
            password=rds_password,
        )
        print("Successfully connected to the RDS database.")
    except OperationalError as e:
        print(f"Error connecting to the database: {e}")
        return  # Exit the function if the connection fails

    # Proceed with the insert commands if the connection is successful
    try:
        for command in commands:
            com, val = command
            cursor = conn.cursor()
            try:
                cursor.execute(com, val)
                conn.commit()
                print(f"Added ID: {val[0]}")
            except IntegrityError as err:
                if "duplicate key value violates unique constraint" in str(err):
                    print(f"Skipping record with duplicate ID: {val[0]}")
                    conn.rollback()  # Rollback the transaction for this specific command
                else:
                    print(f"Exception Error while executing command: {err}")
                    conn.rollback()  # Rollback if any other error occurs
            except Exception as err:
                print(f"General error while executing command: {err}")
                conn.rollback()
            finally:
                cursor.close()  # Ensure cursor is always closed after each iteration

    except Exception as e:
        print(f"Error in processing commands: {e}")

    finally:
        conn.close()  # Ensure the connection is closed after all commands are processed
        print("Connection closed.")

    return


def main():
    """
    It loads the filepaths, then for each filepath, it loads the commands, then it loads the insert
    commands
    :return: A list of filepaths
    """
    # load filepaths
    filepaths = load_filepaths(home_path)
    print(filepaths)
    for file in filepaths:
        print(file)
        correct_path = join(home_path, file)
        print(f"START WITH: {correct_path}")
        commands = load_commands(correct_path)
        print("LOADING COMMANDS DONE!")
        load_insert_commands(commands, file)
        print(f"END WITH {correct_path}")
        sys.exit()
    return filepaths


# This is a way to run the main function only when you run the script directly.
if __name__ == "__main__":
    main()
    print("MAIN FUNCTION EXITED")

sys.exit("FINISHED INSERTING")
