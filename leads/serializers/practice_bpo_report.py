from rest_framework import serializers

from leads.models import (
    PracticeClientBpoRequest,
    PracticeComparableActiveListings,
    PracticeComparableClosedSales,
    PracticeExteriorPhoto,
    PracticeInteriorPhoto,
)
from register.serializers import ClientRetrieveSerializer, AgentRetrieveLeadsSerializer
from register.models import Agent


class PracticeExteriorPhotoSerializer(serializers.ModelSerializer):
    class Meta:
        model = PracticeExteriorPhoto
        fields = "__all__"


class PracticeInteriorPhotoSerializer(serializers.ModelSerializer):
    class Meta:
        model = PracticeInteriorPhoto
        fields = "__all__"


class PracticeComparableSerializer(serializers.ModelSerializer):
    class Meta:
        model = PracticeComparableActiveListings
        fields = [
            "id",
            "address",
            "sales_price",
            "price_per_gross_living_area",
            "description",
            "lot_size",
            "condition",
            "num_bathrooms",
            "num_bedrooms",
            "gross_living_area",
            "days_on_market",
            "home_style_value",
            "home_style_feedback",
            "feng_shui_value",
            "feng_shui_feedback",
            "proximity_to_amenities_value",
            "proximity_to_amenities_feedback",
            "proximity_neighborhood_negative_value",
            "proximity_neighborhood_negative_feedback",
            "lot_size_difference_amount",
            "garage_difference_amount",
            "livable_square_footage_difference_amount",
            "garage",
            "images",
        ]

    def create(self, validated_data):
        instance = super(PracticeComparableSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(PracticeComparableSerializer, self).update(instance, validated_data)
        return instance


class PracticeComparableClosedSerializer(serializers.ModelSerializer):
    class Meta:
        model = PracticeComparableClosedSales
        fields = [
            "id",
            "address",
            "sales_price",
            "price_per_gross_living_area",
            "description",
            "lot_size",
            "condition",
            "num_bathrooms",
            "num_bedrooms",
            "gross_living_area",
            "sale_date",
            "home_style_value",
            "home_style_feedback",
            "feng_shui_value",
            "feng_shui_feedback",
            "proximity_to_amenities_value",
            "proximity_to_amenities_feedback",
            "proximity_neighborhood_negative_value",
            "proximity_neighborhood_negative_feedback",
            "lot_size_difference_amount",
            "garage_difference_amount",
            "livable_square_footage_difference_amount",
            "garage",
            "images",
        ]

    def create(self, validated_data):
        instance = super(PracticeComparableSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(PracticeComparableSerializer, self).update(instance, validated_data)
        return instance


class PracticeCheckReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = PracticeClientBpoRequest
        fields = [
            "id",
            "clients_physical_address",
        ]


class PracticeCreateReportSerializer(serializers.ModelSerializer):
    client_register_id = serializers.IntegerField(required=False)

    class Meta:
        model = PracticeClientBpoRequest
        fields = [
            "id",
            "reason_for_request",
            "payment_status",
            "clients_physical_address",
            "client_register_id",
            "from_agent_profile",
            "report_status",
            "agent_id",
            "client_id",
            "num_of_active_listings",
            "active_listings_period",
            "current_market_value",
            "current_suggested_list_price",
            "repaired_market_value",
            "repaired_suggested_list_price",
            "final_summary",
            "brokerage",
            "name",
            "firm",
            "agent",
            "email",
            "phone",
            "number",
            "completed_by",
            "current_market_conditions",
            "subject_impact",
            "status",
            "unit_type",
            "lead_type",
            "market_price_change_percentage",
            "market_price_change_months",
            "neighborhood_price_upper",
            "neighborhood_price_lower",
            "marketing_time",
            "num_of_sold_listings",
            "sold_listings_time",
            "date_signed",
            "signature",
            "is_lead_signed",
            "property_condition",
            "last_time_remodeled",
            "is_home_new",
            "nearby_nuisances",
            "customer_lot_size",
            "customer_garage",
            "customer_livable_square_footage",
            "is_property_remodelled",
            "is_year",
            "drive_through_address",
            "amount_for_construction_area",
        ]

    def to_internal_value(self, data):
        first_name = data.get("first_name", None)
        last_name = data.get("last_name", None)
        reason_for_selling = data.get("reason_for_selling", None)
        other_information = data.get("other_information", None)
        property_address = data.get("property_address", None)
        data["name"] = first_name + " " + last_name

        data["final_summary"] = f"reason for selling: {reason_for_selling}, other information: {other_information}"

        data["clients_physical_address"] = property_address

        return super().to_internal_value(data)


class PracticePatchReportSerializer(serializers.ModelSerializer):
    client_register = ClientRetrieveSerializer(read_only=True)
    client_register_id = serializers.IntegerField(required=False)
    practice_comparable_active_listings = PracticeComparableSerializer(many=True, required=False)
    practice_comparable_closed_listings = PracticeComparableClosedSerializer(many=True, required=False)
    interior_photos = serializers.SerializerMethodField(required=False)
    exterior_photos = serializers.SerializerMethodField(required=False)

    class Meta:
        model = PracticeClientBpoRequest
        fields = [
            "id",
            "reason_for_request",
            "payment_status",
            "clients_physical_address",
            "client_register_id",
            "from_agent_profile",
            "report_status",
            "agent_id",
            "client_id",
            "client_register",
            "num_of_active_listings",
            "active_listings_period",
            "current_market_value",
            "current_suggested_list_price",
            "repaired_market_value",
            "repaired_suggested_list_price",
            "final_summary",
            "brokerage",
            "name",
            "firm",
            "agent",
            "phone",
            "number",
            "completed_by",
            "current_market_conditions",
            "subject_impact",
            "status",
            "unit_type",
            "lead_type",
            "market_price_change_percentage",
            "market_price_change_months",
            "neighborhood_price_upper",
            "neighborhood_price_lower",
            "marketing_time",
            "num_of_sold_listings",
            "sold_listings_time",
            "practice_comparable_active_listings",
            "practice_comparable_closed_listings",
            "date_created",
            "updated_at",
            "date_signed",
            "signature",
            "is_lead_signed",
            "property_condition",
            "last_time_remodeled",
            "is_home_new",
            "nearby_nuisances",
            "interior_photos",
            "exterior_photos",
            "customer_lot_size",
            "customer_garage",
            "customer_livable_square_footage",
            "is_property_remodelled",
            "is_year",
            "drive_through_address",
            "amount_for_construction_area",
        ]

    def update(self, instance, validated_data):
        instance.reason_for_request = validated_data.get(
            "reason_for_request", instance.reason_for_request
        )
        instance.payment_status = validated_data.get(
            "payment_status", instance.payment_status
        )
        instance.clients_physical_address = validated_data.get(
            "clients_physical_address", instance.clients_physical_address
        )
        instance.client_register_id = validated_data.get(
            "client_register_id", instance.client_register_id
        )
        instance.from_agent_profile = validated_data.get(
            "from_agent_profile", instance.from_agent_profile
        )
        instance.report_status = validated_data.get(
            "report_status", instance.report_status
        )
        instance.agent_id = validated_data.get("agent_id", instance.agent_id)
        instance.client_id = validated_data.get("client_id", instance.client_id)
        instance.num_of_active_listings = validated_data.get(
            "num_of_active_listings", instance.num_of_active_listings
        )
        instance.active_listings_period = validated_data.get(
            "active_listings_period", instance.active_listings_period
        )
        instance.current_market_value = validated_data.get(
            "current_market_value", instance.current_market_value
        )
        instance.current_suggested_list_price = validated_data.get(
            "current_suggested_list_price", instance.current_suggested_list_price
        )
        instance.repaired_market_value = validated_data.get(
            "repaired_market_value", instance.repaired_market_value
        )
        instance.repaired_suggested_list_price = validated_data.get(
            "repaired_suggested_list_price", instance.repaired_suggested_list_price
        )
        instance.final_summary = validated_data.get(
            "final_summary", instance.final_summary
        )
        instance.brokerage = validated_data.get("brokerage", instance.brokerage)
        instance.name = validated_data.get("name", instance.name)
        instance.firm = validated_data.get("firm", instance.firm)
        instance.agent = validated_data.get("agent", instance.agent)
        instance.phone = validated_data.get("phone", instance.phone)
        instance.number = validated_data.get("number", instance.number)
        instance.completed_by = validated_data.get(
            "completed_by", instance.completed_by
        )
        instance.current_market_conditions = validated_data.get(
            "current_market_conditions", instance.current_market_conditions
        )
        instance.subject_impact = validated_data.get(
            "subject_impact", instance.subject_impact
        )
        instance.status = validated_data.get("status", instance.status)
        instance.unit_type = validated_data.get("unit_type", instance.unit_type)
        instance.lead_type = validated_data.get("lead_type", instance.lead_type)
        instance.market_price_change_percentage = validated_data.get(
            "market_price_change_percentage", instance.market_price_change_percentage
        )
        instance.market_price_change_months = validated_data.get(
            "market_price_change_months", instance.market_price_change_months
        )
        instance.neighborhood_price_upper = validated_data.get(
            "neighborhood_price_upper", instance.neighborhood_price_upper
        )
        instance.neighborhood_price_lower = validated_data.get(
            "neighborhood_price_lower", instance.neighborhood_price_lower
        )
        instance.marketing_time = validated_data.get(
            "marketing_time", instance.marketing_time
        )
        instance.num_of_sold_listings = validated_data.get(
            "num_of_sold_listings", instance.num_of_sold_listings
        )
        instance.sold_listings_time = validated_data.get(
            "sold_listings_time", instance.sold_listings_time
        )
        instance.date_signed = validated_data.get("date_signed", instance.date_signed)
        instance.signature = validated_data.get("signature", instance.signature)
        instance.is_lead_signed = validated_data.get(
            "is_lead_signed", instance.is_lead_signed
        )
        instance.property_condition = validated_data.get(
            "property_condition", instance.property_condition
        )
        instance.last_time_remodeled = validated_data.get(
            "last_time_remodeled", instance.last_time_remodeled
        )
        instance.is_home_new = validated_data.get("is_home_new", instance.is_home_new)
        instance.nearby_nuisances = validated_data.get(
            "nearby_nuisances", instance.nearby_nuisances
        )
        instance.customer_lot_size = validated_data.get(
            "customer_lot_size", instance.customer_lot_size
        )
        instance.customer_garage = validated_data.get(
            "customer_garage", instance.customer_garage
        )
        instance.is_property_remodelled = validated_data.get(
            "is_property_remodelled", instance.is_property_remodelled
        )
        instance.customer_livable_square_footage = validated_data.get(
            "customer_livable_square_footage", instance.customer_livable_square_footage
        )

        instance.drive_through_address = validated_data.get(
            "drive_through_address", instance.drive_through_address
        )
        instance.is_year = validated_data.get("is_year", instance.is_year)
        instance.amount_for_construction_area = validated_data.get(
            "amount_for_construction_area", instance.amount_for_construction_area
        )

        try:
            interior_photos = self.initial_data.getlist("interior_photos")
            print("testing")
            print(interior_photos)

            print("now looping through")
            for interior in interior_photos:
                PracticeInteriorPhoto.objects.create(
                    listings_id=instance.id, image=interior
                )
        except Exception:
            print("did  not work")

        try:
            exterior_photos = self.initial_data.getlist("exterior_photos")
            for exterior in exterior_photos:
                try:
                    PracticeExteriorPhoto.objects.create(
                        listings_id=instance.id, image=exterior
                    )
                except Exception:
                    pass
        except Exception:
            pass

        instance.save()
        return instance

    def get_interior_photos(self, obj):
        try:
            get_interior = PracticeInteriorPhoto.objects.filter(listings_id=obj.id)
            serializer = PracticeExteriorPhotoSerializer(get_interior, many=True)

            return serializer.data
        except Exception:
            return None

    def get_exterior_photos(self, obj):
        try:
            get_exterior = PracticeExteriorPhoto.objects.filter(listings_id=obj.id)
            serializer = PracticeExteriorPhotoSerializer(get_exterior, many=True)
            return serializer.data
        except Exception:
            return None


class PracticeRetrieveReportSerializer(serializers.ModelSerializer):
    client_register = ClientRetrieveSerializer(read_only=True)
    practice_comparable_active_listings = PracticeComparableSerializer(many=True, required=False)
    practice_comparable_closed_listings = PracticeComparableClosedSerializer(many=True, required=False)
    interior_photos = serializers.SerializerMethodField(required=False)
    exterior_photos = serializers.SerializerMethodField(required=False)
    agent = serializers.SerializerMethodField(required=False)

    class Meta:
        model = PracticeClientBpoRequest
        fields = [
            "id",
            "reason_for_request",
            "payment_status",
            "clients_physical_address",
            "client_register_id",
            "from_agent_profile",
            "report_status",
            "agent_id",
            "client_id",
            "client_register",
            "practice_comparable_active_listings",
            "practice_comparable_closed_listings",
            "num_of_active_listings",
            "active_listings_period",
            "current_market_value",
            "current_suggested_list_price",
            "repaired_market_value",
            "repaired_suggested_list_price",
            "final_summary",
            "brokerage",
            "name",
            "firm",
            "agent",
            "phone",
            "number",
            "completed_by",
            "current_market_conditions",
            "subject_impact",
            "status",
            "unit_type",
            "lead_type",
            "market_price_change_percentage",
            "market_price_change_months",
            "neighborhood_price_upper",
            "neighborhood_price_lower",
            "marketing_time",
            "num_of_sold_listings",
            "sold_listings_time",
            "date_created",
            "updated_at",
            "date_signed",
            "signature",
            "is_lead_signed",
            "property_condition",
            "last_time_remodeled",
            "is_home_new",
            "nearby_nuisances",
            "interior_photos",
            "exterior_photos",
            "customer_lot_size",
            "customer_garage",
            "customer_livable_square_footage",
            "is_property_remodelled",
            "is_year",
            "drive_through_address",
            "amount_for_construction_area",
        ]

    def get_interior_photos(self, obj):
        try:
            get_interior = PracticeInteriorPhoto.objects.filter(listings_id=obj.id)
            serializer = PracticeExteriorPhotoSerializer(get_interior, many=True)

            return serializer.data
        except Exception:
            return None

    def get_agent(self, obj):
        try:
            get_agent = Agent.objects.get(id=obj.agent_id)
            serializer = AgentRetrieveLeadsSerializer(get_agent, many=False)

            return serializer.data
        except Exception as e:
            print(e)
            return None

    def get_exterior_photos(self, obj):
        try:
            get_exterior = PracticeExteriorPhoto.objects.filter(listings_id=obj.id)
            serializer = PracticeExteriorPhotoSerializer(get_exterior, many=True)
            return serializer.data
        except Exception:
            return None

    def update(self, instance, validated_data):
        instance.reason_for_request = validated_data.get(
            "reason_for_request", instance.reason_for_request
        )
        instance.payment_status = validated_data.get(
            "payment_status", instance.payment_status
        )
        instance.clients_physical_address = validated_data.get(
            "clients_physical_address", instance.clients_physical_address
        )
        instance.from_agent_profile = validated_data.get(
            "from_agent_profile", instance.from_agent_profile
        )
        instance.report_status = validated_data.get(
            "report_status", instance.report_status
        )
        instance.agent_id = validated_data.get("agent_id", instance.agent_id)
        instance.client_id = validated_data.get("client_id", instance.client_id)
        instance.num_of_active_listings = validated_data.get(
            "num_of_active_listings", instance.num_of_active_listings
        )
        instance.active_listings_period = validated_data.get(
            "active_listings_period", instance.active_listings_period
        )
        instance.current_market_value = validated_data.get(
            "current_market_value", instance.current_market_value
        )
        instance.current_suggested_list_price = validated_data.get(
            "current_suggested_list_price", instance.current_suggested_list_price
        )
        instance.repaired_market_value = validated_data.get(
            "repaired_market_value", instance.repaired_market_value
        )
        instance.repaired_suggested_list_price = validated_data.get(
            "repaired_suggested_list_price", instance.repaired_suggested_list_price
        )
        instance.final_summary = validated_data.get(
            "final_summary", instance.final_summary
        )
        instance.brokerage = validated_data.get("brokerage", instance.brokerage)
        instance.name = validated_data.get("name", instance.name)
        instance.firm = validated_data.get("firm", instance.firm)
        instance.agent = validated_data.get("agent", instance.agent)
        instance.phone = validated_data.get("phone", instance.phone)
        instance.number = validated_data.get("number", instance.number)
        instance.completed_by = validated_data.get(
            "completed_by", instance.completed_by
        )
        instance.current_market_conditions = validated_data.get(
            "current_market_conditions", instance.current_market_conditions
        )
        instance.subject_impact = validated_data.get(
            "subject_impact", instance.subject_impact
        )
        instance.status = validated_data.get("status", instance.status)
        instance.unit_type = validated_data.get("unit_type", instance.unit_type)
        instance.subject_impact = validated_data.get(
            "subject_impact", instance.market_price_change_percentage
        )
        instance.market_price_change_months = validated_data.get(
            "market_price_change_months", instance.market_price_change_months
        )
        instance.neighborhood_price_upper = validated_data.get(
            "neighborhood_price_upper", instance.neighborhood_price_upper
        )
        instance.neighborhood_price_lower = validated_data.get(
            "neighborhood_price_lower", instance.neighborhood_price_lower
        )
        instance.marketing_time = validated_data.get(
            "marketing_time", instance.marketing_time
        )
        instance.num_of_sold_listings = validated_data.get(
            "num_of_sold_listings", instance.num_of_sold_listings
        )
        instance.sold_listings_time = validated_data.get(
            "sold_listings_time", instance.sold_listings_time
        )
        instance.property_condition = validated_data.get(
            "property_condition", instance.property_condition
        )
        instance.last_time_remodeled = validated_data.get(
            "last_time_remodeled", instance.last_time_remodeled
        )
        instance.is_home_new = validated_data.get("is_home_new", instance.is_home_new)
        instance.nearby_nuisances = validated_data.get(
            "nearby_nuisances", instance.nearby_nuisances
        )
        instance.customer_lot_size = validated_data.get(
            "customer_lot_size", instance.customer_lot_size
        )
        instance.customer_garage = validated_data.get(
            "customer_garage", instance.customer_garage
        )
        instance.is_property_remodelled = validated_data.get(
            "is_property_remodelled", instance.is_property_remodelled
        )
        instance.customer_livable_square_footage = validated_data.get(
            "customer_livable_square_footage", instance.customer_livable_square_footage
        )

        instance.drive_through_address = validated_data.get(
            "drive_through_address", instance.drive_through_address
        )
        instance.is_year = validated_data.get("is_year", instance.is_year)
        instance.amount_for_construction_area = validated_data.get(
            "amount_for_construction_area", instance.amount_for_construction_area
        )
        instance.save()

        practice_comparable_active_listings = self.context["comparable_active"]

        practice_comparable_closed_listings = self.context["comparable_closed"]

        get_updated_comparable_active_listings_id = [
            item.get("id", 0) for item in practice_comparable_active_listings
        ]
        get_allready_existing_comparable_active_listings_id = [
            item.id
            for item in PracticeComparableActiveListings.objects.filter(
                practice_comparable_active_listings=instance
            )
        ]

        get_updated_comparable_closed_listings_id = [
            item.get("id", 0) for item in practice_comparable_closed_listings
        ]
        get_allready_existing_comparable_closed_listings_id = [
            item.id
            for item in PracticeComparableClosedSales.objects.filter(
                practice_comparable_closed_listings=instance
            )
        ]

        for active in practice_comparable_active_listings:
            try:
                if active["id"] in get_updated_comparable_active_listings_id:
                    comparable = PracticeComparableActiveListings.objects.update_or_create(
                        id=active["id"],
                        defaults={
                            "address": active["address"],
                            "lot_size": active["lot_size"],
                            "sales_price": active["sales_price"],
                            "price_per_gross_living_area": active[
                                "price_per_gross_living_area"
                            ],
                            "description": active["description"],
                            "condition": active["condition"],
                            "gross_living_area": active["gross_living_area"],
                            "num_bedrooms": active["num_bedrooms"],
                            "num_bathrooms": active["num_bathrooms"],
                            "days_on_market": active["days_on_market"],
                            "home_style_value": active["home_style_value"],
                            "home_style_feedback": active["home_style_feedback"],
                            "feng_shui_value": active["feng_shui_value"],
                            "feng_shui_feedback": active["feng_shui_feedback"],
                            "proximity_to_amenities_value": active[
                                "proximity_to_amenities_value"
                            ],
                            "proximity_to_amenities_feedback": active[
                                "proximity_to_amenities_feedback"
                            ],
                            "proximity_neighborhood_negative_value": active[
                                "proximity_neighborhood_negative_value"
                            ],
                            "proximity_neighborhood_negative_feedback": active[
                                "proximity_neighborhood_negative_feedback"
                            ],
                            "lot_size_difference_amount": active[
                                "lot_size_difference_amount"
                            ],
                            "garage_difference_amount": active[
                                "garage_difference_amount"
                            ],
                            "livable_square_footage_difference_amount": active[
                                "livable_square_footage_difference_amount"
                            ],
                            "garage": active["garage"],
                            "images": active["images"],
                        },
                    )
                    get_allready_existing_comparable_active_listings_id.remove(
                        active["id"]
                    )
                else:
                    get_active = PracticeComparableActiveListings.objects.get(id=active["id"])
                    get_active.delete()

            except Exception:
                comparable = PracticeComparableActiveListings.objects.create(
                    address=active["address"],
                    lot_size=active["lot_size"],
                    sales_price=active["sales_price"],
                    price_per_gross_living_area=active["price_per_gross_living_area"],
                    description=active["description"],
                    condition=active["condition"],
                    gross_living_area=active["gross_living_area"],
                    num_bathrooms=active["num_bathrooms"],
                    num_bedrooms=active["num_bedrooms"],
                    days_on_market=active["days_on_market"],
                    home_style_value=active["home_style_value"],
                    home_style_feedback=active["home_style_feedback"],
                    feng_shui_value=active["feng_shui_value"],
                    feng_shui_feedback=active["feng_shui_feedback"],
                    proximity_to_amenities_value=active["proximity_to_amenities_value"],
                    proximity_to_amenities_feedback=active[
                        "proximity_to_amenities_feedback"
                    ],
                    proximity_neighborhood_negative_value=active[
                        "proximity_neighborhood_negative_value"
                    ],
                    proximity_neighborhood_negative_feedback=active[
                        "proximity_neighborhood_negative_feedback"
                    ],
                    lot_size_difference_amount=active["lot_size_difference_amount"],
                    garage_difference_amount=active["garage_difference_amount"],
                    livable_square_footage_difference_amount=active[
                        "livable_square_footage_difference_amount"
                    ],
                    garage=active["garage"],
                    images=active["images"],
                    practice_comparable_active_listings=instance,
                )
                try:
                    get_allready_existing_comparable_active_listings_id.remove(
                        comparable.id
                    )
                except Exception:
                    pass

        for closed in practice_comparable_closed_listings:
            try:
                if closed["id"] in get_updated_comparable_closed_listings_id:
                    comparable = PracticeComparableClosedSales.objects.update_or_create(
                        id=closed["id"],
                        defaults={
                            "address": closed["address"],
                            "lot_size": closed["lot_size"],
                            "sales_price": closed["sales_price"],
                            "price_per_gross_living_area": closed[
                                "price_per_gross_living_area"
                            ],
                            "description": closed["description"],
                            "condition": closed["condition"],
                            "gross_living_area": closed["gross_living_area"],
                            "num_bedrooms": closed["num_bedrooms"],
                            "num_bathrooms": closed["num_bathrooms"],
                            "sale_date": closed["sale_date"],
                            "home_style_value": closed["home_style_value"],
                            "home_style_feedback": closed["home_style_feedback"],
                            "feng_shui_value": closed["feng_shui_value"],
                            "feng_shui_feedback": closed["feng_shui_feedback"],
                            "proximity_to_amenities_value": closed[
                                "proximity_to_amenities_value"
                            ],
                            "proximity_to_amenities_feedback": closed[
                                "proximity_to_amenities_feedback"
                            ],
                            "proximity_neighborhood_negative_value": closed[
                                "proximity_neighborhood_negative_value"
                            ],
                            "proximity_neighborhood_negative_feedback": closed[
                                "proximity_neighborhood_negative_feedback"
                            ],
                            "lot_size_difference_amount": closed[
                                "lot_size_difference_amount"
                            ],
                            "garage_difference_amount": closed[
                                "garage_difference_amount"
                            ],
                            "livable_square_footage_difference_amount": closed[
                                "livable_square_footage_difference_amount"
                            ],
                            "garage": closed["garage"],
                            "images": closed["images"],
                        },
                    )
                    get_allready_existing_comparable_closed_listings_id.remove(
                        active["id"]
                    )

                else:
                    get_closed = PracticeComparableClosedSales.objects.get(id=active["id"])
                    get_closed.delete()
            except Exception:
                comparable = PracticeComparableClosedSales.objects.create(
                    address=closed["address"],
                    lot_size=closed["lot_size"],
                    sales_price=closed["sales_price"],
                    price_per_gross_living_area=closed["price_per_gross_living_area"],
                    description=closed["description"],
                    condition=closed["condition"],
                    gross_living_area=closed["gross_living_area"],
                    num_bedrooms=closed["num_bedrooms"],
                    num_bathrooms=closed["num_bathrooms"],
                    sale_date=closed["sale_date"],
                    home_style_value=closed["home_style_value"],
                    home_style_feedback=closed["home_style_feedback"],
                    feng_shui_value=closed["feng_shui_value"],
                    feng_shui_feedback=closed["feng_shui_feedback"],
                    proximity_to_amenities_value=closed["proximity_to_amenities_value"],
                    proximity_to_amenities_feedback=closed[
                        "proximity_to_amenities_feedback"
                    ],
                    proximity_neighborhood_negative_value=closed[
                        "proximity_neighborhood_negative_value"
                    ],
                    proximity_neighborhood_negative_feedback=closed[
                        "proximity_neighborhood_negative_feedback"
                    ],
                    lot_size_difference_amount=closed["lot_size_difference_amount"],
                    garage_difference_amount=closed["garage_difference_amount"],
                    livable_square_footage_difference_amount=closed[
                        "livable_square_footage_difference_amount"
                    ],
                    garage=closed["garage"],
                    images=closed["images"],
                    practice_comparable_closed_listings=instance,
                )
                try:
                    get_allready_existing_comparable_closed_listings_id.remove(
                        comparable.id
                    )
                except Exception:
                    pass

        for closed in get_allready_existing_comparable_closed_listings_id:
            get_closed = PracticeComparableClosedSales.objects.get(id=closed)
            get_closed.delete()

        for active in get_allready_existing_comparable_active_listings_id:
            get_active = PracticeComparableActiveListings.objects.get(id=active)
            get_active.delete()
        return instance
