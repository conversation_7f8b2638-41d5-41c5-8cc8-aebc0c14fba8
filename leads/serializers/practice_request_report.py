from rest_framework import serializers
from contract.models import Contract
from leads.models import (
    PracticeSellerReportRequest,
    PracticeBuyerReportRequest,
)
from register.models.agents import Agent
from register.serializers.agents import AgentSerializer


class PracticeSellerBPORequestSerializer(serializers.ModelSerializer):
    can_view = serializers.SerializerMethodField()
    agent = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PracticeSellerReportRequest
        fields = [
            "id",
            "agent",
            "can_view",
            "property_address",
            "schedule_type",
            "meeting_date",
            "meeting_time",
            "sale_schedule",
            "property_state",
            "reason_for_selling",
            "working_with_an_agent",
            "look_forward_to_buying",
            "home_improvements",
            "first_name",
            "last_name",
            "email",
            "phone",
            "lead_type",
            "is_lead_signed",
            "other_information",
            "date_created", "signature",
            "address_link",
            "custom_id",
            "created_at",
            "updated_at",
            "date",
        ]

    def get_agent(self, obj):
        try:
            get_client = Agent.objects.get(id=obj.agent.id)
            serializer = AgentSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None

    def get_can_view(self, obj):
        try:
            request = self.context.get("request", None)
            user = request.user
        except Exception:
            user = self.context.get("user", None)

        try:
            Contract.objects.get(lead=obj, user=user)
        except Exception:
            return False

        return True

    def create(self, validated_data):
        leads = PracticeSellerReportRequest.objects.create(**validated_data)

        return leads

    def update(self, instance, validated_data):
        agent_id = self.context.get("agent_id")
        if agent_id is not None:
            instance.agent_id = agent_id

        instance.property_address = validated_data.get(
            "property_address", instance.property_address
        )
        instance.schedule_type = validated_data.get(
            "schedule_type", instance.schedule_type
        )
        instance.meeting_date = validated_data.get(
            "meeting_date", instance.meeting_date
        )
        instance.meeting_time = validated_data.get(
            "meeting_time", instance.meeting_time
        )
        instance.sale_schedule = validated_data.get(
            "sale_schedule", instance.sale_schedule
        )
        instance.property_state = validated_data.get(
            "property_state", instance.property_state
        )
        instance.reason_for_selling = validated_data.get(
            "reason_for_selling", instance.reason_for_selling
        )
        instance.working_with_an_agent = validated_data.get(
            "working_with_an_agent", instance.working_with_an_agent
        )
        instance.look_forward_to_buying = validated_data.get(
            "look_forward_to_buying", instance.look_forward_to_buying
        )
        instance.home_improvements = validated_data.get(
            "home_improvements", instance.home_improvements
        )
        instance.first_name = validated_data.get("first_name", instance.first_name)
        instance.last_name = validated_data.get("last_name", instance.last_name)
        instance.email = validated_data.get("email", instance.email)
        instance.phone = validated_data.get("phone", instance.phone)
        instance.lead_type = validated_data.get("lead_type", instance.lead_type)
        instance.is_lead_signed = validated_data.get(
            "is_lead_signed", instance.is_lead_signed
        )
        instance.other_information = validated_data.get(
            "other_information", instance.other_information
        )
        instance.date_created = validated_data.get(
            "date_created", instance.date_created
        )
        instance.signature = validated_data.get("signature", instance.signature)
        instance.address_link = validated_data.get(
            "address_link", instance.address_link
        )

        instance.custom_id = validated_data.get(
            "custom_id", instance.custom_id
        )

        instance.save()
        return instance


class PracticeCreateBuyerBPORequestSerializer(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PracticeBuyerReportRequest
        fields = [
            "agent",
            "id",
            "custom_id",
            "first_name",
            "last_name",
            "email",
            "phone",
            "other_information",
        ]

    def get_agent(self, obj):
        try:
            get_client = Agent.objects.get(id=obj.agent.id)
            serializer = AgentSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None

    def create(self, validated_data):
        leads = PracticeBuyerReportRequest.objects.create(**validated_data)

        return leads


class PracticeRetrieveBuyerBPORequestSerializer(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PracticeBuyerReportRequest
        fields = [
            "id",
            "agent",
            "custom_id",
            "first_name",
            "last_name",
            "email",
            "phone",
            "other_information",
        ]

    def get_agent(self, obj):
        try:
            get_client = Agent.objects.get(id=obj.agent.id)
            serializer = AgentSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None

    def update(self, instance, validated_data):
        agent_id = self.context.get("agent_id")
        if agent_id is not None:
            instance.agent_id = agent_id

        instance.first_name = validated_data.get("first_name", instance.first_name)
        instance.last_name = validated_data.get("last_name", instance.last_name)
        instance.email = validated_data.get("email", instance.email)
        instance.phone = validated_data.get("phone", instance.phone)
        instance.other_information = validated_data.get("other_information", instance.other_information)
        instance.custom_id = validated_data.get(
            "custom_id", instance.custom_id
        )

        instance.save()
        return instance
