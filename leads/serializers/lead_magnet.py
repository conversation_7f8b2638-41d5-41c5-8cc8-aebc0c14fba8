from rest_framework import serializers
from leads.models import (
    LeadMagnet,
)
from register.models.agents import Agent
from register.serializers.agents import AgentSerializer


class LeadsMagnetsSerializer(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = LeadMagnet
        fields = [
            "agent",
            "full_name",
            "email",
            "phone",
            "page",
            "is_verified",
            "date_created",
            "question",
            "updated_at",
            "date",
            "source_url",
        ]

    def get_agent(self, obj):
        try:
            get_client = Agent.objects.get(id=obj.agent.id)
            serializer = AgentSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None

    def update(self, instance, validated_data):

        agent_id = self.context.get("agent_id")
        if agent_id is not None:
            instance.agent_id = agent_id

        instance.full_name = validated_data.get("full_name", instance.full_name)
        instance.email = validated_data.get("email", instance.email)
        instance.phone = validated_data.get("phone", instance.phone)
        instance.page = validated_data.get("page", instance.page)
        instance.question = validated_data.get("question", instance.question)
        instance.is_verified = validated_data.get("is_verified", instance.is_verified)

        instance.save()
        return instance
