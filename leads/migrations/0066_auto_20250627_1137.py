# Generated by Django 3.2.15 on 2025-06-27 11:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('leads', '0065_rename_comparable_closed_listings_practicecomparableclosedsales_practice_comparable_closed_listings'),
    ]

    operations = [
        migrations.AddField(
            model_name='clientbporequest',
            name='source_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='divorce',
            name='source_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='expired',
            name='source_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='fsbo',
            name='source_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='leadmagnet',
            name='source_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='leads',
            name='source_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='probate',
            name='source_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='sellerreportrequest',
            name='source_url',
            field=models.URLField(blank=True, null=True),
        ),
    ]
