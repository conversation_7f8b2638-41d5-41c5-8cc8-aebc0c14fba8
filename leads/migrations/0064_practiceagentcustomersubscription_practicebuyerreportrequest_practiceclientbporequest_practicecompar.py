# Generated by Django 3.2.15 on 2025-06-25 13:53

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0112_auto_20250415_2133'),
        ('leads', '0063_clientbporequest_email'),
    ]

    operations = [
        migrations.CreateModel(
            name='PracticeClientBpoRequest',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('agent_id', models.IntegerField(default=0)),
                ('reason_for_request', models.CharField(blank=True, db_column='reason_for_request', max_length=255, null=True)),
                ('client_id', models.IntegerField(db_column='client_id', default=0)),
                ('clients_physical_address', models.CharField(blank=True, db_column='clients_physical_address', max_length=100, null=True)),
                ('report_status', models.BooleanField(default=False)),
                ('payment_status', models.Char<PERSON>ield(choices=[('Complete', 'Complete'), ('Incomplete', 'Incomplete'), ('Pending', 'Pending')], default='Decreased', max_length=255, null=True)),
                ('from_agent_profile', models.BooleanField(db_column='from_agent_profile', default=False)),
                ('num_of_active_listings', models.IntegerField(default=0)),
                ('active_listings_period', models.IntegerField(default=0)),
                ('current_market_value', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('current_suggested_list_price', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('repaired_market_value', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('repaired_suggested_list_price', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('final_summary', models.TextField(null=True)),
                ('brokerage', models.CharField(max_length=150, null=True)),
                ('name', models.CharField(max_length=200, null=True)),
                ('firm', models.CharField(max_length=200, null=True)),
                ('agent', models.CharField(max_length=100, null=True)),
                ('email', models.EmailField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(max_length=50, null=True)),
                ('number', models.IntegerField(null=True)),
                ('completed_by', models.CharField(max_length=100, null=True)),
                ('current_market_conditions', models.CharField(choices=[('Decreased', 'Decreased'), ('Slow', 'Slow'), ('Stable', 'Stable'), ('Increased', 'Increased')], default='Decreased', max_length=255, null=True)),
                ('subject_impact', models.CharField(choices=[('over', 'over'), ('under', 'under'), ('appropriate', 'appropriate')], default='appropriate', max_length=255, null=True)),
                ('status', models.CharField(choices=[('Complete', 'Complete'), ('Incomplete', 'Incomplete'), ('Pending', 'Pending')], default='Incomplete', max_length=255)),
                ('unit_type', models.CharField(choices=[('Single Family Detached', 'Single Family Detached'), ('Single Family Attached', 'Single Family Attached'), ('Condominium', 'Condominium'), ('Condominium', 'Condominium'), ('Town House', 'Town House'), ('Co-op', 'Co-op')], default='Single Family Detached', max_length=255, null=True)),
                ('drive_through_address', models.CharField(max_length=255, null=True)),
                ('market_price_change_percentage', models.IntegerField(default=0, null=True)),
                ('market_price_change_months', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('neighborhood_price_upper', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('neighborhood_price_lower', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('marketing_time', models.IntegerField(default=0, null=True)),
                ('num_of_sold_listings', models.IntegerField(default=0, null=True)),
                ('lead_type', models.CharField(blank=True, default='ordered', max_length=10, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sold_listings_time', models.IntegerField(default=0, null=True)),
                ('is_lead_signed', models.BooleanField(default=False)),
                ('date_signed', models.DateTimeField(blank=True, null=True)),
                ('signature', models.FileField(blank=True, null=True, upload_to='')),
                ('property_condition', models.CharField(choices=[('Poor', 'Poor'), ('Average', 'Average'), ('Above Average', 'Above Average'), ('Excellent Condition', 'Excellent Condition')], max_length=255, null=True)),
                ('last_time_remodeled', models.IntegerField(blank=True, null=True)),
                ('is_home_new', models.BooleanField(blank=True, null=True)),
                ('nearby_nuisances', models.TextField(blank=True, null=True)),
                ('customer_lot_size', models.IntegerField(default=0)),
                ('customer_garage', models.IntegerField(default=0)),
                ('customer_livable_square_footage', models.IntegerField(default=0)),
                ('is_property_remodelled', models.BooleanField(default=False)),
                ('is_year', models.BooleanField(default=False)),
                ('short_uuid', models.TextField(blank=True, null=True)),
                ('amount_for_construction_area', models.FloatField(default=0)),
                ('date', models.DateField(blank=True, null=True)),
                ('client_register', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_client_bpo_requests', to='register.client')),
            ],
            options={
                'db_table': 'practice_bpo_report_request',
            },
        ),
        migrations.CreateModel(
            name='PracticeSessionReportTierPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.TextField(default=None, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='PracticeSellerReportRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_created', models.DateTimeField(auto_created=True, auto_now_add=True)),
                ('property_address', models.CharField(blank=True, max_length=255, null=True)),
                ('schedule_type', models.CharField(blank=True, max_length=50, null=True)),
                ('meeting_date', models.CharField(blank=True, max_length=20, null=True)),
                ('meeting_time', models.CharField(blank=True, max_length=10, null=True)),
                ('sale_schedule', models.CharField(choices=[('ASAP', 'ASAP'), ('1 - 3 months', '1 - 3 months'), ('3 - 6 months', '3 - 6 months'), ('6 - 12 months', '6 - 12 months'), ('Just need Info', 'Just need Info')], default='ASAP', max_length=255)),
                ('property_state', models.CharField(choices=[('New Construction', 'New Construction'), ('Recently Remodeled', 'Recently Remodeled'), ('Needs a little work', 'Needs a little work'), ('Needs Full Remodelling', 'Needs Full Remodelling'), ('Tear down', 'Tear down')], default='New Construction', max_length=255)),
                ('reason_for_selling', models.CharField(choices=[('Upgrading my home', 'Upgrading my home'), ('Selling secondary home', 'Selling secondary home'), ('Relocating', 'Relocating'), ('Downsizing my home', 'Downsizing my home'), ('Retiring', 'Retiring'), ('Other', 'Other')], default='Upgrading my home', max_length=255)),
                ('working_with_an_agent', models.CharField(blank=True, max_length=3, null=True)),
                ('look_forward_to_buying', models.CharField(blank=True, max_length=3, null=True)),
                ('home_improvements', models.CharField(blank=True, max_length=255, null=True)),
                ('first_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(blank=True, max_length=100, null=True)),
                ('email', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=100, null=True)),
                ('lead_type', models.CharField(blank=True, max_length=10, null=True)),
                ('is_lead_signed', models.BooleanField(default=False)),
                ('other_information', models.CharField(blank=True, max_length=255, null=True)),
                ('signature', models.ImageField(blank=True, null=True, upload_to='')),
                ('address_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('custom_id', models.TextField(blank=True, null=True)),
                ('date', models.DateField(blank=True, null=True)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='register.agent')),
            ],
            options={
                'db_table': 'practice_leads_seller_bpo_request',
            },
        ),
        migrations.CreateModel(
            name='PracticeInteriorPhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.FileField(blank=True, null=True, upload_to='')),
                ('listings', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_interior', to='leads.practiceclientbporequest')),
            ],
        ),
        migrations.CreateModel(
            name='PracticeExteriorPhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.FileField(blank=True, null=True, upload_to='')),
                ('listings', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_exterior', to='leads.practiceclientbporequest')),
            ],
        ),
        migrations.CreateModel(
            name='PracticeComparableClosedSales',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('sales_price', models.IntegerField(blank=True, null=True)),
                ('price_per_gross_living_area', models.IntegerField(blank=True, null=True)),
                ('sale_date', models.DateTimeField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('lot_size', models.FloatField(blank=True, null=True)),
                ('condition', models.CharField(blank=True, choices=[('very_poor', 'very_poor'), ('poor', 'poor'), ('average', 'average'), ('above_average', 'above_average'), ('excellent', 'excellent')], max_length=255, null=True)),
                ('num_bedrooms', models.IntegerField(default=0)),
                ('num_bathrooms', models.IntegerField(default=0)),
                ('gross_living_area', models.IntegerField(default=0)),
                ('home_style_value', models.IntegerField(default=0, null=True)),
                ('home_style_feedback', models.TextField(blank=True, null=True)),
                ('feng_shui_value', models.IntegerField(default=0, null=True)),
                ('feng_shui_feedback', models.TextField(blank=True, null=True)),
                ('proximity_to_amenities_value', models.IntegerField(default=0, null=True)),
                ('proximity_to_amenities_feedback', models.TextField(blank=True, null=True)),
                ('proximity_neighborhood_negative_value', models.IntegerField(default=0, null=True)),
                ('proximity_neighborhood_negative_feedback', models.TextField(blank=True, null=True)),
                ('lot_size_difference_amount', models.IntegerField(default=0, null=True)),
                ('garage_difference_amount', models.IntegerField(default=0, null=True)),
                ('garage', models.IntegerField(default=0, null=True)),
                ('livable_square_footage_difference_amount', models.IntegerField(default=0, null=True)),
                ('images', django.contrib.postgres.fields.ArrayField(base_field=models.URLField(), blank=True, null=True, size=None)),
                ('comparable_closed_listings', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_comparable_closed_listings', to='leads.practiceclientbporequest')),
            ],
            options={
                'db_table': 'practice_comparable_closed_listings',
            },
        ),
        migrations.CreateModel(
            name='PracticeComparableActiveListings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('sales_price', models.IntegerField(blank=True, default=0, null=True)),
                ('price_per_gross_living_area', models.IntegerField(blank=True, default=0, null=True)),
                ('sale_date', models.DateTimeField(blank=True, null=True)),
                ('days_on_market', models.IntegerField(default=0)),
                ('description', models.TextField(blank=True, null=True)),
                ('lot_size', models.FloatField(blank=True, null=True)),
                ('condition', models.CharField(blank=True, choices=[('very_poor', 'very_poor'), ('poor', 'poor'), ('average', 'average'), ('above_average', 'above_average'), ('excellent', 'excellent')], max_length=255, null=True)),
                ('num_bedrooms', models.IntegerField(default=0)),
                ('num_bathrooms', models.IntegerField(default=0)),
                ('gross_living_area', models.IntegerField(default=0)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('home_style_value', models.IntegerField(default=0, null=True)),
                ('home_style_feedback', models.TextField(blank=True, null=True)),
                ('feng_shui_value', models.IntegerField(default=0, null=True)),
                ('feng_shui_feedback', models.TextField(blank=True, null=True)),
                ('proximity_to_amenities_value', models.IntegerField(default=0, null=True)),
                ('proximity_to_amenities_feedback', models.TextField(blank=True, null=True)),
                ('proximity_neighborhood_negative_value', models.IntegerField(default=0, null=True)),
                ('proximity_neighborhood_negative_feedback', models.TextField(blank=True, null=True)),
                ('lot_size_difference_amount', models.IntegerField(default=0, null=True)),
                ('garage', models.IntegerField(default=0, null=True)),
                ('garage_difference_amount', models.IntegerField(default=0, null=True)),
                ('livable_square_footage_difference_amount', models.IntegerField(default=0, null=True)),
                ('images', django.contrib.postgres.fields.ArrayField(base_field=models.URLField(), blank=True, null=True, size=None)),
                ('practice_comparable_active_listings', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_comparable_active_listings', to='leads.practiceclientbporequest')),
            ],
            options={
                'db_table': 'practice_comparable_active_listings',
            },
        ),
        migrations.CreateModel(
            name='PracticeBuyerReportRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_created', models.DateTimeField(auto_created=True, auto_now_add=True)),
                ('custom_id', models.TextField(blank=True, null=True)),
                ('first_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(blank=True, max_length=100, null=True)),
                ('email', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=100, null=True)),
                ('other_information', models.CharField(blank=True, max_length=255, null=True)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='register.agent')),
            ],
            options={
                'db_table': 'practice_leads_buyer_bpo_request',
            },
        ),
        migrations.CreateModel(
            name='PracticeAgentCustomerSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.FloatField(default=0)),
                ('drive_amount', models.FloatField(default=0)),
                ('sub_amount', models.FloatField(default=0)),
                ('tier', models.IntegerField(blank=True, null=True)),
                ('number_of_report', models.IntegerField(blank=True, null=True)),
                ('status', models.CharField(choices=[('Subscription', 'Subscription'), ('One Time', 'One Time')], default='Subscription', max_length=255)),
                ('sub_ended', models.BooleanField(default=False)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('date_reset', models.DateTimeField(blank=True, null=True)),
                ('default_number_of_report', models.IntegerField(blank=True, null=True)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_agent_payments', to='register.agent')),
                ('client', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_client_subs', to='register.client')),
                ('payment_session', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_payment_session', to='leads.practicesessionreporttierpayment')),
                ('report', models.ManyToManyField(to='leads.PracticeClientBpoRequest')),
                ('subscription', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practice_agent_customer_subscription', to='register.stripesubscription')),
            ],
        ),
    ]
