from ..models import (
    PracticeAgentCustomerSubscription,
)
from compensation.models import PracticeAgentCompensation
import logging

logger = logging.getLogger("aws_logger")
stripe_logger = logging.getLogger("stripe_logs")


def practice_compensate_agent(agent_id, report_id):
    print("@@@@@@@@@@@@@@@@")
    try:
        print("doing first")
        get_agent_compensation = PracticeAgentCompensation.objects.get(agent_id=agent_id)
            
        # get_agent_compensation.amount = get_agent_compensation.amount + 100

        try:
            get_payment_compensation = PracticeAgentCustomerSubscription.objects.filter(
                status="One Time",
                agent_id=agent_id,
                report=report_id,
            ).last()
            print("got here")
            print(get_payment_compensation.drive_amount)

            if get_payment_compensation.tier == 1:
                if not get_payment_compensation.drive_amount == 0:
                    drive_amount = get_payment_compensation.drive_amount - 99
                else:
                    drive_amount = 0

                print("got here1")
                agent_compensation = abs(get_payment_compensation.amount - 99)
                get_agent_compensation.amount = (
                    get_agent_compensation.amount + agent_compensation + drive_amount
                )

            if get_payment_compensation.tier == 2:
                if not get_payment_compensation.drive_amount == 0:
                    drive_amount = get_payment_compensation.drive_amount - (
                        get_payment_compensation.drive_amount * 0.2
                    )
                else:
                    drive_amount = 0
                agent_compensation = abs(
                    get_payment_compensation.amount
                    - (get_payment_compensation.amount * 0.2)
                )
                get_agent_compensation.amount = (
                    get_agent_compensation.amount + agent_compensation + drive_amount
                )
            if get_payment_compensation.tier == 3:
                if not get_payment_compensation.drive_amount == 0:
                    drive_amount = get_payment_compensation.drive_amount - (
                        get_payment_compensation.drive_amount * 0.25
                    )
                else:
                    drive_amount = 0
                agent_compensation = abs(
                    get_payment_compensation.amount
                    - (get_payment_compensation.amount * 0.25)
                )
                get_agent_compensation.amount = (
                    get_agent_compensation.amount + agent_compensation + drive_amount
                )
        except Exception as e:
            print(e)
            logger.info("could not compensate subscription payment")

        get_agent_compensation.save()
    except PracticeAgentCompensation.DoesNotExist:
        create_agent_compensation = PracticeAgentCompensation.objects.create(
            agent_id=agent_id, amount=0
        )

        try:
            get_payment_compensation = PracticeAgentCustomerSubscription.objects.filter(
                status="One Time",
                agent_id=agent_id,
                report=report_id,
            ).last()

            if get_payment_compensation.tier == 1:
                if not get_payment_compensation.drive_amount == 0:
                    drive_amount = get_payment_compensation.drive_amount - 99
                else:
                    drive_amount = 0
                agent_compensation = abs(get_payment_compensation.amount - 99)
                create_agent_compensation.amount = (
                    create_agent_compensation.amount + agent_compensation + drive_amount
                )

            if get_payment_compensation.tier == 2:
                if not get_payment_compensation.drive_amount == 0:
                    drive_amount = get_payment_compensation.drive_amount - (
                        get_payment_compensation.drive_amount * 0.2
                    )
                else:
                    drive_amount = 0
                agent_compensation = abs(
                    get_payment_compensation.amount
                    - (get_payment_compensation.amount * 0.2)
                )
                create_agent_compensation.amount = (
                    create_agent_compensation.amount + agent_compensation + drive_amount
                )

            if get_payment_compensation.tier == 3:
                if not get_payment_compensation.drive_amount == 0:
                    drive_amount = get_payment_compensation.drive_amount - (
                        get_payment_compensation.drive_amount * 0.25
                    )
                else:
                    drive_amount = 0
                agent_compensation = abs(
                    get_payment_compensation.amount
                    - (get_payment_compensation.amount * 0.25)
                )
                create_agent_compensation.amount = (
                    create_agent_compensation.amount + agent_compensation + drive_amount
                )
            create_agent_compensation.save()
        except Exception:
            logger.info("could not compensate subscription payment")


def compensate_sub_one_time(agent_amount, agent_id, sub_amount, tier, get_drive_amount):
    try:
        logger.info("bpo drive amount in function %s", get_drive_amount)
        get_agent_compensation = PracticeAgentCompensation.objects.get(agent_id=agent_id)
        try:
            if tier == 1:
                agent_compensation = abs(agent_amount - 99)
                if not get_drive_amount == 0:
                    drive_amount = get_drive_amount - 99
                    logger.info("tier one drive %s", drive_amount)
                else:
                    logger.info("tier one drive 0")

                    drive_amount = 0
                get_agent_compensation.amount = (
                    get_agent_compensation.amount + agent_compensation + drive_amount
                )
            if tier == 2:
                
                if not get_drive_amount == 0:

                    drive_amount = get_drive_amount - (get_drive_amount * 0.2)
                    logger.info("tier two drive %s", drive_amount)

                else:
                    logger.info("tier two drive 0")
                    
                    drive_amount = 0
                agent_compensation = abs(agent_amount - (agent_amount * 0.2))
                get_agent_compensation.amount = (
                    get_agent_compensation.amount + agent_compensation + drive_amount
                )
            if tier == 3:
                if not get_drive_amount == 0:
                    drive_amount = get_drive_amount - (get_drive_amount * 0.25)
                    logger.info("tier three drive %s", drive_amount)

                else:
                    drive_amount = 0
                    logger.info("tier three drive 0")
                    
                agent_compensation = abs(agent_amount - (agent_amount * 0.25))
                get_agent_compensation.amount = (
                    get_agent_compensation.amount + agent_compensation + drive_amount
                )

            amount_for_agent = 0.7 * sub_amount
            get_agent_compensation.amount = (
                get_agent_compensation.amount + amount_for_agent
            )

        except Exception:
            logger.info("could not compensate one time payment")

        get_agent_compensation.save()
    except PracticeAgentCompensation.DoesNotExist:
        create_agent_compensation = PracticeAgentCompensation.objects.create(agent_id=agent_id)

        try:
            if tier == 1:
                if not get_drive_amount == 0:
                    drive_amount = get_drive_amount - 99
                else:
                    drive_amount = 0
                agent_compensation = abs(agent_amount - 99)
                create_agent_compensation.amount = (
                    create_agent_compensation.amount + agent_compensation
                )

            if tier == 2:
                if not get_drive_amount == 0:
                    drive_amount = get_drive_amount - (get_drive_amount * 0.2)
                else:
                    drive_amount = 0
                agent_compensation = abs(agent_amount - (agent_amount * 0.2))
                create_agent_compensation.amount = (
                    create_agent_compensation.amount + agent_compensation + drive_amount
                )

            if tier == 3:
                if not get_drive_amount == 0:
                    drive_amount = get_drive_amount - (get_drive_amount * 0.25)
                else:
                    drive_amount = 0
                agent_compensation = abs(agent_amount - (agent_amount * 0.25))
                create_agent_compensation.amount = (
                    create_agent_compensation.amount + agent_compensation + drive_amount
                )

            amount_for_agent = 0.7 * sub_amount
            get_agent_compensation.amount = (
                get_agent_compensation.amount + amount_for_agent + drive_amount
            )
            create_agent_compensation.save()
        except Exception:
            logger.info("could not compensate one time payment")


def compensate_sub_every_month(sub_id):
    try:
        get_agent_sub = PracticeAgentCustomerSubscription.objects.get(subscription_id=sub_id)
        get_agent_compensation = PracticeAgentCompensation.objects.get(
            agent_id=get_agent_sub.agent.id
        )
        try:
            amount_for_agent = 0.7 * get_agent_sub.sub_amount
            get_agent_compensation.amount = (
                get_agent_compensation.amount + amount_for_agent
            )

        except Exception:
            logger.info("could not compensate monthly payment")

        get_agent_compensation.save()
    except PracticeAgentCompensation.DoesNotExist:
        get_agent_sub = PracticeAgentCustomerSubscription.objects.get(subscription_id=sub_id)
        create_agent_compensation = PracticeAgentCompensation.objects.create(
            agent_id=get_agent_sub.agent.id
        )

        try:
            amount_for_agent = 0.7 * get_agent_sub.sub_amount
            get_agent_compensation.amount = (
                get_agent_compensation.amount + amount_for_agent
            )
            create_agent_compensation.save()
        except Exception:
            logger.info("could not compensate monthly payment")
