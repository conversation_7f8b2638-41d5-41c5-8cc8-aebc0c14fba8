from rest_framework import generics, status
from leads.models import (
    PracticeSellerReportRequest,
    PracticeBuyerReportRequest,
)
from leads.serializers import (
    PracticeSellerBPORequestSerializer,
    PracticeCreateBuyerBPORequestSerializer,
    PracticeRetrieveBuyerBPORequestSerializer,
)
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny, IsAuthenticatedOrReadOnly

from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.response import Response

from leads.views.defaults import ResultsSetPagination


class PracticeSellerReportRequestView(generics.ListCreateAPIView):
    queryset = PracticeSellerReportRequest.objects.all()
    serializer_class = PracticeSellerBPORequestSerializer


class PracticeDetailSellerReportRequest(
    generics.RetrieveAPIView, generics.UpdateAPIView, APIView
):
    permission_classes = [IsAuthenticatedOrReadOnly]
    authentication_classes = [JWTAuthentication]
    serializer_class = PracticeSellerBPORequestSerializer
    model = PracticeSellerReportRequest
    queryset = PracticeSellerReportRequest.objects
    permision_model = PracticeSellerReportRequest

    def get_object(self, pk):
        get_contact = PracticeSellerReportRequest.objects.get(id=pk)
        return get_contact

    def get(self, request, pk, format=None):
        get_contact = self.get_object(pk)
        serializer = self.serializer_class(get_contact, many=False)
        return Response(serializer.data)

    def patch(self, request, pk, *args, **kwargs):
        instance = self.get_object(pk)
        data = request.data
        serializer = self.get_serializer(
            instance,
            data=request.data,
            context={
                "agent_id": data.get("agent_id", None),
            },
            partial=True,
        )

        if serializer.is_valid():
            serializer.save()
            return Response(
                serializer.data,
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "failed",
                    "detail": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete(self, request, pk, format=None):
        get_contact = self.get_object(pk)
        get_contact.delete()
        return Response(
            {"status": "success", "detail": "Lead deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )


class PracticeSellerReportRequestCreateView(generics.CreateAPIView):
    serializer_class = PracticeSellerBPORequestSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            data=request.data,
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        
        return serializer

    def post(self, request, *args, **kwargs):
        seller = self.create(request, *args, **kwargs)
        print("\n\n\n\n\n")
        print(seller)
        return Response(seller.data)


class PracticeSellerReportRequestListView(generics.ListAPIView):
    queryset = PracticeSellerReportRequest.objects.all().order_by("-date_created")
    serializer_class = PracticeSellerBPORequestSerializer
    pagination_class = ResultsSetPagination


class PracticeBuyerReportRequestView(generics.CreateAPIView):
    queryset = PracticeBuyerReportRequest.objects.all()
    serializer_class = PracticeCreateBuyerBPORequestSerializer


class PracticeBuyerReportRequestCreateView(generics.CreateAPIView):
    serializer_class = PracticeCreateBuyerBPORequestSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            data=request.data,
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        
        return serializer

    def post(self, request, *args, **kwargs):
        buyer = self.create(request, *args, **kwargs)
        return Response(buyer.data)


class PracticeDetailBuyerReportRequest(
    generics.RetrieveAPIView, generics.UpdateAPIView, APIView
):
    permission_classes = [IsAuthenticatedOrReadOnly]
    authentication_classes = [JWTAuthentication]
    serializer_class = PracticeRetrieveBuyerBPORequestSerializer
    model = PracticeBuyerReportRequest
    queryset = PracticeBuyerReportRequest.objects
    permision_model = PracticeBuyerReportRequest

    def get_object(self, pk):
        get_contact = PracticeBuyerReportRequest.objects.get(id=pk)
        return get_contact

    def get(self, request, pk, format=None):
        get_contact = self.get_object(pk)
        serializer = self.serializer_class(get_contact, many=False)
        return Response(serializer.data)

    def patch(self, request, pk, *args, **kwargs):
        instance = self.get_object(pk)
        data = request.data
        serializer = self.get_serializer(
            instance,
            data=request.data,
            context={
                "agent_id": data.get("agent_id", None),
            },
            partial=True,
        )

        if serializer.is_valid():
            serializer.save()
            return Response(
                serializer.data,
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "failed",
                    "detail": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete(self, request, pk, format=None):
        get_contact = self.get_object(pk)
        get_contact.delete()
        return Response(
            {"status": "success", "detail": "Lead deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )


class PracticeBuyerReportRequestListView(generics.ListAPIView):
    queryset = PracticeBuyerReportRequest.objects.all().order_by("date_created")
    serializer_class = PracticeRetrieveBuyerBPORequestSerializer
    pagination_class = ResultsSetPagination
