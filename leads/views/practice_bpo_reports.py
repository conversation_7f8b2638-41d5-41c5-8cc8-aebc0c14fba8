from django.http import Http404
from django_filters.rest_framework import Django<PERSON>ilter<PERSON><PERSON>end
from rest_framework import generics, status
from rest_framework import mixins
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework.response import Response
from rest_framework.views import APIView
from deal_analyzer.exceptions.errors import ResourceNotFound
from geopy.distance import distance
from geopy.geocoders import GoogleV3
import os
from leads.models import (
    PracticeClientBpoRequest,
    PracticeExteriorPhoto,
    PracticeAgentCustomerSubscription,
    PracticeInteriorPhoto,
)
from leads.serializers import (
    PracticeCreateReportSerializer,
    PracticeRetrieveReportSerializer,
    PracticePatchReportSerializer,
    PracticeCheckReportSerializer,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from deal_analyzer.models.straight_aesthetics_remodel import EstimatedResaleValueComps
from django.db.models import Q
from rest_framework.mixins import *
from leads.views.defaults import ResultsSetPagination
from register.models import Client, Agent
# from leads.email import (
#     send_completed_report_email,
#     send_created_report_email,
#     send_bpo_agent_notify,
# )
from farm.models import Parcel
# from leads.utils.practice_compensate_agent import practice_compensate_agent


class PracticeClientReportRequestView(
    generics.ListAPIView,
    mixins.RetrieveModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    generics.GenericAPIView,
):
    serializer_class = PracticeRetrieveReportSerializer
    queryset = PracticeClientBpoRequest.objects
    lookup_field = "pk"
    pagination_class = ResultsSetPagination

    # Gets all reports
    def get_queryset(self):
        report = (
            self.queryset.select_related("client_register")
            .select_related("client_register__user")
            .prefetch_related("practice_comparable_active_listings")
            .prefetch_related("practice_comparable_closed_listings")
            .order_by("-date_created")
        )
        return report

        # reports = RetrieveReportSerializer(report, many=True)
        # return Response(data=reports.data, status=status.HTTP_200_OK)

    def post(self, request, *args, **kwargs):
        serializer_class = PracticeCreateReportSerializer(data=request.data, partial=True)

        if serializer_class.is_valid(raise_exception=True):
            serializer_class.save()
            try:
                get_agent = Agent.objects.filter(id=request.data["agent_id"]).first()
                if get_agent.report_notification:
                    try:
                        client = Client.objects.get(id=request.client_register_id)
                        full_name = client.user.first_name + " " + client.user.last_name
                        # send_bpo_agent_notify(
                        #     get_agent.user.email,
                        #     full_name,
                        #     client.phone,
                        #     client.user.email,
                        #     request.data["clients_physical_address"],
                        #     request.data["reason_for_request"],
                        #     serializer_class.data["id"],
                        # )
                    except Exception as e:
                        print(e)
                try:
                    client = Client.objects.get(id=request.client_register_id)
                    client_email = client.user.email
                    name = client.user.first_name + " " + client.user.last_name
                    # send_created_report_email([client_email])
                except Exception as e:
                    print(e)
            except Exception:
                try:
                    client = Client.objects.get(id=request.client_register_id)
                    client_email = client.user.email
                    name = client.user.first_name + " " + client.user.last_name
                    # send_created_report_email([client_email])
                except Exception as e:
                    print(e)

            return Response(data=serializer_class.data, status=status.HTTP_201_CREATED)
        return Response(
            data=serializer_class.errors, status=status.HTTP_400_BAD_REQUEST
        )

    def update(self, request, *args, **kwargs):
        serializer_class = PracticeCreateReportSerializer(data=request.data, partial=True)
        # comparable_serializer_class = ComparableSerializer(data=request.data.get('comparable_active_listings'))
        # comparable_closed_serializer_class = ComparableClosedSerializer(
        # data=request.data.get('comparable_closed_listings'))
        if serializer_class.is_valid(raise_exception=True):
            # comparable_serializer_class.save()
            # comparable_closed_serializer_class.save()
            serializer_class.save()
            return Response(data=serializer_class.data, status=status.HTTP_201_CREATED)
        return Response(
            data=serializer_class.errors, status=status.HTTP_400_BAD_REQUEST
        )

    def delete(self, request, *args, **kwargs):
        return self.destroy(request, *args, **kwargs)


class PracticeClientReportRequestDetailView(
    generics.RetrieveAPIView,
    APIView,
    UpdateModelMixin,
):
    serializer_class = PracticeRetrieveReportSerializer
    queryset = PracticeClientBpoRequest.objects.order_by("-date_created").all()
    lookup_field = "pk"

    def get_object(self, pk=None):
        try:
            pk = self.kwargs.get("pk")
            return PracticeClientBpoRequest.objects.get(id=pk)
        except PracticeClientBpoRequest.DoesNotExist:
            raise ResourceNotFound
        except Exception:
            raise ResourceNotFound

    def get(self, request, pk, format=None):
        report = self.get_object(pk)

        try:
            get_sub = PracticeAgentCustomerSubscription.objects.get(
                payment_session__session_id=report.short_uuid
            )
            if not report in get_sub.report.all():
                get_sub.report.add(report)

        except Exception:
            pass

        serializer = PracticeRetrieveReportSerializer(report, many=False)
        return Response(serializer.data)

    def put(self, request, pk, format=None):
        report = self.get_object(pk)
        request_data = request.data["practice_comparable_active_listings"]
        request_data_closed = request.data["practice_comparable_closed_listings"]
        # try:
        #     if (len(request_data) <= 0):
        #         ComparableActiveListings.objects.get(comparable_active_listings_id=pk).delete()

        #     if (len(request_data_closed) <= 0):
        #         ComparableClosedSales.objects.get(comparable_closed_listings_id=pk).delete()

        # except ObjectDoesNotExist as e:
        #     print(e)

        serializer = PracticeRetrieveReportSerializer(
            report,
            data=request.data,
            context={
                "comparable_active": request_data,
                "comparable_closed": request_data_closed,
            },
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, pk):
        report = self.get_object(pk)
        print(request.data)
        serializer = PracticePatchReportSerializer(
            report, data=request.data, partial=True
        )  # set partial=True to update a data partially
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PracticeClientReportRequestListViewWithoutPaginations(generics.ListAPIView):
    queryset = PracticeClientBpoRequest.objects.all().order_by("-date_created")
    serializer_class = PracticeRetrieveReportSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        "agent_id",
        "client_id",
        "payment_status",
        "status",
        "client_register_id",
        "lead_type",
    ]
    search_fields = ["payment_status", "lead_type", "client_register_id"]


class PracticeClientReportRequestListView(generics.ListAPIView):
    queryset = PracticeClientBpoRequest.objects.all()
    serializer_class = PracticeRetrieveReportSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        "agent_id",
        "client_id",
        "payment_status",
        "status",
        "client_register_id",
        "lead_type",
    ]
    search_fields = ["payment_status", "lead_type"]
    pagination_class = ResultsSetPagination


class PracticeExteriorView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get_object(self, pk):
        try:
            return PracticeExteriorPhoto.objects.get(pk=pk)
        except PracticeExteriorPhoto.DoesNotExist:
            raise Http404

    def delete(self, request, pk, format=None):
        get_time = self.get_object(pk)
        get_time.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class PracticeInteriorView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get_object(self, pk):
        try:
            return PracticeInteriorPhoto.objects.get(pk=pk)
        except PracticeInteriorPhoto.DoesNotExist:
            raise Http404

    def delete(self, request, pk, format=None):
        get_time = self.get_object(pk)
        get_time.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class PracticeGetGarageFromParcelToComps(APIView):
    permission_classes = []

    def post(self, request):
        try:
            for comps in EstimatedResaleValueComps.objects.all():
                get_parcel = Parcel.objects.get(id=comps.id)
                comps.garage = get_parcel.parking_spaces
                comps.save()
        except Exception:
            pass
        return Response("Done Updating", status=status.HTTP_200_OK)


class PracticeGetDiliveryDistance(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        agent_address = self.request.GET.get("agent_address", None)
        client_address = self.request.GET.get("client_address", None)

        geolocator = GoogleV3(api_key=os.getenv("GOOGLE_API"))

        try:
            location1 = geolocator.geocode(agent_address)
            coord1 = (location1.latitude, location1.longitude)

            location2 = geolocator.geocode(client_address)
            coord2 = (location2.latitude, location2.longitude)
        except Exception:
            return Response(
                {"detail": "Error getting coordinate"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        distance_km = distance(coord1, coord2).km
        distance_mile = distance_km / 1.6

        return Response(
            {
                "distance": round(distance_mile, 2),
            },
            status=status.HTTP_200_OK,
        )


class PracticeCheckPendingReport(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, request):
        try:
            get_client = Client.objects.get(user=request.user)

            get_uncompleted_reports = (
                PracticeClientBpoRequest.objects.filter(client_register_id=get_client.id)
                .filter(
                    Q(customer_livable_square_footage=0)
                    | Q(customer_lot_size=0)
                    | Q(customer_garage=0)
                    | Q(exterior__isnull=True)
                    | Q(interior__isnull=True)
                )
                .exclude(status="Complete")
            )

            serializer = PracticeCheckReportSerializer(get_uncompleted_reports, many=True)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response(f"{e}", status=status.HTTP_400_BAD_REQUEST)


class PracticeCheckReportSummary(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, request):
        try:
            get_agent = Agent.objects.get(user=request.user)

            get_uncompleted_reports = (
                PracticeClientBpoRequest.objects.filter(agent_id=get_agent.id)
                .filter(
                    Q(customer_livable_square_footage=0)
                    | Q(customer_lot_size=0)
                    | Q(customer_garage=0)
                    | Q(exterior__isnull=True)
                    | Q(interior__isnull=True)
                )
                .exclude(status="Complete")
                .count()
            )

            completed_report = (
                PracticeClientBpoRequest.objects.filter(agent_id=get_agent.id)
                .filter(status="Complete")
                .count()
            )

            pending_report = (
                PracticeClientBpoRequest.objects.filter(agent_id=get_agent.id)
                .filter(status="Pending")
                .count()
            )
            return Response(
                {
                    "pending": pending_report,
                    "completed": completed_report,
                    "incompleted": get_uncompleted_reports,
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            return Response(f"{e}", status=status.HTTP_400_BAD_REQUEST)


class PracticeGetClosedListings(APIView):
    """Get all available comps for estimated resale value"""

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        query = self.request.GET.get("query", None)

        # perform filter lookups
        if query:
            queryset = EstimatedResaleValueComps.objects.filter(
                Q(street__icontains=query)
                | Q(city__icontains=query)
                | Q(state_or_province__icontains=query)
            ).exclude(lot_size__isnull=True)[:10]
            return Response(
                {
                    "status": "success",
                    "detail": "queried comps returned",
                    "data": queryset.values(),
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "no comps available for query",
            },
            status=status.HTTP_404_NOT_FOUND,
        )

    def post(self, *args, **kwargs):
        request = self.request

        address = request.data["address"]

        split_address = [x.strip() for x in address.split(",")]

        state = "".join(split_address[-2]).upper()
        city = "".join(split_address[-3]).upper()

        if split_address:
            queryset = EstimatedResaleValueComps.objects.filter(
                (Q(city__icontains=city) & Q(state_or_province__icontains=state))
            )[:10]

            return Response(
                {
                    "status": "success",
                    "detail": "filtered queryset returned successfully",
                    "data": queryset.values(),
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "no comps available for this filter",
            },
            status=status.HTTP_404_NOT_FOUND,
        )


class PracticeSendReportMailView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, request):
        report_id = request.data.get("report_id", None)
        report = PracticeClientBpoRequest.objects.get(id=report_id)
        if report.status == "Complete":
            print("work")
            pass
        else:
            report.status = "Complete"
            report.save()
            print("saved")

            # practice_compensate_agent(report.agent_id, report.id)

        try:
            client_email = report.client_register.user.email
            report_id = report.id
            # send_completed_report_email(client_email, report.id)
        except Exception as e:
            print(e)
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)
        return Response("Report email sent successfully", status=status.HTTP_200_OK)
