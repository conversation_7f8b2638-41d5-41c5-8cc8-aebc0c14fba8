from django.db import models

from leads.models.practice_bpo_reports import PracticeClientBpoRequest


class PracticeSessionReportTierPayment(models.Model):
    session_id = models.TextField(null=True, default=None)


class PracticeAgentCustomerSubscription(models.Model):
    SUBSCRIPTION = "Subscription"
    ONE_TIME = "One Time"

    STATUS = [
        (SUBSCRIPTION, SUBSCRIPTION),
        (ONE_TIME, ONE_TIME),
    ]

    subscription = models.OneToOneField(
        "register.StripeSubscription",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="practice_agent_customer_subscription",
    )
    payment_session = models.OneToOneField(
        PracticeSessionReportTierPayment,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="practice_payment_session",
    )
    agent = models.ForeignKey(
        "register.Agent",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="practice_agent_payments",
    )
    client = models.ForeignKey(
        "register.Client",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="practice_client_subs",
    )
    amount = models.FloatField(default=0)
    drive_amount = models.FloatField(default=0)
    sub_amount = models.FloatField(default=0)
    tier = models.IntegerField(null=True, blank=True)
    number_of_report = models.IntegerField(null=True, blank=True)
    status = models.CharField(max_length=255, choices=STATUS, default=SUBSCRIPTION)
    report = models.ManyToManyField(PracticeClientBpoRequest)
    sub_ended = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    date_reset = models.DateTimeField(null=True, blank=True)
    default_number_of_report = models.IntegerField(null=True, blank=True)
