from django.db import models
from register.models import Client
from django.contrib.postgres.fields import ArrayField


class PracticeClientBpoRequest(models.Model):
    # Selects

    MKT_DECREASED = "Decreased"
    MKT_SLOW = "Slow"
    MKT_STABLE = "Stable"
    MKT_INCREASED = "Increased"

    MKT_CONDITION_CHOICES = [
        (MKT_DECREASED, MKT_DECREASED),
        (MKT_SLOW, MKT_SLOW),
        (MKT_STABLE, MKT_STABLE),
        (MKT_INCREASED, MKT_INCREASED),
    ]

    PROPERTY_POOR = "Poor"
    PROPERTY_AVERAGE = "Average"
    PROPERTY_ABOVE_AVERAGE = "Above Average"
    PROPERTY_EXCELLENT_CONDITION = "Excellent Condition"
    PROPERTY_CONDITION = [
        (PROPERTY_POOR, PROPERTY_POOR),
        (PROPERTY_AVERAGE, PROPERTY_AVERAGE),
        (PROPERTY_ABOVE_AVERAGE, PROPERTY_ABOVE_AVERAGE),
        (PROPERTY_EXCELLENT_CONDITION, PROPERTY_EXCELLENT_CONDITION),
    ]

    SUBJECT_OVER_IMP = "over"
    SUBJECT_UNDER_IMP = "under"
    SUBJECT_APPROP_IMP = "appropriate"
    SUBJECT_IMPACT_CHOICES = [
        (SUBJECT_OVER_IMP, SUBJECT_OVER_IMP),
        (SUBJECT_UNDER_IMP, SUBJECT_UNDER_IMP),
        (SUBJECT_APPROP_IMP, SUBJECT_APPROP_IMP),
    ]

    UNIT_SFD = "Single Family Detached"
    UNIT_SFAT = "Single Family Attached"
    UNIT_SFA = "Condominium"
    UNIT_CONDO = "Condominium"
    UNIT_TOWNHOUSE = "Town House"
    UNIT_COOP = "Co-op"
    UNIT_TYPE_CHOICES = [
        (UNIT_SFD, UNIT_SFD),
        (UNIT_SFAT, UNIT_SFAT),
        (UNIT_SFA, UNIT_SFA),
        (UNIT_CONDO, UNIT_CONDO),
        (UNIT_TOWNHOUSE, UNIT_TOWNHOUSE),
        (UNIT_COOP, UNIT_COOP),
    ]

    STATUS_COMPLETED = "Complete"
    STATUS_INCOMPLETE = "Incomplete"
    STATUS_PENDING = "Pending"
    STATUS_CHOICES = [
        (STATUS_COMPLETED, STATUS_COMPLETED),
        (STATUS_INCOMPLETE, STATUS_INCOMPLETE),
        (STATUS_PENDING, STATUS_PENDING),
    ]

    PAYMENT_COMPLETED = "Complete"
    PAYMENT_INCOMPLETE = "Incomplete"
    PAYMENT_PENDING = "Pending"
    PAYMENT_STATUS_CHOICES = [
        (PAYMENT_COMPLETED, PAYMENT_COMPLETED),
        (PAYMENT_INCOMPLETE, PAYMENT_INCOMPLETE),
        (PAYMENT_PENDING, PAYMENT_PENDING),
    ]

    id = models.AutoField(primary_key=True)
    agent_id = models.IntegerField(
        default=0,
    )
    reason_for_request = models.CharField(
        max_length=255, db_column="reason_for_request", null=True, blank=True
    )
    client_id = models.IntegerField(
        db_column="client_id",
        default=0,
    )
    clients_physical_address = models.CharField(
        max_length=100, db_column="clients_physical_address", null=True, blank=True
    )
    report_status = models.BooleanField(default=False)
    payment_status = models.CharField(
        max_length=255, choices=PAYMENT_STATUS_CHOICES, default=MKT_DECREASED, null=True
    )
    from_agent_profile = models.BooleanField(
        default=False, db_column="from_agent_profile"
    )
    client_register = models.ForeignKey(
        Client, on_delete=models.SET_NULL, null=True, related_name="practice_client_bpo_requests"
    )
    num_of_active_listings = models.IntegerField(default=0)
    active_listings_period = models.IntegerField(default=0)
    current_market_value = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    current_suggested_list_price = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    repaired_market_value = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    repaired_suggested_list_price = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    final_summary = models.TextField(null=True)
    brokerage = models.CharField(null=True, max_length=150)
    name = models.CharField(null=True, max_length=200)
    firm = models.CharField(null=True, max_length=200)
    agent = models.CharField(null=True, max_length=100)
    email = models.EmailField(null=True, blank=True,  max_length=255)
    phone = models.CharField(null=True, max_length=50)
    number = models.IntegerField(null=True)
    completed_by = models.CharField(null=True, max_length=100)
    current_market_conditions = models.CharField(
        max_length=255, choices=MKT_CONDITION_CHOICES, default=MKT_DECREASED, null=True
    )
    subject_impact = models.CharField(
        max_length=255,
        choices=SUBJECT_IMPACT_CHOICES,
        default=SUBJECT_APPROP_IMP,
        null=True,
    )
    status = models.CharField(
        max_length=255, choices=STATUS_CHOICES, default=STATUS_INCOMPLETE
    )
    unit_type = models.CharField(
        max_length=255, choices=UNIT_TYPE_CHOICES, default=UNIT_SFD, null=True
    )
    drive_through_address = models.CharField(max_length=255, null=True)
    market_price_change_percentage = models.IntegerField(default=0, null=True)
    market_price_change_months = models.DecimalField(
        decimal_places=2, max_digits=10, default=0
    )
    neighborhood_price_upper = models.DecimalField(
        decimal_places=2, max_digits=10, default=0
    )
    neighborhood_price_lower = models.DecimalField(
        decimal_places=2, max_digits=10, default=0
    )
    marketing_time = models.IntegerField(default=0, null=True)
    num_of_sold_listings = models.IntegerField(default=0, null=True)
    lead_type = models.CharField(
        null=True, blank=True, max_length=10, default="ordered"
    )
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)
    sold_listings_time = models.IntegerField(default=0, null=True)
    is_lead_signed = models.BooleanField(default=False)
    date_signed = models.DateTimeField(null=True, blank=True)
    signature = models.FileField(null=True, blank=True)
    property_condition = models.CharField(
        max_length=255, choices=PROPERTY_CONDITION, null=True
    )
    last_time_remodeled = models.IntegerField(null=True, blank=True)
    is_home_new = models.BooleanField(null=True, blank=True)
    nearby_nuisances = models.TextField(null=True, blank=True)
    customer_lot_size = models.IntegerField(default=0)
    customer_garage = models.IntegerField(
        default=0,
    )
    customer_livable_square_footage = models.IntegerField(default=0)
    is_property_remodelled = models.BooleanField(default=False)
    is_year = models.BooleanField(default=False)
    short_uuid = models.TextField(null=True, blank=True)
    amount_for_construction_area = models.FloatField(default=0)
    date = models.DateField(null=True, blank=True)

    class Meta:
        db_table = "practice_bpo_report_request"


class PracticeExteriorPhoto(models.Model):
    listings = models.ForeignKey(
        PracticeClientBpoRequest, on_delete=models.SET_NULL, null=True, related_name="practice_exterior"
    )

    image = models.FileField(null=True, blank=True)


class PracticeInteriorPhoto(models.Model):
    listings = models.ForeignKey(
        PracticeClientBpoRequest, on_delete=models.SET_NULL, null=True, related_name="practice_interior"
    )

    image = models.FileField(null=True, blank=True)


class PracticeComparableActiveListings(models.Model):
    CONDITION_VERY_POOR = "very_poor"
    CONDITION_POOR = "poor"
    CONDITION_AVERAGE = "average"
    CONDITION_ABOVE_AVERAGE = "above_average"
    CONDITION_EXCELLENT = "excellent"

    COMPARABLE_CONDITION_CHOICES = [
        (CONDITION_VERY_POOR, CONDITION_VERY_POOR),
        (CONDITION_POOR, CONDITION_POOR),
        (CONDITION_AVERAGE, CONDITION_AVERAGE),
        (CONDITION_ABOVE_AVERAGE, CONDITION_ABOVE_AVERAGE),
        (CONDITION_EXCELLENT, CONDITION_EXCELLENT),
    ]

    address = models.CharField(null=True, blank=True, max_length=255)
    sales_price = models.IntegerField(null=True, blank=True, default=0)
    price_per_gross_living_area = models.IntegerField(null=True, blank=True, default=0)
    sale_date = models.DateTimeField(null=True, blank=True)
    days_on_market = models.IntegerField(default=0)
    description = models.TextField(null=True, blank=True)
    lot_size = models.FloatField(null=True, blank=True)
    condition = models.CharField(
        null=True, blank=True, max_length=255, choices=COMPARABLE_CONDITION_CHOICES
    )
    practice_comparable_active_listings = models.ForeignKey(
        PracticeClientBpoRequest,
        on_delete=models.SET_NULL,
        null=True,
        related_name="practice_comparable_active_listings",
    )
    num_bedrooms = models.IntegerField(default=0)
    num_bathrooms = models.IntegerField(default=0)
    gross_living_area = models.IntegerField(default=0)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)

    home_style_value = models.IntegerField(default=0, null=True)
    home_style_feedback = models.TextField(null=True, blank=True)
    feng_shui_value = models.IntegerField(default=0, null=True)
    feng_shui_feedback = models.TextField(null=True, blank=True)
    proximity_to_amenities_value = models.IntegerField(default=0, null=True)
    proximity_to_amenities_feedback = models.TextField(null=True, blank=True)
    proximity_neighborhood_negative_value = models.IntegerField(default=0, null=True)
    proximity_neighborhood_negative_feedback = models.TextField(null=True, blank=True)
    lot_size_difference_amount = models.IntegerField(default=0, null=True)
    garage = models.IntegerField(default=0, null=True)
    garage_difference_amount = models.IntegerField(default=0, null=True)
    livable_square_footage_difference_amount = models.IntegerField(default=0, null=True)
    images = ArrayField(models.URLField(), blank=True, null=True)

    class Meta:
        db_table = "practice_comparable_active_listings"


class PracticeComparableClosedSales(models.Model):
    CONDITION_VERY_POOR = "very_poor"
    CONDITION_POOR = "poor"
    CONDITION_AVERAGE = "average"
    CONDITION_ABOVE_AVERAGE = "above_average"
    CONDITION_EXCELLENT = "excellent"

    COMPARABLE_CONDITION_CHOICES = [
        (CONDITION_VERY_POOR, CONDITION_VERY_POOR),
        (CONDITION_POOR, CONDITION_POOR),
        (CONDITION_AVERAGE, CONDITION_AVERAGE),
        (CONDITION_ABOVE_AVERAGE, CONDITION_ABOVE_AVERAGE),
        (CONDITION_EXCELLENT, CONDITION_EXCELLENT),
    ]

    address = models.CharField(max_length=255, null=True, blank=True)
    sales_price = models.IntegerField(null=True, blank=True)
    price_per_gross_living_area = models.IntegerField(null=True, blank=True)
    sale_date = models.DateTimeField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    lot_size = models.FloatField(null=True, blank=True)
    condition = models.CharField(
        max_length=255, null=True, blank=True, choices=COMPARABLE_CONDITION_CHOICES
    )
    num_bedrooms = models.IntegerField(default=0)
    num_bathrooms = models.IntegerField(default=0)
    practice_comparable_closed_listings = models.ForeignKey(
        PracticeClientBpoRequest,
        on_delete=models.SET_NULL,
        null=True,
        related_name="practice_comparable_closed_listings",
    )
    gross_living_area = models.IntegerField(default=0)
    home_style_value = models.IntegerField(default=0, null=True)
    home_style_feedback = models.TextField(null=True, blank=True)
    feng_shui_value = models.IntegerField(default=0, null=True)
    feng_shui_feedback = models.TextField(null=True, blank=True)
    proximity_to_amenities_value = models.IntegerField(default=0, null=True)
    proximity_to_amenities_feedback = models.TextField(null=True, blank=True)
    proximity_neighborhood_negative_value = models.IntegerField(default=0, null=True)
    proximity_neighborhood_negative_feedback = models.TextField(null=True, blank=True)
    lot_size_difference_amount = models.IntegerField(default=0, null=True)
    garage_difference_amount = models.IntegerField(default=0, null=True)
    garage = models.IntegerField(default=0, null=True)
    livable_square_footage_difference_amount = models.IntegerField(default=0, null=True)
    images = ArrayField(models.URLField(), blank=True, null=True)

    class Meta:
        db_table = "practice_comparable_closed_listings"
