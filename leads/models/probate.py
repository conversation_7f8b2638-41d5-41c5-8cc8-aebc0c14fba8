import uuid

from django.db import models


class BaseTimeStamp(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    agent = models.ForeignKey(
        "register.Agent", on_delete=models.SET_NULL, null=True, blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


class Probate(BaseTimeStamp):

    county_name = models.CharField(null=True, max_length=255, blank=True)
    state = models.CharField(null=True, max_length=255, blank=True)
    file_date = models.Char<PERSON>ield(null=True, max_length=255, blank=True)
    first_name = models.Char<PERSON>ield(null=True, max_length=255, blank=True)
    last_name = models.CharField(null=True, max_length=255, blank=True)
    middle_name = models.CharField(null=True, max_length=255, blank=True)
    document_type = models.Char<PERSON>ield(null=True, default="probate", max_length=255, blank=True)
    property_address = models.CharField(null=True, max_length=255, blank=True)
    property_city = models.CharField(null=True, max_length=255, blank=True)
    property_zip = models.CharField(null=True, max_length=255, blank=True)
    mailing_address = models.CharField(null=True, max_length=255, blank=True)
    mailing_city = models.CharField(null=True, max_length=255, blank=True)
    mailing_state = models.CharField(null=True, max_length=255, blank=True)
    mailing_zip = models.CharField(null=True, max_length=255, blank=True)
    bedrooms = models.CharField(null=True, max_length=255, blank=True)
    bathrooms = models.CharField(null=True, max_length=255, blank=True)
    year_built = models.CharField(null=True, max_length=255, blank=True)
    property_use = models.CharField(null=True, max_length=255, blank=True)
    pr_first_name = models.CharField(null=True, max_length=255, blank=True)
    pr_last_name = models.CharField(null=True, max_length=255, blank=True)
    pr_middle_name = models.CharField(null=True, max_length=255, blank=True)
    pr_address = models.CharField(null=True, max_length=255, blank=True)
    pr_city = models.CharField(null=True, max_length=255, blank=True)
    pr_state = models.CharField(null=True, max_length=255, blank=True)
    pr_zip = models.CharField(null=True, max_length=255, blank=True)
    pr_phone_number = models.CharField(null=True, max_length=255, blank=True)
    attorney_first_name = models.CharField(null=True, max_length=255, blank=True)
    attorney_last_name = models.CharField(null=True, max_length=255, blank=True)
    attorney_middle_name = models.CharField(null=True, max_length=255, blank=True)
    attorney_address = models.CharField(null=True, max_length=255, blank=True)
    attorney_city = models.CharField(null=True, max_length=255, blank=True)
    attorney_state = models.CharField(null=True, max_length=255, blank=True)
    attorney_zip = models.CharField(null=True, max_length=255, blank=True)
    attorney_phone_number = models.CharField(null=True, max_length=255, blank=True)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    date = models.DateField(null=True, blank=True)
    source_url = models.URLField(null=True, blank=True)

    class Meta:
        db_table = "leads_probate"
        ordering = ("-created_at",)


class Divorce(BaseTimeStamp):
    county_name = models.CharField(null=True, max_length=255, blank=True)
    state = models.CharField(null=True, max_length=255, blank=True)
    file_date = models.CharField(null=True, max_length=255, blank=True)
    defendant_first_name = models.CharField(null=True, max_length=255, blank=True)
    defendant_last_name = models.CharField(null=True, max_length=255, blank=True)
    defendant_middle_name = models.CharField(null=True, max_length=255, blank=True)
    plaintiff_first_name = models.CharField(null=True, max_length=255, blank=True)
    plaintiff_last_name = models.CharField(null=True, max_length=255, blank=True)
    plaintiff_middle_name = models.CharField(null=True, max_length=255, blank=True)
    property_owned = models.CharField(null=True, max_length=255, blank=True)
    parcel_no = models.CharField(null=True, max_length=255, blank=True)
    document_type = models.CharField(null=True, default="divorce", max_length=255, blank=True)
    property_address = models.CharField(null=True, max_length=255, blank=True)
    property_city = models.CharField(null=True, max_length=255, blank=True)
    property_zip = models.CharField(null=True, max_length=255, blank=True)
    mailing_address = models.CharField(null=True, max_length=255, blank=True)
    mailing_city = models.CharField(null=True, max_length=255, blank=True)
    mailing_state = models.CharField(null=True, max_length=255, blank=True)
    mailing_zip = models.CharField(null=True, max_length=255, blank=True)
    bedrooms = models.CharField(null=True, max_length=255, blank=True)
    bathrooms = models.CharField(null=True, max_length=255, blank=True)
    sqft = models.CharField(null=True, max_length=255, blank=True)
    year_built = models.CharField(null=True, max_length=255, blank=True)
    property_use = models.CharField(null=True, max_length=255, blank=True)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    date = models.DateField(null=True, blank=True)
    source_url = models.URLField(null=True, blank=True)

    class Meta:
        db_table = "leads_divorce"
        ordering = ("-created_at",)


class Expired(BaseTimeStamp):
    listing_id = models.CharField(null=True, max_length=255)
    processed_date = models.CharField(null=True, max_length=255)
    document_type = models.CharField(null=True, default="expired", max_length=255)
    status = models.CharField(null=True, max_length=255)
    address = models.CharField(null=True, max_length=255)
    city = models.CharField(null=True, max_length=255)
    state = models.CharField(null=True, max_length=255)
    zip = models.CharField(null=True, max_length=255)
    county_name = models.CharField(null=True, max_length=255)
    price = models.CharField(null=True, max_length=255)
    bedrooms = models.CharField(null=True, max_length=255)
    bathrooms = models.CharField(null=True, max_length=255)
    year_built = models.CharField(null=True, max_length=255)
    property_type = models.CharField(null=True, max_length=255)
    square_footage = models.CharField(null=True, max_length=255)
    lot_size = models.CharField(null=True, max_length=255)
    contact_phone = models.CharField(null=True, max_length=255)
    list_agent_name = models.CharField(null=True, max_length=255)
    list_agent_phone = models.CharField(null=True, max_length=255)
    list_agent_email = models.CharField(null=True, max_length=255)
    owner_1_name = models.CharField(null=True, max_length=255)
    owner_2_name = models.CharField(null=True, max_length=255)
    owner_address = models.CharField(null=True, max_length=255)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    date = models.DateField(null=True, blank=True)
    source_url = models.URLField(null=True, blank=True)

    class Meta:
        db_table = "leads_expired"
        ordering = ("-created_at",)


class Fsbo(BaseTimeStamp):
    listing_id = models.CharField(null=True, max_length=255)
    processed_date = models.CharField(null=True, max_length=255)
    document_type = models.CharField(null=True, default="fsbo", max_length=255)
    status = models.CharField(null=True, max_length=255)
    address = models.CharField(null=True, max_length=255)
    city = models.CharField(null=True, max_length=255)
    state = models.CharField(null=True, max_length=255)
    zip = models.CharField(null=True, max_length=255)
    county_name = models.CharField(null=True, max_length=255)
    price = models.CharField(null=True, max_length=255)
    bedrooms = models.CharField(null=True, max_length=255)
    bathrooms = models.CharField(null=True, max_length=255)
    year_built = models.CharField(null=True, max_length=255)
    property_type = models.CharField(null=True, max_length=255)
    square_footage = models.CharField(null=True, max_length=255)
    lot_size = models.CharField(null=True, max_length=255)
    contact_phone = models.CharField(null=True, max_length=255)
    list_agent_name = models.CharField(null=True, max_length=255)
    list_agent_phone = models.CharField(null=True, max_length=255)
    list_agent_email = models.CharField(null=True, max_length=255)
    owner_1_name = models.CharField(null=True, max_length=255)
    owner_2_name = models.CharField(null=True, max_length=255)
    owner_address = models.CharField(null=True, max_length=255)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    date = models.DateField(null=True, blank=True)
    source_url = models.URLField(null=True, blank=True)

    class Meta:
        db_table = "leads_fsbo"
        ordering = ("-created_at",)


class Canceled(BaseTimeStamp):
    listing_id = models.CharField(null=True, max_length=255)
    processed_date = models.CharField(null=True, max_length=255)
    document_type = models.CharField(null=True, default="expired", max_length=255)
    status = models.CharField(null=True, max_length=255)
    address = models.CharField(null=True, max_length=255)
    city = models.CharField(null=True, max_length=255)
    state = models.CharField(null=True, max_length=255)
    zip = models.CharField(null=True, max_length=255)
    county_name = models.CharField(null=True, max_length=255)
    price = models.CharField(null=True, max_length=255)
    bedrooms = models.CharField(null=True, max_length=255)
    bathrooms = models.CharField(null=True, max_length=255)
    year_built = models.CharField(null=True, max_length=255)
    property_type = models.CharField(null=True, max_length=255)
    square_footage = models.CharField(null=True, max_length=255)
    lot_size = models.CharField(null=True, max_length=255)
    contact_phone = models.CharField(null=True, max_length=255)
    list_agent_name = models.CharField(null=True, max_length=255)
    list_agent_phone = models.CharField(null=True, max_length=255)
    list_agent_email = models.CharField(null=True, max_length=255)
    owner_1_name = models.CharField(null=True, max_length=255)
    owner_2_name = models.CharField(null=True, max_length=255)
    owner_address = models.CharField(null=True, max_length=255)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    date = models.DateField(null=True, blank=True)

    class Meta:
        db_table = "leads_canceled"
        ordering = ("-created_at",)


class Withdrawn(BaseTimeStamp):
    listing_id = models.CharField(null=True, max_length=255)
    processed_date = models.CharField(null=True, max_length=255)
    document_type = models.CharField(null=True, default="expired", max_length=255)
    status = models.CharField(null=True, max_length=255)
    address = models.CharField(null=True, max_length=255)
    city = models.CharField(null=True, max_length=255)
    state = models.CharField(null=True, max_length=255)
    zip = models.CharField(null=True, max_length=255)
    county_name = models.CharField(null=True, max_length=255)
    price = models.CharField(null=True, max_length=255)
    bedrooms = models.CharField(null=True, max_length=255)
    bathrooms = models.CharField(null=True, max_length=255)
    year_built = models.CharField(null=True, max_length=255)
    property_type = models.CharField(null=True, max_length=255)
    square_footage = models.CharField(null=True, max_length=255)
    lot_size = models.CharField(null=True, max_length=255)
    contact_phone = models.CharField(null=True, max_length=255)
    list_agent_name = models.CharField(null=True, max_length=255)
    list_agent_phone = models.CharField(null=True, max_length=255)
    list_agent_email = models.CharField(null=True, max_length=255)
    owner_1_name = models.CharField(null=True, max_length=255)
    owner_2_name = models.CharField(null=True, max_length=255)
    owner_address = models.CharField(null=True, max_length=255)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    date = models.DateField(null=True, blank=True)

    class Meta:
        db_table = "leads_withdrawn"
        ordering = ("-created_at",)
