from django.db import models


class LeadMagnet(models.Model):
    agent = models.ForeignKey(
        "register.Agent", on_delete=models.SET_NULL, null=True, blank=True
    )
    full_name = models.CharField(null=True, blank=True, max_length=250)
    email = models.CharField(null=True, blank=True, max_length=255)
    phone = models.CharField(null=True, blank=True, max_length=100)
    page = models.URLField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    question = models.TextField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    date = models.DateField(null=True, blank=True)
    source_url = models.URLField(null=True, blank=True)

    class Meta:
        db_table = "leads_magnets"
