from django.db import models
from datetime import datetime


class BaseModel(models.Model):
    created = models.CharField(
        max_length=255, null=True, default=datetime.now, blank=True
    )
    updated = models.CharField(
        max_length=255, null=True, default=datetime.now, blank=True
    )

    class Meta:
        abstract = True


class Leads(BaseModel, models.Model):
    id = models.AutoField(primary_key=True)
    agent = models.ForeignKey(
        "register.Agent", on_delete=models.SET_NULL, null=True, blank=True
    )
    situs_state_code = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        db_column="SitusStateCode",
        verbose_name="SitusStateCode",
    )
    situs_county = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        db_column="SitusCountry",
        verbose_name="SitusCountry",
    )
    property_address_full = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        db_column="PropertyAddressFull",
        verbose_name="PropertyAddressFull",
    )
    property_address_city = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        db_column="PropertyAddressCity",
        verbose_name="PropertyAddressCity",
    )
    property_address_zip = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        db_column="PropertyAddressZIP",
        verbose_name="PropertyAddressZIP",
    )
    property_latitude = models.FloatField(
        max_length=255,
        null=True,
        blank=True,
        db_column="PropertyLatitude",
        verbose_name="PropertyLatitude",
    )
    property_longitude = models.FloatField(
        null=True,
        blank=True,
        db_column="PropertyLongitude",
        verbose_name="PropertyLongitude",
    )
    property_use_group = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        db_column="PropertyUseGroup",
        verbose_name="PropertyUseGroup",
    )
    bath_count = models.CharField(
        db_column="BathCount",
        verbose_name="BathCount",
        max_length=255,
        null=True,
        blank=True,
    )
    bedrooms_count = models.CharField(
        db_column="BedroomsCount",
        verbose_name="BedroomsCount",
        max_length=255,
        null=True,
        blank=True,
    )
    area_building = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        db_column="AreaBuilding",
        verbose_name="AreaBuilding",
    )
    area_lot_sf = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        db_column="AreaLotSF",
        verbose_name="AreaLotSF",
    )
    year_built = models.CharField(
        max_length=4,
        null=True,
        blank=True,
        db_column="YearBuilt",
        verbose_name="YearBuilt",
    )
    lead_type = models.CharField(null=True, default="nod", max_length=255)
    borrower_name_owner = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        db_column="BorrowerNameOwner",
        verbose_name="BorrowerNameOwner",
    )
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    date = models.DateField(null=True, blank=True)
    source_url = models.URLField(null=True, blank=True)

    class Meta:
        db_table = "leads"
        ordering = ["-created"]


class LeadsImage(models.Model):
    image_file = models.ImageField(upload_to="leads_images/")
