from django.db import models


class PracticeSellerReportRequest(models.Model):
    PROP_STATE_1 = "New Construction"
    PROP_STATE_2 = "Recently Remodeled"
    PROP_STATE_3 = "Needs a little work"
    PROP_STATE_4 = "Needs Full Remodelling"
    PROP_STATE_5 = "Tear down"

    PROP_STATE_CHOICES = [
        (PROP_STATE_1, "New Construction"),
        (PROP_STATE_2, "Recently Remodeled"),
        (PROP_STATE_3, "Needs a little work"),
        (PROP_STATE_4, "Needs Full Remodelling"),
        (PROP_STATE_5, "Tear down"),
    ]

    REASON_FOR_SALE_1 = "Upgrading my home"
    REASON_FOR_SALE_2 = "Selling secondary home"
    REASON_FOR_SALE_3 = "Relocating"
    REASON_FOR_SALE_4 = "Downsizing my home"
    REASON_FOR_SALE_5 = "Retiring"
    REASON_FOR_SALE_6 = "Other"

    REASON_FOR_SALES_CHOICES = [
        (REASON_FOR_SALE_1, "Upgrading my home"),
        (REASON_FOR_SALE_2, "Selling secondary home"),
        (REASON_FOR_SALE_3, "Relocating"),
        (REASON_FOR_SALE_4, "Downsizing my home"),
        (REASON_FOR_SALE_5, "Retiring"),
        (REASON_FOR_SALE_6, "Other"),
    ]

    SALE_SCHEDULE_1 = "ASAP"
    SALE_SCHEDULE_2 = "1 - 3 months"
    SALE_SCHEDULE_3 = "3 - 6 months"
    SALE_SCHEDULE_4 = "6 - 12 months"
    SALE_SCHEDULE_5 = "Just need Info"

    SALE_SCHEDULE_CHOICES = [
        (SALE_SCHEDULE_1, "ASAP"),
        (SALE_SCHEDULE_2, "1 - 3 months"),
        (SALE_SCHEDULE_3, "3 - 6 months"),
        (SALE_SCHEDULE_4, "6 - 12 months"),
        (SALE_SCHEDULE_5, "Just need Info"),
    ]
    agent = models.ForeignKey(
        "register.Agent", on_delete=models.SET_NULL, null=True, blank=True
    )
    property_address = models.CharField(null=True, blank=True, max_length=255)
    schedule_type = models.CharField(null=True, blank=True, max_length=50)
    meeting_date = models.CharField(null=True, blank=True, max_length=20)
    meeting_time = models.CharField(null=True, blank=True, max_length=10)
    sale_schedule = models.CharField(
        max_length=255, choices=SALE_SCHEDULE_CHOICES, default=SALE_SCHEDULE_1
    )
    property_state = models.CharField(
        max_length=255, choices=PROP_STATE_CHOICES, default=PROP_STATE_1
    )
    reason_for_selling = models.CharField(
        max_length=255, choices=REASON_FOR_SALES_CHOICES, default=REASON_FOR_SALE_1
    )
    working_with_an_agent = models.CharField(null=True, blank=True, max_length=3)
    look_forward_to_buying = models.CharField(null=True, blank=True, max_length=3)
    home_improvements = models.CharField(null=True, blank=True, max_length=255)
    first_name = models.CharField(null=True, blank=True, max_length=100)
    last_name = models.CharField(null=True, blank=True, max_length=100)
    email = models.CharField(null=True, blank=True, max_length=255)
    phone = models.CharField(null=True, blank=True, max_length=100)
    lead_type = models.CharField(null=True, blank=True, max_length=10)
    # is_lead_signed is replaced to 'can_view' on the Contract model.
    is_lead_signed = models.BooleanField(default=False)
    other_information = models.CharField(null=True, blank=True, max_length=255)
    date_created = models.DateTimeField(auto_now_add=True, auto_created=True)
    signature = models.ImageField(blank=True, null=True)
    address_link = models.CharField(null=True, blank=True, max_length=255)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    custom_id = models.TextField(null=True, blank=True)
    date = models.DateField(null=True, blank=True)

    class Meta:
        db_table = "practice_leads_seller_bpo_request"


class PracticeBuyerReportRequest(models.Model):
    agent = models.ForeignKey(
        "register.Agent", on_delete=models.SET_NULL, null=True, blank=True
    )
    custom_id = models.TextField(null=True, blank=True)
    first_name = models.CharField(null=True, blank=True, max_length=100)
    last_name = models.CharField(null=True, blank=True, max_length=100)
    email = models.CharField(null=True, blank=True, max_length=255)
    phone = models.CharField(null=True, blank=True, max_length=100)
    other_information = models.CharField(null=True, blank=True, max_length=255)
    date_created = models.DateTimeField(auto_now_add=True, auto_created=True)

    class Meta:
        db_table = "practice_leads_buyer_bpo_request"
