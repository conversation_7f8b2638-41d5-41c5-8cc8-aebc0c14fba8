
# Updated signals.py content:
from django.core.serializers import serialize
from django.db.models.signals import post_save
from leads.tasks.lead_mg import notify_lead_system, models_to_connect, get_category_by_key, LEAD_CATEGORY, logger
from leads.tasks.lead_mg import Leads
from django.db import transaction
import json
from typing import Optional, Dict, Any


def safe_serialize_instance(instance) -> Optional[Dict[str, Any]]:
    """Safely serialize a Django model instance."""
    try:
        if instance is None:
            return None

        serialized_data = serialize('json', [instance])
        parsed_data = json.loads(serialized_data)

        if parsed_data and len(parsed_data) > 0:
            return parsed_data[0]['fields']

        logger.warning(f"Empty serialization result for instance: {instance}")
        return None

    except Exception as e:
        logger.error(f"Failed to serialize instance {instance}: {str(e)}")
        return None


@transaction.atomic
def send_leads(sender, instance: Leads, created: bool, **kwargs):
    try:
        sender_name = sender.__name__

        # Safely serialize the main instance
        lead_data = safe_serialize_instance(instance)
        if lead_data is None:
            logger.error(f"Failed to serialize lead data for {sender_name}")
            return

        # Handle agent data safely
        agent_data = None

        try:
            if hasattr(instance, 'agent') and instance.agent is not None:
                # Only include agent data if it's not an "all agents" category
                if LEAD_CATEGORY.ALL_AGENTS != get_category_by_key(sender_name):
                    agent_data = safe_serialize_instance(instance.agent)
                    if agent_data is None:
                        logger.warning(
                            f"Failed to serialize agent data for {sender_name}, proceeding without agent data")
            else:
                logger.info(f"No agent associated with this lead: {sender_name}")
        except Exception as agent_error:
            logger.warning(
                f"Error handling agent data for {sender_name}: {str(agent_error)}, proceeding without agent data")
            agent_data = None

        # Combine the data

        serializable_data = {
            **lead_data,
            'agent': agent_data
        }

        # Add some metadata for debugging
        serializable_data['_metadata'] = {
            'sender': sender_name,
            'has_agent': agent_data is not None,
            'created': created
        }

        transaction.on_commit(lambda: notify_lead_system.delay(sender_name, created, serializable_data))

    except Exception as e:
        logger.error(f"Error sending lead data to LMS for {sender.__name__}: {str(e)}", exc_info=True)


def connect_signals():
    for model in models_to_connect:
        post_save.connect(send_leads, sender=model)
