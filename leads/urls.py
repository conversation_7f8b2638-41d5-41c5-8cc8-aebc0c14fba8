from django.urls import path
from leads import views


urlpatterns = [
    path(r"report/nod/", views.OrderedLeads.as_view()),
    path(r"report/create-nod/", views.OrderedCreateView.as_view()),
    path(r"report/nod/<pk>/", views.DetailLeads.as_view()),

    path(r"report/", views.ClientReportRequestView.as_view()), # This is the view for creating a report request. Starts from here
    path("send_report_email/", views.SendReportMailView.as_view()),
    path(r"report/<int:pk>/", views.ClientReportRequestDetailView.as_view()), # This is the view for getting a report request detail
    path(r"practice-report/<int:pk>/", views.PracticeClientReportRequestDetailView.as_view()), # This is the view for getting a report request detail

    path(r"report/search/", views.ClientReportRequestListView.as_view()), # This is the view for searching BPO report requests
    path(r"report/practice-search/", views.PracticeClientReportRequestListView.as_view()), # This is the view for searching BPO report requests

    path("search-report/", views.ClientReportRequestListViewWithoutPaginations.as_view()),
    path(r"properties/", views.SellerReportRequestListView.as_view()),
    path(r"property/request/", views.SellerReportRequestView.as_view()),
    path(r"property/request/<pk>/", views.DetailSellerReportRequest.as_view()),
    path(r"property/create-request/", views.SellerReportRequestCreateView.as_view()),

    path("update_listings/", views.GetGarageFromParcelToComps.as_view()),
    path("get_listings/", views.GetClosedListings.as_view(), name="get_listings"),
    path("query_leads/", views.LeadsFilter.as_view(), name="query_leads"),
    path("create-probate/", views.ProbateLeadsCreateView.as_view(), name="create_probate"),
    path("probate/", views.ProbateLeadsSearchView.as_view(), name="probate"),
    path("probate/<pk>/", views.DetailProbate.as_view(), name="probate_detail"),
    path("create-divorce/", views.DivorceLeadsCreateView.as_view(), name="create_divorce"),
    path("divorce/<pk>/", views.DetailDivorce.as_view(), name="divorce_detail"),
    path("divorce/", views.DivorceLeadsSearchView.as_view(), name="divorce"),
    path("create-expired/", views.ExpiredLeadsCreateView.as_view(), name="create_expired"),
    path("expired/", views.ExpiredLeadsSearchView.as_view(), name="expired"),
    path("expired/<pk>/", views.DetailExpired.as_view(), name="expired_detail"),

    path("canceled/", views.CanceledLeadsSearchView.as_view(), name="canceled"),
    path("canceled/<pk>/", views.DetailCanceled.as_view(), name="canceled_detail"),

    path("withdrawn/", views.WithdrawnLeadsSearchView.as_view(), name="withdrawn"),
    path("withdrawn/<pk>/", views.DetailWithdrawn.as_view(), name="withdrawn_detail"),


    path("create-fsbo/", views.FsboLeadsCreateView.as_view(), name="create_fsbo"),
    path("fsbo/", views.FsboLeadsSearchView.as_view(), name="fsbo"),
    path("fsbo/<pk>/", views.DetailFsbo.as_view(), name="fsbo_detail"),
    path("share-property/", views.ShareProperty.as_view(), name="share_property"),

    path("interior/<int:pk>/", views.InteriorView.as_view()),
    path("exterior/<int:pk>/", views.ExteriorView.as_view()),

    path("fetch-zillow-data-active-closed/", views.FetchZillowDataForClosedActive.as_view()),
    path("leads_images/", views.SaveLeadsImages.as_view()),
    path('get_delivery_distance/', views.GetDiliveryDistance.as_view()),
    path('check_pending_report/', views.CheckPendingReport.as_view()), # performs a check on pending reports
    path('summary_report/', views.CheckReportSummary.as_view()),
    path('create-lead-magnet/', views.LeadsMagnetsCreateView.as_view()),
    path('lead-magnet/<pk>/', views.DetailLeadMagnet.as_view()),
    path('list-lead-magnet/', views.LeadsMagnetsListView.as_view()),
    

    path(r"property/create-buy/", views.BuyerReportRequestCreateView.as_view()),
    path(r"property/buy/<pk>/", views.DetailBuyerReportRequest.as_view()),
]
