#!/usr/bin/env bash

set -e

cluster=$1


aws ecs run-task \
--cluster=$cluster \
--task-definition=bpohomes-$cluster-createdefaultcrmls \
--count=1 \
--launch-type=FARGATE \
--network-configuration="awsvpcConfiguration={subnets=[subnet-073f3eb7993cadd41, subnet-0a0bfc01de0801481, subnet-094fea93cf26bd951, subnet-0b1b9dc020fbd40e0],securityGroups=[sg-0c3eb9b80965bc54e],assignPublicIp=ENABLED}" \
--output=json

aws ecs run-task \
--cluster=$cluster \
--task-definition=bpohomes-$cluster-createdefaultmls \
--count=1 \
--launch-type=FARGATE \
--network-configuration="awsvpcConfiguration={subnets=[subnet-073f3eb7993cadd41, subnet-0a0bfc01de0801481, subnet-094fea93cf26bd951, subnet-0b1b9dc020fbd40e0],securityGroups=[sg-0c3eb9b80965bc54e],assignPublicIp=ENABLED}" \
--output=json

aws ecs run-task \
--cluster=$cluster \
--task-definition=bpohomes-$cluster-createsoldmls \
--count=1 \
--launch-type=FARGATE \
--network-configuration="awsvpcConfiguration={subnets=[subnet-073f3eb7993cadd41, subnet-0a0bfc01de0801481, subnet-094fea93cf26bd951, subnet-0b1b9dc020fbd40e0],securityGroups=[sg-0c3eb9b80965bc54e],assignPublicIp=ENABLED}" \
--output=json

aws ecs run-task \
--cluster=$cluster \
--task-definition=bpohomes-$cluster-createsoldcrmls \
--count=1 \
--launch-type=FARGATE \
--network-configuration="awsvpcConfiguration={subnets=[subnet-073f3eb7993cadd41, subnet-0a0bfc01de0801481, subnet-094fea93cf26bd951, subnet-0b1b9dc020fbd40e0],securityGroups=[sg-0c3eb9b80965bc54e],assignPublicIp=ENABLED}" \
--output=json

aws ecs run-task \
--cluster=$cluster \
--task-definition=bpohomes-$cluster-createopenhousecrmls \
--count=1 \
--launch-type=FARGATE \
--network-configuration="awsvpcConfiguration={subnets=[subnet-073f3eb7993cadd41, subnet-0a0bfc01de0801481, subnet-094fea93cf26bd951, subnet-0b1b9dc020fbd40e0],securityGroups=[sg-0c3eb9b80965bc54e],assignPublicIp=ENABLED}" \
--output=json

aws ecs run-task \
--cluster=$cluster \
--task-definition=bpohomes-$cluster-createopenhousemls \
--count=1 \
--launch-type=FARGATE \
--network-configuration="awsvpcConfiguration={subnets=[subnet-073f3eb7993cadd41, subnet-0a0bfc01de0801481, subnet-094fea93cf26bd951, subnet-0b1b9dc020fbd40e0],securityGroups=[sg-0c3eb9b80965bc54e],assignPublicIp=ENABLED}" \
--output=json