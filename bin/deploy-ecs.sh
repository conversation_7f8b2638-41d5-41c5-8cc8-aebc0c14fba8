#!/usr/bin/env bash
set -e

# http://stackoverflow.com/questions/821396/aborting-a-shell-script-if-any-command-returns-a-non-zero-value
set -e
BRANCH=$1

case "$BRANCH" in
'develop') cluster=testing ;;
'staging') cluster=staging ;;
'main') cluster=production ;;
*) cluster= ;;
esac

if [ -z "$cluster" ]; then
    echo "Not deploying branch $BRANCH."
    exit 0
else
  # grab config from remote...
  # export AWS_STORAGE_BUCKET_NAME=$(aws ssm get-parameter --name "/bpohomes/backend/$cluster/aws_storage_bucket_name" --query "Parameter.Value" | sed s/\"//g)
  # echo "======================================"
  # echo "Collecting static assets"
  # echo "======================================"
  # python manage.py collectstatic --noinput

  aws ecs update-service --cluster=$cluster --service="bpohomes-$cluster-web" --force-new-deployment
  aws ecs update-service --cluster=$cluster --service="bpohomes-$cluster-worker" --force-new-deployment
  ./bin/create-migration-task.sh $cluster

#wait for deployments to complete
  echo "Waiting for bpohomes $cluster deployments to complete..."
  aws ecs wait services-stable --cluster=$cluster --services "bpohomes-$cluster-web"
  aws ecs wait services-stable --cluster=$cluster --services "bpohomes-$cluster-worker"
  echo "bpohomes $cluster deployments complete..."
fi
