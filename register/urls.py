# register/urls.py
from django.urls import path
from rest_framework_nested import routers
from register import views

router = routers.DefaultRouter()
router.register("clients", views.ClientViewSet)
router.register("agents", views.AgentViewSet, basename="agents")
router.register("get_agents", views.GetAgents, basename="get_agents")

router.register("farm_prices", views.FarmPriceViewSet)
router.register("languages", views.LanguageViewSet)
router.register("professional_types", views.ProfessionalTypeViewSet)


agents_router = routers.NestedDefaultRouter(router, "agents", lookup="agent")
agents_router.register("reviews", views.AgentReviewViewSet, basename="agent-reviews")


other_paths = [
    path("send_client_review/", views.SendClientReview.as_view()),
    path("export_agents/", views.ExportAgents.as_view()),
    path("export_free_agents/", views.ExportPartnerAgents.as_view()),
    path("export_farm_agent/", views.ExportTerritoryAgent.as_view()),
    path("change_set_password/", views.SetDefaultPassword.as_view()),
    path("send_admin_email/", views.GetBoughtTerritories.as_view()),
    path("send_a_territory_email/", views.GetABoughtTerritory.as_view()),
    path("send_subscription_admin_email/", views.GetBoughtSubscription.as_view()),
    path("contact_agent/", views.ContactAgentView.as_view()),
    path("support-ticket/", views.TicketView.as_view()),
    path("contact_agent/<pk>/", views.DetailContactAgent.as_view()),
    path("get-contact-agent/", views.GetContactAgentView.as_view()),
    path("random_paid_agent/", views.RandomPaidAgent.as_view()),
    path("leads_notification/<int:agent_id>/", views.LeadNotificationViewSet.as_view()),
    path("stripe_config/", views.StripeConfigView.as_view()),
    path("list_prices/", views.PriceListView.as_view()),
    path("list_products/", views.ProductListView.as_view()),
    path("get_product/<product_id>", views.ProductRetrieveView.as_view()),
    path("add_payment_method/", views.AddPaymentMethodView.as_view()),
    path("create_subscription/", views.CheckoutSessionView.as_view()),
    path("expired_subscriptions/", views.ExpiredSubscriptionListView.as_view()),
    path("unpaid_invoices/", views.UnpaidInvoiceView.as_view()),
    path("all_invoices/", views.AllInvoiceView.as_view()),
    path("get_invoices/", views.GetUserInvoiceView.as_view()),
    path("pay_invoice/", views.PayInvoiceView.as_view()),
    path("subscriptions/", views.SubscriptionListView.as_view()),
    path("update_subscription/", views.SubscriptionUpdateView.as_view()),
    path("get_payment_history/", views.PaymentHistoryView.as_view()),
    path("get_payment_method/", views.RetrievePaymentMethodView.as_view()),
    path("assign-farm-agent/", views.AssignFarmAgent.as_view()),
    path("webhook/", views.WebhookAPIView.as_view()),
    path("generate-upgrade-downgrade-invoice/", views.GenerateDowngradeUpgradeIvoice.as_view()),
    path("pay-upgrade-downgrade-invoice/", views.PayDowngradeUpgradeIvoice.as_view()),
    path(
        "search_realtyagent/",
        views.SearchRealtyAgent.as_view(),
        name="search_realtyagent",
    ),

    path("agent-list/", views.AgentLists.as_view()),

    path("practice_bporeport_confirmation/", views.PracticeBPOReportPaymentConfirmation.as_view()), 
    path("bporeport_confirmation/", views.BPOReportPaymentConfirmation.as_view()), 

    path("confirm-internship-payment/", views.ConfirmInternshipPayment.as_view()),

    path("practice_check_client_report_status/", views.PracticeCheckClientReportStatus.as_view()),
    path("check_client_report_status/", views.CheckClientReportStatus.as_view()),
    
    path("import-investors/", views.ImportInvesters.as_view()),
    path("check_investor_status/", views.CheckInvestorStatus.as_view()),
    path("ind-prof/", views.ProfessionRegisterView.as_view()),
    path("ind-prof/user/profile/", views.ProfessionUserProfileView.as_view()),
    path("ind-prof/profile/<pk>/", views.ProfessionProfileView.as_view()),
    path("ind-prof/profile/", views.GetProfessionProfileView.as_view()),
    path("ind-prof/create-profile/", views.ProfessionCreateView.as_view()),

    path("user-agent/<str:email>/", views.UserAgentRetrieveView.as_view(), name='user_agent'),




]
# URLConf
urlpatterns = router.urls + agents_router.urls + other_paths
