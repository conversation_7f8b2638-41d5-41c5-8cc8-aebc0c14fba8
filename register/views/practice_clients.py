import stripe
import logging
from django.conf import settings
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework import status
from rest_framework.exceptions import NotFound
from leads.models import (
    PracticeSessionReportTierPayment,
    PracticeAgentCustomerSubscription,
    PracticeClientBpoRequest,
)
from leads.serializers import PracticeRetrieveReportSerializer
from register.models import (
    Agent,
    Client,
)
from register.tasks import (
    send_client_review_email_task,
    bpo_agent_notify_task,
    client_bpo_report_request_task,
)
from register import serializers

stripe.api_key = settings.STRIPE_SECRET_KEY
stripe.api_version = settings.STRIPE_API_VERSION
# Get an instance of a logger
logger = logging.getLogger("stripe_logs")
basic_logger = logging.getLogger("aws_logger")


class PracticeClientViewSet(ModelViewSet):
    USER_ROLE = "client"
    queryset = Client.objects.all()

    def get_serializer_class(self):
        if self.action == "create":
            return serializers.ClientCreateSerializer
        if self.action in ("list", "retrieve"):
            return serializers.ClientRetrieveSerializer
        return serializers.ClientSerializer

    @action(
        detail=False,
        methods=["GET", "PUT", "PATCH"],
        permission_classes=[IsAuthenticated],
    )
    def me(self, request):
        user = request.user
        if user.role == self.USER_ROLE:
            try:
                client = Client.objects.get(user_id=request.user.id)
            except Client.DoesNotExist:
                raise NotFound
            if request.method == "GET":
                serializer = serializers.ClientSerializer(client)
                return Response(serializer.data)
            elif request.method == "PUT":
                serializer = serializers.ClientSerializer
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)


class PracticeSendClientReview(APIView):
    permission_classes = []

    def post(self, request):
        agent_id = request.data.get("agent_id", None)
        emails = list(request.data.get("emails", None))

        agent = Agent.objects.get(id=agent_id)
        agent_image = agent.profile_image.url

        try:
            send_client_review_email_task.delay(
                agent_id=agent_id,
                emails=emails,
                agent_image=agent_image,
                agent_name=agent.user.first_name + " " + agent.user.last_name,
            )
            return Response(
                "Review message sent successfully", status=status.HTTP_200_OK
            )

        except Exception:
            return Response(
                "Could not send. Something went wrong", status=status.HTTP_200_OK
            )


class PracticeBPOReportPaymentConfirmation(APIView):
    def post(self, request):
        import random
        import uuid

        RANDOM_ADDRESSES = [
            "123 Elm Street, Springfield, IL",
            "456 Oak Avenue, Denver, CO",
            "789 Maple Lane, Austin, TX",
            "321 Pine Boulevard, Miami, FL",
            "987 Cedar Court, Seattle, WA",
        ]

        RANDOM_REASONS = [
            "Investment purposes",
            "Selling my home",
            "For refinancing",
            "Mortgage assessment",
            "I'm buying a property",
        ]

        # Randomize specific fields
        clients_physical_address = random.choice(RANDOM_ADDRESSES)
        reason_for_request = random.choice(RANDOM_REASONS)
        session_id = f"cs_test_{uuid.uuid4().hex[:32]}"

        # clients_physical_address = request.data.get("clients_physical_address", None)
        # reason_for_request = request.data.get("reason_for_request", None)
        client_register_id = request.data.get("client_register_id", None)
        payment_status = "Complete"  # Assuming a default payment status for practice
        # payment_status = request.data.get("payment_status", None)
        agent_id = request.data.get("agent_id", None)
        client_id = request.data.get("client_id", None)
        tier = "1"  # Assuming a default tier for practice
        # tier = request.data.get("tier", None)
        drive_amount = "0"  # Assuming a default drive amount for practice
        # drive_amount = request.data.get("drive_amount", None)
        # session_id = request.data.get("session_id", None)
        get_status = "one_time"
        # get_status = request.data.get("status", None)
        short_uuid = request.data.get("short_uuid", None)

        if get_status == "one_time":
            try:
                create_onetime_report_tier_payment = (
                    PracticeSessionReportTierPayment.objects.create(session_id=session_id)
                )
            except Exception as e:
                return Response(
                    f"creating payment {e}", status=status.HTTP_400_BAD_REQUEST
                )

            try:
                create_report = PracticeClientBpoRequest.objects.create(
                    clients_physical_address=clients_physical_address,
                    reason_for_request=reason_for_request,
                    client_register_id=client_register_id,
                    payment_status=payment_status,
                    agent_id=agent_id,
                )
                serializer = PracticeRetrieveReportSerializer(create_report, many=False)
            except Exception as e:
                return Response(
                    f"creating report {e}", status=status.HTTP_400_BAD_REQUEST
                )

            try:
                get_agent = Agent.objects.get(id=agent_id)
                if get_agent.discount_percent > 0:
                    final_amount_dollars = get_agent.report_price - (
                        get_agent.report_price * (get_agent.discount_percent / 100)
                    )
                    get_agent_amount = final_amount_dollars
                else:
                    get_agent_amount = get_agent.report_price

                drive_amount_dollars = float(drive_amount) / 100

                create_agent_custumer_sub = PracticeAgentCustomerSubscription.objects.create(
                    payment_session=create_onetime_report_tier_payment,
                    agent_id=agent_id,
                    client_id=client_id,
                    tier=int(tier),
                    number_of_report=0,
                    status="One Time",
                    amount=get_agent_amount,
                    sub_ended=True,
                    drive_amount=drive_amount_dollars,
                )

                create_agent_custumer_sub.report.add(create_report)
            except Exception as e:
                return Response(
                    f"Agent creation {e}",
                    status=status.HTTP_400_BAD_REQUEST,
                )

            get_agent = Agent.objects.get(id=agent_id)
            get_client = Client.objects.get(id=client_id)
            try:
                bpo_agent_notify_task.delay(
                    customer_name=get_client.user.first_name,
                    customer_phone=get_client.phone,
                    customer_email=get_client.user.email,
                    property_address=clients_physical_address,
                    agent_email=get_agent.user.email,
                    reason=reason_for_request,
                )
            except Exception:
                logger.info("Could not send bpo agent notify email")

            try:
                client_bpo_report_request_task.delay(
                    client_name=get_client.user.first_name,
                    client_email=get_client.user.email,
                )
            except Exception:
                logger.info("Could not send bpo report email")

        if get_status == "subscription":
            try:
                get_sub = PracticeAgentCustomerSubscription.objects.get(
                    payment_session__session_id=short_uuid
                )

                create_report = PracticeClientBpoRequest.objects.create(
                    clients_physical_address=clients_physical_address,
                    reason_for_request=reason_for_request,
                    client_register_id=client_register_id,
                    payment_status=payment_status,
                    agent_id=agent_id,
                    short_uuid=short_uuid,
                )
                get_sub.number_of_report = get_sub.number_of_report - 1
                get_sub.save()

                get_sub.report.add(create_report)
                serializer = PracticeRetrieveReportSerializer(create_report, many=False)
            except Exception:
                create_report = PracticeClientBpoRequest.objects.create(
                    clients_physical_address=clients_physical_address,
                    reason_for_request=reason_for_request,
                    client_register_id=client_register_id,
                    payment_status=payment_status,
                    agent_id=agent_id,
                    short_uuid=short_uuid,
                )
                # get_sub.number_of_report = get_sub.number_of_report - 1
                # get_sub.save()
                serializer = PracticeRetrieveReportSerializer(create_report, many=False)

            get_agent = Agent.objects.get(id=agent_id)
            get_client = Client.objects.get(id=client_id)
            try:
                bpo_agent_notify_task.delay(
                    customer_name=get_client.user.first_name,
                    customer_phone=get_client.phone,
                    customer_email=get_client.user.email,
                    property_address=clients_physical_address,
                    agent_email=get_agent.user.email,
                    reason=reason_for_request,
                )
            except Exception:
                logger.info("Could not send bpo agent notify email")

            try:
                client_bpo_report_request_task.delay(
                    client_name=get_client.user.first_name,
                    client_email=get_client.user.email,
                )
            except Exception:
                logger.info("Could not send bpo report email")

        return Response(serializer.data)




class PracticeCheckClientReportStatus(APIView):
    def post(self, request):
        agent_id = request.data.get("agent_id", None)
        clients_physical_address = request.data.get("clients_physical_address", None)
        reason_for_request = request.data.get("reason_for_request", None)
        client_register_id = request.data.get("client_register_id", None)
        payment_status = request.data.get("payment_status", None)
        client_id = request.data.get("client_id")

        if PracticeAgentCustomerSubscription.objects.filter(
            agent_id=agent_id,
            client_id=client_id,
            number_of_report__gt=0,
            status="Subscription",
        ).exists():
            get_sub = PracticeAgentCustomerSubscription.objects.filter(
                agent_id=agent_id,
                client_id=client_id,
                number_of_report__gt=0,
                status="Subscription",
            ).first()

            create_report = PracticeClientBpoRequest.objects.create(
                clients_physical_address=clients_physical_address,
                reason_for_request=reason_for_request,
                client_register_id=client_register_id,
                payment_status=payment_status,
                agent_id=agent_id,
            )
            get_sub.number_of_report = get_sub.number_of_report - 1
            get_sub.save()
            serializer = PracticeRetrieveReportSerializer(create_report, many=False)
            return Response(serializer.data)
        else:
            return Response({"subscription": False}, status=status.HTTP_200_OK)
