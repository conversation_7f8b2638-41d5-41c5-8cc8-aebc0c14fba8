from django.db import models
from django.utils import timezone
from datetime import datetime
from io import BytesIO
import sys
from PIL import Image
from django.core.files.uploadedfile import InMemoryUploadedFile


now = timezone.now()


# Treat as abstract
class Customer(models.Model):
    CAT_REAL_ESTATE_AGENT = "real_estate_agent"
    CAT_INVESTOR = "investor"
    CAT_HOME_IMPROVEMENT = "home_improvement"
    CAT_PHOTOGRAPHER = "photographer"
    CAT_HOME_BUILDER = "home_builder"
    CAT_HOME_INSPECTOR = "home_inspector"
    CAT_PROPERTY_MANAGER = "property_manager"
    CAT_OTHER_REAL_ESTATE = "real_estate_prof"

    CAT_CHOICES = [
        (CAT_REAL_ESTATE_AGENT, "Real Estate Agent/Broker"),
        (CAT_INVESTOR, "Investor"),
        (CAT_HOME_IMPROVEMENT, "Home Improvement Services"),
        (CAT_PHOTOGR<PERSON>HER, "Photographer"),
        (CAT_HOME_BUILDER, "Home Builder"),
        (CAT_HOME_INSPECTOR, "Home Inspector"),
        (CAT_PROPERTY_MANAGER, "Property Manager"),
        (CAT_OTHER_REAL_ESTATE, "Other Real Estate Professional"),
    ]

    category = models.CharField(
        max_length=255, choices=CAT_CHOICES, default=CAT_INVESTOR
    )
    profile_image = models.ImageField(upload_to="profile_images", null=True)
    bio = models.TextField(null=True)
    title = models.CharField(max_length=255, null=True)
    brokerage_name = models.CharField(max_length=255, null=True)
    brokerage_address = models.CharField(max_length=255, null=True)
    city = models.CharField(max_length=255, null=True)
    state = models.CharField(max_length=255, null=True)
    zip_code = models.CharField(max_length=255, null=True)
    phone = models.CharField(max_length=255, null=True)
    brokerage_phone = models.CharField(max_length=255, null=True)
    screen_name = models.CharField(max_length=255, null=True)
    timezone = models.CharField(max_length=255, null=True)
    video_link = models.URLField(blank=True, null=True)
    year_work_commenced = models.DateField(null=True)
    web_link = models.URLField(blank=True, null=True)
    blog = models.URLField(blank=True, null=True)
    facebook = models.URLField(blank=True, null=True)
    twitter = models.URLField(blank=True, null=True)
    youtube = models.URLField(blank=True, null=True)
    linkedin = models.URLField(blank=True, null=True)
    instagram = models.URLField(blank=True, null=True)
    stripe_cus_id = models.CharField(max_length=255, null=True)
    is_internship = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)

    @property
    def number_of_years(self):
        if self.year_work_commenced:
            today = datetime.today().date()
            years = today.year - self.year_work_commenced.year
            if today.month < self.year_work_commenced.month or (
                today.month == self.year_work_commenced.month
                and today.day < self.year_work_commenced.day
            ):
                years -= 1
            return years
        else:
            return None

    def save(self, *args, **kwargs):
        # Opening the uploaded image
        try:
            im = Image.open(self.profile_image)

            output = BytesIO()

            # Resize/modify the image
            base_width = 360
            width_percent = base_width / float(im.size[0])
            hsize = int((float(im.size[1]) * float(width_percent)))
            im = im.resize((base_width, hsize), Image.ANTIALIAS)

            # after modifications, save it to the output
            im.save(output, format="JPEG", quality=90)
            output.seek(0)

            # change the imagefield value to be the newley modifed image value
            self.profile_image = InMemoryUploadedFile(
                output,
                "ImageField",
                "%s.jpg" % self.profile_image.name.split(".")[0],
                "image/jpeg",
                sys.getsizeof(output),
                None,
            )

            super(Customer, self).save(*args, **kwargs)
        except Exception:
            super(Customer, self).save(*args, **kwargs)


class Language(models.Model):
    name = models.CharField(max_length=255)
    customer = models.ManyToManyField(Customer, related_name="languages")


class MailAddress(models.Model):
    name = models.CharField(max_length=255)
    suite_number = models.CharField(max_length=255, null=True)
    customer = models.ManyToManyField(Customer, related_name="mail_addresses")


class Specialty(models.Model):
    title = models.CharField(max_length=255)
    customer = models.ManyToManyField(Customer, related_name="specialties")

    class Meta:
        verbose_name_plural = "specialties"


class ServiceArea(models.Model):
    location = models.CharField(max_length=255)
    customer = models.ManyToManyField(Customer, related_name="service_areas")


class ProfessionalType(models.Model):
    title = models.CharField(max_length=255)
    subscribable = models.BooleanField()


class License(models.Model):
    CAT_AGENT = "real_estate_agent"
    CAT_OTHER = "other_profession"
    CAT_PROF = "ind_prof"

    CAT_CHOICES = [(CAT_AGENT, "Real Estate Agent"), (CAT_OTHER, "Other Profession"), (CAT_PROF, CAT_PROF)]
    state = models.CharField(max_length=255, null=True)
    number = models.CharField(max_length=255)
    category = models.CharField(max_length=255, choices=CAT_CHOICES, default=CAT_AGENT)
    description = models.CharField(max_length=255, null=True)
    expiration = models.DateField(null=True)
    customer = models.ForeignKey(
        Customer, on_delete=models.CASCADE, related_name="licenses"
    )


class ContactAgent(models.Model):
    agent = models.ForeignKey(
        "register.Agent", on_delete=models.SET_NULL, null=True, blank=True, related_name="contact_agent"
    )
    client_name = models.CharField(max_length=255, null=True)
    client_phone = models.CharField(max_length=255, null=True)
    client_email = models.CharField(max_length=255, null=True)
    is_verified = models.BooleanField(default=False)
    client_message = models.TextField(null=True)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, blank=True, null=True)
    date = models.DateField(null=True, blank=True)
    preferred_move = models.CharField(max_length=255, null=True, blank=True)
    source_url = models.URLField(null=True, blank=True)
