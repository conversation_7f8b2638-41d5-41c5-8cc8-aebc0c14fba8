from rest_framework import serializers

from contract.errors import ContractAlreadySigned
from contract.models import Contract


class CreateReferralAgreementContractSerializer(serializers.ModelSerializer):

    class Meta:
        model = Contract
        extra_kwargs = {"user": {"read_only": True}, "lead": {"read_only": True}}
        exclude = ['user', 'lead']

    def create(self, validated_data):
        user = self.context["user"]
        lead = self.context["lead"]

        # if Contract.objects.filter(user=user, lead=lead):
        #     raise ContractAlreadySigned

        validated_data.update({"user": user, "lead": lead})
        instance = super(CreateReferralAgreementContractSerializer, self).create(validated_data)

        return instance


class RetrieveReferralAgreementContractSerializer(serializers.ModelSerializer):

    class Meta:
        model = Contract
        exclude = ['user', 'lead']


class UpdateReferralAgreementContractSerializer(serializers.ModelSerializer):

    class Meta:
        model = Contract
        exclude = ['user', 'lead']
