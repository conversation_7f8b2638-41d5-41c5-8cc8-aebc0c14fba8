from django.conf import settings
from celery import shared_task
from celery.utils.log import get_task_logger
from templated_mail.mail import BaseEmailMessage
logger = get_task_logger(__name__)


@shared_task()
def send_email_contract(instance):
    logger.info("Sending Emails to clients and operations")
    recipient = settings.ADMINS[0][1]

    recipient_agent_email = instance['recipient_agent_email']
    context = {
        "header": f"Contract Signed By {recipient_agent_email}",
        "recipient": "Operations Manager",
        "agent_email": instance['recipient_agent_email'],
        "agent_name": instance['recipient_agent_name'],
        "lead": instance['referring_broker_address'],
        "contract_url": instance['docfile']
    }
    message = BaseEmailMessage(
        template_name="emails/contract_signed.html", context=context
    )
    message.send([recipient])
    logger.info(f"Sent emails to admin: {recipient} successfully")
    return True
