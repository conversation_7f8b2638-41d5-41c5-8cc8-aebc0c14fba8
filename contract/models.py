import logging
from django.conf import settings
from django.db import models
from leads.models import SellerReportRequest

logger = logging.getLogger("aws_logger")


class Contract(models.Model):
    id = models.AutoField(primary_key=True)
    referring_broker_name = models.CharField(null=True, blank=True, max_length=150)
    referring_broker_email = models.CharField(null=True, blank=True, max_length=150)
    referring_broker_phone = models.CharField(null=True, blank=True, max_length=100)
    referring_broker_address = models.CharField(null=True, blank=True, max_length=200)
    recipient_broker_name = models.CharField(null=True, blank=True, max_length=150)
    recipient_broker_email = models.CharField(null=True, blank=True, max_length=150)
    recipient_broker_phone = models.CharField(null=True, blank=True, max_length=100)
    recipient_broker_address = models.Char<PERSON><PERSON>(null=True, blank=True, max_length=200)
    recipient_agent_name = models.Char<PERSON><PERSON>(null=True, blank=True, max_length=150)
    recipient_agent_email = models.Char<PERSON>ield(null=True, blank=True, max_length=150)
    recipient_agent_phone = models.CharField(null=True, blank=True, max_length=100)
    recipient_agent_address = models.Char<PERSON>ield(null=True, blank=True, max_length=200)
    signature = models.ImageField(upload_to="contract/signature", blank=True, null=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="assigned_user",
        null=True
    )
    lead = models.ForeignKey(SellerReportRequest, null=True, blank=True, on_delete=models.DO_NOTHING)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    docfile = models.FileField(upload_to="contract/documents/%Y/%m/%d/", null=True, blank=True)

    class Meta:
        db_table = "referral_fee_agreement_contract"
