# Generated by Django 3.2.15 on 2022-11-28 16:10

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('referring_broker_name', models.Char<PERSON>ield(blank=True, max_length=150, null=True)),
                ('referring_broker_email', models.CharField(blank=True, max_length=150, null=True)),
                ('referring_broker_phone', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('referring_broker_address', models.Char<PERSON>ield(blank=True, max_length=200, null=True)),
                ('recipient_broker_name', models.Char<PERSON>ield(blank=True, max_length=150, null=True)),
                ('recipient_broker_email', models.Char<PERSON><PERSON>(blank=True, max_length=150, null=True)),
                ('recipient_broker_phone', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('recipient_broker_address', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=200, null=True)),
                ('recipient_agent_name', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=150, null=True)),
                ('recipient_agent_email', models.Char<PERSON>ield(blank=True, max_length=150, null=True)),
                ('recipient_agent_phone', models.CharField(blank=True, max_length=100, null=True)),
                ('recipient_agent_address', models.CharField(blank=True, max_length=200, null=True)),
                ('signature', models.FileField(upload_to='')),
            ],
            options={
                'db_table': 'referral_fee_agreement_contract',
            },
        ),
    ]
