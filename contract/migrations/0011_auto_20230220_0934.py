# Generated by Django 3.2.15 on 2023-02-20 09:34

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('contract', '0010_contract_lead'),
    ]

    operations = [
        migrations.AddField(
            model_name='contract',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='contract',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
