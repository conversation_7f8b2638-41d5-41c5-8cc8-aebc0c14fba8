from django import forms
from contract.models import Contract


class UploadFileForm(forms.ModelForm):
    referring_broker_name = forms.Char<PERSON><PERSON>(max_length=50)
    referring_broker_email = forms.Char<PERSON><PERSON>(max_length=150)
    referring_broker_phone = forms.Char<PERSON><PERSON>(max_length=50)
    referring_broker_address = forms.Char<PERSON>ield(max_length=50)
    recipient_broker_name = forms.Char<PERSON>ield(max_length=50)
    recipient_broker_email = forms.Char<PERSON><PERSON>(max_length=150)
    recipient_broker_phone = forms.Cha<PERSON><PERSON><PERSON>(max_length=50)
    recipient_broker_address = forms.Char<PERSON><PERSON>(max_length=50)
    recipient_agent_name = forms.Char<PERSON><PERSON>(max_length=50)
    recipient_agent_email = forms.Char<PERSON><PERSON>(max_length=150)
    recipient_agent_phone = forms.Char<PERSON><PERSON>(max_length=50)
    recipient_agent_address = forms.Char<PERSON><PERSON>(max_length=50)
    lead_type = forms.Char<PERSON><PERSON>(max_length=50)
    lead_id = forms.Char<PERSON>ield(max_length=50)

    class Meta:
        model = Contract
        fields = [
            'referring_broker_name',
            'referring_broker_email',
            'referring_broker_phone',
            'referring_broker_address',
            'recipient_broker_name',
            'recipient_broker_email',
            'recipient_broker_phone',
            'recipient_broker_address',
            'recipient_agent_name',
            'recipient_agent_email',
            'recipient_agent_phone',
            'recipient_agent_address',
            'lead_type',
            'lead_id',
        ]


class SignaturePicForm(forms.ModelForm):

    class Meta:
        model = Contract
        fields = ['signature', 'docfile', 'recipient_broker_name']
