import json
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.views import APIView
from rest_framework import generics
from rest_framework import status

from deal_analyzer.exceptions.errors import ResourceNotFound

from contract.serializers import (
    CreateReferralAgreementContractSerializer,
    UpdateReferralAgreementContractSerializer,
    RetrieveReferralAgreementContractSerializer
)
from contract.models import Contract
from contract.forms import SignaturePicForm
from contract import tasks

from leads.models import SellerReportRequest
from leads.serializers import SellerBPORequestSerializer


class ReferralFeeAgreementViewset(generics.RetrieveAPIView, APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)
    serializer_class = CreateReferralAgreementContractSerializer
    queryset = Contract.objects.all()
    lookup_field = "pk"

    def get_object(self):
        try:
            pk = self.kwargs.get('pk')
            return Contract.objects.get(id=pk)
        except Contract.DoesNotExist:
            raise ResourceNotFound

    def post(self, request, *args, **kwargs):
        body_unicode = request.body.decode('utf-8')
        body = json.loads(body_unicode)
        lead_id = body['lead_id']
        try:
            lead_instance = SellerReportRequest.objects.get(id=lead_id)
        except SellerReportRequest.DoesNotExist:
            raise ResourceNotFound

        serializier = CreateReferralAgreementContractSerializer(
            data=request.data,  context={"user": request.user, "lead": lead_instance}
        )

        if serializier.is_valid():
            serializier.save()
            seller_serializer = SellerBPORequestSerializer(
                instance=lead_instance, context={"user": request.user}
            ).data

            response = {
                "contract": serializier.data,
                "lead": seller_serializer,
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response(data=serializier.errors, status=status.HTTP_400_BAD_REQUEST)


class RetrieveReferralFeeAgreementViewset(generics.ListAPIView):
    queryset = Contract.objects.all()
    serializer_class = CreateReferralAgreementContractSerializer


class UpdateReferralFeeAgreementViewset(APIView):

    serializer_class = UpdateReferralAgreementContractSerializer
    # queryset = Contract.objects.all()
    lookup_field = "pk"

    def put(self, request, pk, format=None):
        try:
            contract_instance = Contract.objects.get(id=pk)
        except Contract.DoesNotExist:
            raise ResourceNotFound

        contract_form = SignaturePicForm(
            request.POST, request.FILES, instance=contract_instance
        )

        if contract_form.is_valid():
            try:
                contract_form.save()
                contract_serilaizer = RetrieveReferralAgreementContractSerializer(
                            instance=contract_instance
                        ).data
                tasks.send_email_contract.delay(contract_serilaizer)
                return Response({
                    "message": "Signature Updated",
                    "contract": contract_serilaizer
                }, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({
                    "message": "Error Uploading Files. Try Again!",
                    "error": str(e),
                }, status=status.HTTP_400_BAD_REQUEST)

        else:
            return Response({
                "message": contract_form.errors,
            }, status=status.HTTP_400_BAD_REQUEST)
