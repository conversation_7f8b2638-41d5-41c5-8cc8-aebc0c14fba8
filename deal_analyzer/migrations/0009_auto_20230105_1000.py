# Generated by Django 3.2.15 on 2023-01-05 10:00

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0008_customcomps_user'),
    ]

    operations = [
        migrations.AddField(
            model_name='brpropertyinfo',
            name='updated',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='updated',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='updated',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raapropertyinfo',
            name='updated',
            field=models.Char<PERSON>ield(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='updated',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdspropertyinfo',
            name='updated',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
    ]
