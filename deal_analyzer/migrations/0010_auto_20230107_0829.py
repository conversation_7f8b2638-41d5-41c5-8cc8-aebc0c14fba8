# Generated by Django 3.2.15 on 2023-01-07 08:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0009_auto_20230105_1000'),
    ]

    operations = [
        migrations.AddField(
            model_name='brestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='estimatedresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=0, max_length=255, null=True),
        ),
    ]
