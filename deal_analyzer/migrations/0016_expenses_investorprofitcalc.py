# Generated by Django 3.2.15 on 2023-08-09 22:26

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0015_brcustomcomps_nccustomcomps_raacustomcomps_rdsacustomcomps_rdscustomcomps'),
    ]

    operations = [
        migrations.CreateModel(
            name='InvestorProfitCalc',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investor', models.CharField(max_length=255)),
                ('investment_values_dollar_or_percent', models.CharField(choices=[('1', 'Dollar'), ('2', 'Percent')], max_length=1)),
                ('equity_in_value', models.FloatField(null=True)),
                ('equity_in_percent', models.FloatField(null=True)),
                ('profit_in_value', models.FloatField(null=True)),
                ('profit_in_percent', models.FloatField(null=True)),
                ('roi_in_percent', models.FloatField(null=True)),
                ('roi_in_value', models.FloatField(null=True)),
                ('annual_roi', models.FloatField(default=0, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Expenses',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total_liquid_capital_required', models.FloatField(null=True)),
                ('total_expenses', models.FloatField(null=True)),
                ('estimated_net_profit', models.FloatField(null=True)),
                ('model_type', models.CharField(max_length=255, null=True)),
                ('model_id', models.CharField(max_length=255, null=True)),
                ('investors_profit', models.ManyToManyField(to='deal_analyzer.InvestorProfitCalc')),
            ],
        ),
    ]
