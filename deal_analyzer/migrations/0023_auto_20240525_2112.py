# Generated by Django 3.2.15 on 2024-05-25 21:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0022_auto_20240422_1058'),
    ]

    operations = [
        migrations.AddField(
            model_name='brclosingcost',
            name='closing_cost_credit_purchase',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='commission_on_purchase_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='commission_on_resale_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='custom_commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='custom_commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='custom_commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='custom_commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='custom_title_and_escrow_buyer_pays',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='custom_title_and_escrow_buyer_pays_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='custom_title_and_escrow_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='custom_title_and_escrow_on_resale_dollar_or_percent',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='title_and_escrow_fees',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='title_and_escrow_fees_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brclosingcost',
            name='title_and_escrow_fees_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brestresalevalue',
            name='annual_property_appreciation',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brestresalevalue',
            name='appreciation_value_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brestresalevalue',
            name='appreciation_value_option_desired',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brestresalevalue',
            name='resale_value_option_v',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brothercosts',
            name='utilities',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brpropertyinfo',
            name='additional_units',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='brpropertyinfo',
            name='adu',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brpropertyinfo',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brpropertyinfo',
            name='other_property_type',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brremodelcost',
            name='additional_units_price',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='brremodelcost',
            name='adu_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='closing_cost_credit_purchase',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='commission_on_purchase_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='commission_on_resale_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='custom_commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='custom_commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='custom_commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='custom_commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='custom_title_and_escrow_buyer_pays',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='custom_title_and_escrow_buyer_pays_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='custom_title_and_escrow_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='custom_title_and_escrow_on_resale_dollar_or_percent',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='title_and_escrow_fees',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='title_and_escrow_fees_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='closingcost',
            name='title_and_escrow_fees_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='estimatedresalevalue',
            name='annual_property_appreciation',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='estimatedresalevalue',
            name='appreciation_value_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='estimatedresalevalue',
            name='appreciation_value_option_desired',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='estimatedresalevalue',
            name='resale_value_option_v',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='closing_cost_credit_purchase',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='commission_on_purchase_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='commission_on_resale_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='custom_commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='custom_commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='custom_commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='custom_commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='custom_title_and_escrow_buyer_pays',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='custom_title_and_escrow_buyer_pays_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='custom_title_and_escrow_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='custom_title_and_escrow_on_resale_dollar_or_percent',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='title_and_escrow_fees',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='title_and_escrow_fees_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncclosingcost',
            name='title_and_escrow_fees_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncestresalevalue',
            name='annual_property_appreciation',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncestresalevalue',
            name='appreciation_value_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncestresalevalue',
            name='appreciation_value_option_desired',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncestresalevalue',
            name='resale_value_option_v',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncothercosts',
            name='utilities',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='additional_units',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='adu',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='other_property_type',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='othercosts',
            name='utilities',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='additional_units',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='adu',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='other_property_type',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='closing_cost_credit_purchase',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='commission_on_purchase_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='commission_on_resale_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='custom_commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='custom_commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='custom_commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='custom_commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='custom_title_and_escrow_buyer_pays',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='custom_title_and_escrow_buyer_pays_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='custom_title_and_escrow_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='custom_title_and_escrow_on_resale_dollar_or_percent',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='title_and_escrow_fees',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='title_and_escrow_fees_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaclosingcost',
            name='title_and_escrow_fees_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raaestresalevalue',
            name='annual_property_appreciation',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaestresalevalue',
            name='appreciation_value_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaestresalevalue',
            name='appreciation_value_option_desired',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaestresalevalue',
            name='resale_value_option_v',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaothercosts',
            name='utilities',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raapropertyinfo',
            name='additional_units',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='raapropertyinfo',
            name='adu',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raapropertyinfo',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raapropertyinfo',
            name='other_property_type',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaremodelcost',
            name='additional_units_price',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='raaremodelcost',
            name='adu_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='closing_cost_credit_purchase',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='commission_on_purchase_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='commission_on_resale_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='custom_commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='custom_commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='custom_commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='custom_commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='custom_title_and_escrow_buyer_pays',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='custom_title_and_escrow_buyer_pays_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='custom_title_and_escrow_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='custom_title_and_escrow_on_resale_dollar_or_percent',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='title_and_escrow_fees',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='title_and_escrow_fees_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaclosingcost',
            name='title_and_escrow_fees_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaestresalevalue',
            name='annual_property_appreciation',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaestresalevalue',
            name='appreciation_value_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaestresalevalue',
            name='appreciation_value_option_desired',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaestresalevalue',
            name='resale_value_option_v',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaothercosts',
            name='utilities',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='additional_units',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='adu',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='other_property_type',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaremodelcost',
            name='additional_units_price',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaremodelcost',
            name='adu_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='closing_cost_credit_purchase',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='commission_on_purchase_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='commission_on_resale_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='custom_commission_on_purchase',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='custom_commission_on_purchase_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='custom_commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='custom_commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='custom_title_and_escrow_buyer_pays',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='custom_title_and_escrow_buyer_pays_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='custom_title_and_escrow_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='custom_title_and_escrow_on_resale_dollar_or_percent',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='title_and_escrow_fees',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='title_and_escrow_fees_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsclosingcost',
            name='title_and_escrow_fees_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsestresalevalue',
            name='annual_property_appreciation',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsestresalevalue',
            name='appreciation_value_option',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsestresalevalue',
            name='appreciation_value_option_desired',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsestresalevalue',
            name='resale_value_option_v',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsothercosts',
            name='utilities',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdspropertyinfo',
            name='additional_units',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='rdspropertyinfo',
            name='adu',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdspropertyinfo',
            name='other_listing_source',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdspropertyinfo',
            name='other_property_type',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsremodelcost',
            name='additional_units_price',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='rdsremodelcost',
            name='adu_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='remodelcost',
            name='additional_units_price',
            field=models.JSONField(null=True),
        ),
        migrations.AddField(
            model_name='remodelcost',
            name='adu_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='brclosingcost',
            name='closing_cost_credit',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='brclosingcost',
            name='closing_cost_option_resell',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='brclosingcost',
            name='commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='brclosingcost',
            name='commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name='brfinanceoptions',
            name='pmi_value',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='brpropertyinfo',
            name='listing_source',
            field=models.CharField(choices=[(0, 'BPO Homes'), (1, 'Mls'), (2, 'Zillow'), (3, 'Others')], default=0, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='brpropertyinfo',
            name='property_type',
            field=models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex'), (6, 'Others')], default=0, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='brpropertymanagement',
            name='annual_maintenance',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='closingcost',
            name='closing_cost_credit',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='closingcost',
            name='closing_cost_option_resell',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='closingcost',
            name='commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='closingcost',
            name='commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name='financeoptions',
            name='pmi_value',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='ncclosingcost',
            name='closing_cost_credit',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='ncclosingcost',
            name='closing_cost_option_resell',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='ncclosingcost',
            name='commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='ncclosingcost',
            name='commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name='ncfinanceoptions',
            name='pmi_value',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='ncpropertyinfo',
            name='listing_source',
            field=models.CharField(choices=[(0, 'BPO Homes'), (1, 'Mls'), (2, 'Zillow'), (3, 'Others')], default=0, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='ncpropertyinfo',
            name='property_type',
            field=models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex'), (6, 'Others')], default=0, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='ncpropertymanagement',
            name='annual_maintenance',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='propertyinfo',
            name='listing_source',
            field=models.CharField(choices=[(0, 'BPO Homes'), (1, 'Mls'), (2, 'Zillow'), (3, 'Others')], default=0, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='propertyinfo',
            name='property_type',
            field=models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex'), (6, 'Others')], default=0, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='propertymanagement',
            name='annual_maintenance',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='raaclosingcost',
            name='closing_cost_credit',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='raaclosingcost',
            name='closing_cost_option_resell',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='raaclosingcost',
            name='commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='raaclosingcost',
            name='commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name='raafinanceoptions',
            name='pmi_value',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='raapropertyinfo',
            name='listing_source',
            field=models.CharField(choices=[(0, 'BPO Homes'), (1, 'Mls'), (2, 'Zillow'), (3, 'Others')], default=0, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='raapropertyinfo',
            name='property_type',
            field=models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex'), (6, 'Others')], default=0, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='raapropertymanagement',
            name='annual_maintenance',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsaclosingcost',
            name='closing_cost_credit',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsaclosingcost',
            name='closing_cost_option_resell',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsaclosingcost',
            name='commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsaclosingcost',
            name='commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name='rdsafinanceoptions',
            name='pmi_value',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsapropertyinfo',
            name='listing_source',
            field=models.CharField(choices=[(0, 'BPO Homes'), (1, 'Mls'), (2, 'Zillow'), (3, 'Others')], default=0, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='rdsapropertyinfo',
            name='property_type',
            field=models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex'), (6, 'Others')], default=0, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsapropertymanagement',
            name='annual_maintenance',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsclosingcost',
            name='closing_cost_credit',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsclosingcost',
            name='closing_cost_option_resell',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsclosingcost',
            name='commission_on_resale',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsclosingcost',
            name='commission_on_resale_dollar_or_percent',
            field=models.FloatField(null=True),
        ),
        migrations.AlterField(
            model_name='rdsfinanceoptions',
            name='pmi_value',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdspropertyinfo',
            name='listing_source',
            field=models.CharField(choices=[(0, 'BPO Homes'), (1, 'Mls'), (2, 'Zillow'), (3, 'Others')], default=0, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='rdspropertyinfo',
            name='property_type',
            field=models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex'), (6, 'Others')], default=0, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdspropertymanagement',
            name='annual_maintenance',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
    ]
