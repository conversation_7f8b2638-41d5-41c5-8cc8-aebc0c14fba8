# Generated by Django 3.2.15 on 2023-08-22 00:31

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0017_alter_investorprofitcalc_investment_values_dollar_or_percent'),
    ]

    operations = [
        migrations.CreateModel(
            name='ZillowComps',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('model_type', models.CharField(max_length=255, null=True)),
                ('model_id', models.CharField(max_length=255, null=True)),
                ('sales_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('gross_living_area', models.PositiveIntegerField()),
                ('lot_size', models.PositiveIntegerField()),
                ('num_bedrooms', models.PositiveIntegerField()),
                ('num_bathrooms', models.PositiveIntegerField()),
                ('description', models.TextField()),
                ('address', models.CharField(max_length=255)),
                ('price_per_gross_living_area', models.DecimalField(decimal_places=2, max_digits=10)),
                ('images', models.JSONField()),
                ('new', models.BooleanField()),
                ('year_built', models.PositiveIntegerField()),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
