# Generated by Django 3.2.15 on 2022-12-19 18:13

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0004_auto_20221216_2216'),
    ]

    operations = [
        migrations.AddField(
            model_name='brpropertyinfo',
            name='created',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='created',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='created',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raapropertyinfo',
            name='created',
            field=models.Char<PERSON>ield(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='created',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdspropertyinfo',
            name='created',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
    ]
