# Generated by Django 3.2.15 on 2023-01-11 15:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0011_auto_20230110_1940'),
    ]

    operations = [
        migrations.AddField(
            model_name='brestresalevalue',
            name='resale_value_option',
            field=models.CharField(choices=[(1, 'BPO Estimate'), (2, 'Desired Resale Value')], default=1, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='estimatedresalevalue',
            name='resale_value_option',
            field=models.CharField(choices=[(1, 'BPO Estimate'), (2, 'Desired Resale Value')], default=1, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncestresalevalue',
            name='resale_value_option',
            field=models.CharField(choices=[(1, 'BPO Estimate'), (2, 'Desired Resale Value')], default=1, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaestresalevalue',
            name='resale_value_option',
            field=models.CharField(choices=[(1, 'BPO Estimate'), (2, 'Desired Resale Value')], default=1, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaestresalevalue',
            name='resale_value_option',
            field=models.CharField(choices=[(1, 'BPO Estimate'), (2, 'Desired Resale Value')], default=1, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsestresalevalue',
            name='resale_value_option',
            field=models.CharField(choices=[(1, 'BPO Estimate'), (2, 'Desired Resale Value')], default=1, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='brestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=2, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='estimatedresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=2, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='ncestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=2, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='raaestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=2, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsaestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=2, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='rdsestresalevalue',
            name='comps_option',
            field=models.CharField(choices=[(1, 'Custom Comps'), (2, 'BPOHomes Comps'), (3, 'Both Custom Comps and BPOHomes Comps')], default=2, max_length=255, null=True),
        ),
    ]
