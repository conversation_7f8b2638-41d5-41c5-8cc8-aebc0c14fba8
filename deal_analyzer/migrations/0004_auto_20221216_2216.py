# Generated by Django 3.2.15 on 2022-12-16 22:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0003_auto_20221207_1459'),
    ]

    operations = [
        migrations.AddField(
            model_name='brestresalevalue',
            name='average_price_psqft',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='estimatedresalevalue',
            name='average_price_psqft',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncestresalevalue',
            name='average_price_psqft',
            field=models.Char<PERSON>ield(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaestresalevalue',
            name='average_price_psqft',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaestresalevalue',
            name='average_price_psqft',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsestresalevalue',
            name='average_price_psqft',
            field=models.CharField(max_length=255, null=True),
        ),
    ]
