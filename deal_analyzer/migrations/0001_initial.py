# Generated by Django 3.2.15 on 2022-12-02 18:28

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('register', '0042_agent_ghanaian_marketer'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BRPropertyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('listing_date', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('listing_source', models.CharField(choices=[(0, 'Mls'), (1, 'Zillow'), (2, 'Others')], default=0, max_length=20, null=True)),
                ('bedrooms', models.IntegerField(null=True)),
                ('bathrooms', models.IntegerField(null=True)),
                ('lot_size', models.IntegerField(null=True)),
                ('pre_existing_livable_sqft', models.CharField(max_length=255, null=True)),
                ('garage', models.CharField(max_length=255, null=True)),
                ('additional_sqrft', models.IntegerField(null=True)),
                ('year_of_construction', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('property_type', models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex')], default=0, max_length=255, null=True)),
                ('completed_round', models.BooleanField(default=False)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Contractor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('final_remodel', models.FloatField(blank=True, max_length=255, null=True)),
                ('final_new_construction', models.FloatField(blank=True, max_length=255, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='EstimatedResaleValueComps',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('street', models.CharField(max_length=255, null=True)),
                ('city', models.CharField(max_length=255, null=True)),
                ('state_or_province', models.CharField(max_length=255, null=True)),
                ('per_sqft', models.FloatField(null=True)),
                ('price', models.FloatField(null=True)),
                ('bedrooms', models.FloatField(null=True)),
                ('bathrooms', models.FloatField(null=True)),
                ('year_built', models.CharField(max_length=255, null=True)),
                ('sqft', models.FloatField(null=True)),
                ('lot_size', models.CharField(max_length=255, null=True)),
                ('close_date', models.CharField(max_length=255, null=True)),
                ('new_lot_size', models.CharField(max_length=255, null=True)),
                ('site_address', models.CharField(max_length=255, null=True)),
                ('owner_name', models.CharField(max_length=255, null=True)),
                ('house_number', models.CharField(max_length=255, null=True)),
                ('street_name', models.CharField(max_length=255, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCPropertyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('listing_date', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('listing_source', models.CharField(choices=[(0, 'Mls'), (1, 'Zillow'), (2, 'Others')], default=0, max_length=20, null=True)),
                ('bedrooms', models.IntegerField(null=True)),
                ('bathrooms', models.IntegerField(null=True)),
                ('lot_size', models.IntegerField(null=True)),
                ('pre_existing_livable_sqft', models.CharField(max_length=255, null=True)),
                ('garage', models.CharField(max_length=255, null=True)),
                ('additional_sqrft', models.IntegerField(null=True)),
                ('year_of_construction', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('property_type', models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex')], default=0, max_length=255, null=True)),
                ('completed_round', models.BooleanField(default=False)),
                ('new_construction_sqft', models.CharField(max_length=255, null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PropertyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('listing_date', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('listing_source', models.CharField(choices=[(0, 'Mls'), (1, 'Zillow'), (2, 'Others')], default=0, max_length=20, null=True)),
                ('bedrooms', models.IntegerField(null=True)),
                ('bathrooms', models.IntegerField(null=True)),
                ('lot_size', models.IntegerField(null=True)),
                ('pre_existing_livable_sqft', models.CharField(max_length=255, null=True)),
                ('garage', models.CharField(max_length=255, null=True)),
                ('additional_sqrft', models.IntegerField(null=True)),
                ('year_of_construction', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('property_type', models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex')], default=0, max_length=255, null=True)),
                ('completed_round', models.BooleanField(default=False)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAPropertyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('listing_date', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('listing_source', models.CharField(choices=[(0, 'Mls'), (1, 'Zillow'), (2, 'Others')], default=0, max_length=20, null=True)),
                ('bedrooms', models.IntegerField(null=True)),
                ('bathrooms', models.IntegerField(null=True)),
                ('lot_size', models.IntegerField(null=True)),
                ('pre_existing_livable_sqft', models.CharField(max_length=255, null=True)),
                ('garage', models.CharField(max_length=255, null=True)),
                ('additional_sqrft', models.IntegerField(null=True)),
                ('year_of_construction', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('property_type', models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex')], default=0, max_length=255, null=True)),
                ('completed_round', models.BooleanField(default=False)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAPropertyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('listing_date', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('listing_source', models.CharField(choices=[(0, 'Mls'), (1, 'Zillow'), (2, 'Others')], default=0, max_length=20, null=True)),
                ('bedrooms', models.IntegerField(null=True)),
                ('bathrooms', models.IntegerField(null=True)),
                ('lot_size', models.IntegerField(null=True)),
                ('pre_existing_livable_sqft', models.CharField(max_length=255, null=True)),
                ('garage', models.CharField(max_length=255, null=True)),
                ('additional_sqrft', models.IntegerField(null=True)),
                ('year_of_construction', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('property_type', models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex')], default=0, max_length=255, null=True)),
                ('completed_round', models.BooleanField(default=False)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSPropertyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('listing_date', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('listing_source', models.CharField(choices=[(0, 'Mls'), (1, 'Zillow'), (2, 'Others')], default=0, max_length=20, null=True)),
                ('bedrooms', models.IntegerField(null=True)),
                ('bathrooms', models.IntegerField(null=True)),
                ('lot_size', models.IntegerField(null=True)),
                ('pre_existing_livable_sqft', models.CharField(max_length=255, null=True)),
                ('garage', models.CharField(max_length=255, null=True)),
                ('additional_sqrft', models.IntegerField(null=True)),
                ('year_of_construction', models.CharField(default=datetime.datetime.now, max_length=255, null=True)),
                ('property_type', models.CharField(choices=[(0, 'Single Family Home'), (1, 'Townhouse'), (2, 'Condo'), (3, 'Duplex'), (4, 'Triplex'), (5, 'Fourplex')], default=0, max_length=255, null=True)),
                ('completed_round', models.BooleanField(default=False)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Taxes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_and_county_tax', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SummaryText',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(blank=True, null=True)),
                ('property_info', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='StraightAestheticsQuestions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, max_length=255, null=True)),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_remodel_cost', models.FloatField(null=True)),
                ('primary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('primary_wholesale_fee', models.FloatField(null=True)),
                ('primary_wholesale_fee_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('primary_loan_amount', models.FloatField(null=True)),
                ('primary_down_payment', models.FloatField(null=True)),
                ('primary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_term_of_loan_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_annual_interest_rate_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_interest_only_term_secondary_option', models.BooleanField(default=False, null=True)),
                ('cash_amount', models.FloatField(null=True)),
                ('secondary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('secondary_wholesale_fee', models.FloatField(null=True)),
                ('secondary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_down_payment', models.FloatField(null=True)),
                ('secondary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('rental_income_option', models.BooleanField(default=False, null=True)),
                ('monthly_rental_income', models.FloatField(null=True)),
                ('property_manager_option', models.BooleanField(default=False, null=True)),
                ('investors_option', models.BooleanField(default=False, null=True)),
                ('information', models.TextField(null=True)),
                ('contact_name', models.CharField(max_length=255, null=True)),
                ('contact_phone', models.CharField(max_length=255, null=True)),
                ('contact_email', models.CharField(max_length=255, null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RentalIncome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('est_rental_income_per_month', models.FloatField(null=True)),
                ('vacancy_months', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RemodelDownToStudsQuestions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, max_length=255, null=True)),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_remodel_cost', models.FloatField(null=True)),
                ('primary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('primary_wholesale_fee', models.FloatField(null=True)),
                ('primary_wholesale_fee_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('primary_loan_amount', models.FloatField(null=True)),
                ('primary_down_payment', models.FloatField(null=True)),
                ('primary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_term_of_loan_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_annual_interest_rate_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_interest_only_term_secondary_option', models.BooleanField(default=False, null=True)),
                ('cash_amount', models.FloatField(null=True)),
                ('secondary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('secondary_wholesale_fee', models.FloatField(null=True)),
                ('secondary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_down_payment', models.FloatField(null=True)),
                ('secondary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('rental_income_option', models.BooleanField(default=False, null=True)),
                ('monthly_rental_income', models.FloatField(null=True)),
                ('property_manager_option', models.BooleanField(default=False, null=True)),
                ('investors_option', models.BooleanField(default=False, null=True)),
                ('information', models.TextField(null=True)),
                ('contact_name', models.CharField(max_length=255, null=True)),
                ('contact_phone', models.CharField(max_length=255, null=True)),
                ('contact_email', models.CharField(max_length=255, null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RemodelDownToStudsAndAdditionQuestions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, max_length=255, null=True)),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_remodel_cost', models.FloatField(null=True)),
                ('primary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('primary_wholesale_fee', models.FloatField(null=True)),
                ('primary_wholesale_fee_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('primary_loan_amount', models.FloatField(null=True)),
                ('primary_down_payment', models.FloatField(null=True)),
                ('primary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_term_of_loan_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_annual_interest_rate_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_interest_only_term_secondary_option', models.BooleanField(default=False, null=True)),
                ('cash_amount', models.FloatField(null=True)),
                ('secondary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('secondary_wholesale_fee', models.FloatField(null=True)),
                ('secondary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_down_payment', models.FloatField(null=True)),
                ('secondary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('rental_income_option', models.BooleanField(default=False, null=True)),
                ('monthly_rental_income', models.FloatField(null=True)),
                ('property_manager_option', models.BooleanField(default=False, null=True)),
                ('investors_option', models.BooleanField(default=False, null=True)),
                ('information', models.TextField(null=True)),
                ('contact_name', models.CharField(max_length=255, null=True)),
                ('contact_phone', models.CharField(max_length=255, null=True)),
                ('contact_email', models.CharField(max_length=255, null=True)),
                ('square_footage', models.FloatField(null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RemodelCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('est_remodel_cost_psqft', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RemodelAddAdditionQuestions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, max_length=255, null=True)),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_remodel_cost', models.FloatField(null=True)),
                ('primary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('primary_wholesale_fee', models.FloatField(null=True)),
                ('primary_wholesale_fee_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('primary_loan_amount', models.FloatField(null=True)),
                ('primary_down_payment', models.FloatField(null=True)),
                ('primary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_term_of_loan_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_annual_interest_rate_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_interest_only_term_secondary_option', models.BooleanField(default=False, null=True)),
                ('cash_amount', models.FloatField(null=True)),
                ('secondary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('secondary_wholesale_fee', models.FloatField(null=True)),
                ('secondary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_down_payment', models.FloatField(null=True)),
                ('secondary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('rental_income_option', models.BooleanField(default=False, null=True)),
                ('monthly_rental_income', models.FloatField(null=True)),
                ('property_manager_option', models.BooleanField(default=False, null=True)),
                ('investors_option', models.BooleanField(default=False, null=True)),
                ('information', models.TextField(null=True)),
                ('contact_name', models.CharField(max_length=255, null=True)),
                ('contact_phone', models.CharField(max_length=255, null=True)),
                ('contact_email', models.CharField(max_length=255, null=True)),
                ('square_footage', models.FloatField(null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSTaxes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_and_county_tax', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSRentalIncome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('est_rental_income_per_month', models.FloatField(null=True)),
                ('vacancy_months', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSRemodelCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('est_remodel_cost_psqft', models.FloatField(null=True)),
                ('est_new_construction_cost_psqft', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSPropertyPermitFees',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_permit_impact_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('city_permit_impact_fees', models.FloatField(null=True)),
                ('title_and_escrow_fees', models.FloatField(null=True)),
                ('architectural_fees', models.FloatField(null=True)),
                ('monthly_home_insurance', models.FloatField(null=True)),
                ('monthly_hoa_dues', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSPropertyManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_management_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('property_management_fees', models.FloatField(null=True)),
                ('annual_maintenance_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('annual_maintenance', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSOtherCosts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('miscellaneous', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSNonAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSInvestors',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investor_value', models.FloatField(null=True)),
                ('remodel_down_to_studs', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='investors', to='deal_analyzer.remodeldowntostudsquestions')),
            ],
        ),
        migrations.CreateModel(
            name='RDSInvestorProfit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investment_values_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('investor', models.CharField(max_length=255, null=True)),
                ('equity', models.FloatField(null=True)),
                ('profit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSFinanceOptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('primary_wholesale_fee', models.IntegerField(null=True)),
                ('primary_wholesale_fee_options', models.CharField(choices=[(0, 'Add Wholesale Fee to Loan Amount'), (1, 'Add Wholesale Fee to Upfront Closing Cost'), (2, 'Add Wholesale Fee to Back End Closing Cost')], default=0, max_length=255, null=True)),
                ('primary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment', models.CharField(max_length=255, null=True)),
                ('primary_include_pmi', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('primary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_total_remodel_cost', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('secondary_purchase_price', models.FloatField(null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('secondary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_down_payment', models.CharField(max_length=255, null=True)),
                ('secondary_include_pmi', models.BooleanField(default=False, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_total_remodel_cost', models.FloatField(null=True)),
                ('cash_for_home_purchase_price', models.FloatField(null=True)),
                ('cash_for_home_lender_points', models.FloatField(null=True)),
                ('cash_for_home_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSEstResaleValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_square_footage', models.CharField(max_length=255, null=True)),
                ('average_lot_size', models.CharField(max_length=255, null=True)),
                ('average_purchase_price', models.CharField(max_length=255, null=True)),
                ('number_of_comps', models.IntegerField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSClosingCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('closing_cost_option_resell', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=1, max_length=255, null=True)),
                ('closing_cost_option_purchase', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=0, max_length=255, null=True)),
                ('commission_on_resale_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('commission_on_resale', models.FloatField(null=True)),
                ('closing_cost_credit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSCarryingMonths',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('carrying_cost_owning_months', models.FloatField(null=True)),
                ('carrying_cost_owning_months_option', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSATaxes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_and_county_tax', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSARentalIncome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('est_rental_income_per_month', models.FloatField(null=True)),
                ('vacancy_months', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSARemodelCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('est_remodel_cost_psqft', models.FloatField(null=True)),
                ('est_new_construction_cost_psqft', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAPropertyPermitFees',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_permit_impact_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('city_permit_impact_fees', models.FloatField(null=True)),
                ('title_and_escrow_fees', models.FloatField(null=True)),
                ('architectural_fees', models.FloatField(null=True)),
                ('monthly_home_insurance', models.FloatField(null=True)),
                ('monthly_hoa_dues', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAPropertyManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_management_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('property_management_fees', models.FloatField(null=True)),
                ('annual_maintenance_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('annual_maintenance', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAOtherCosts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('miscellaneous', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSANonAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAInvestors',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investor_value', models.FloatField(null=True)),
                ('remodel_down_to_studs_and_addition', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='investors', to='deal_analyzer.remodeldowntostudsandadditionquestions')),
            ],
        ),
        migrations.CreateModel(
            name='RDSAInvestorProfit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investment_values_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('investor', models.CharField(max_length=255, null=True)),
                ('equity', models.FloatField(null=True)),
                ('profit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAFinanceOptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('primary_wholesale_fee', models.IntegerField(null=True)),
                ('primary_wholesale_fee_options', models.CharField(choices=[(0, 'Add Wholesale Fee to Loan Amount'), (1, 'Add Wholesale Fee to Upfront Closing Cost'), (2, 'Add Wholesale Fee to Back End Closing Cost')], default=0, max_length=255, null=True)),
                ('primary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment', models.CharField(max_length=255, null=True)),
                ('primary_include_pmi', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('primary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_total_remodel_cost', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('secondary_purchase_price', models.FloatField(null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('secondary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_down_payment', models.CharField(max_length=255, null=True)),
                ('secondary_include_pmi', models.BooleanField(default=False, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_total_remodel_cost', models.FloatField(null=True)),
                ('cash_for_home_purchase_price', models.FloatField(null=True)),
                ('cash_for_home_lender_points', models.FloatField(null=True)),
                ('cash_for_home_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAEstResaleValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_square_footage', models.CharField(max_length=255, null=True)),
                ('average_lot_size', models.CharField(max_length=255, null=True)),
                ('average_purchase_price', models.CharField(max_length=255, null=True)),
                ('number_of_comps', models.IntegerField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAClosingCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('closing_cost_option_resell', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=1, max_length=255, null=True)),
                ('closing_cost_option_purchase', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=0, max_length=255, null=True)),
                ('commission_on_resale_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('commission_on_resale', models.FloatField(null=True)),
                ('closing_cost_credit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSACarryingMonths',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('carrying_cost_owning_months', models.FloatField(null=True)),
                ('carrying_cost_owning_months_option', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAATaxes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_and_county_tax', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAARentalIncome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('est_rental_income_per_month', models.FloatField(null=True)),
                ('vacancy_months', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAARemodelCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('est_remodel_cost_psqft', models.FloatField(null=True)),
                ('est_new_construction_cost_psqft', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAPropertyPermitFees',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_permit_impact_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('city_permit_impact_fees', models.FloatField(null=True)),
                ('title_and_escrow_fees', models.FloatField(null=True)),
                ('architectural_fees', models.FloatField(null=True)),
                ('monthly_home_insurance', models.FloatField(null=True)),
                ('monthly_hoa_dues', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAPropertyManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_management_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('property_management_fees', models.FloatField(null=True)),
                ('annual_maintenance_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('annual_maintenance', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAOtherCosts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('miscellaneous', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAANonAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAInvestors',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investor_value', models.FloatField(null=True)),
                ('remodel_add_addition', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='investors', to='deal_analyzer.remodeladdadditionquestions')),
            ],
        ),
        migrations.CreateModel(
            name='RAAInvestorProfit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investment_values_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('investor', models.CharField(max_length=255, null=True)),
                ('equity', models.FloatField(null=True)),
                ('profit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAFinanceOptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('primary_wholesale_fee', models.IntegerField(null=True)),
                ('primary_wholesale_fee_options', models.CharField(choices=[(0, 'Add Wholesale Fee to Loan Amount'), (1, 'Add Wholesale Fee to Upfront Closing Cost'), (2, 'Add Wholesale Fee to Back End Closing Cost')], default=0, max_length=255, null=True)),
                ('primary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment', models.CharField(max_length=255, null=True)),
                ('primary_include_pmi', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('primary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_total_remodel_cost', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('secondary_purchase_price', models.FloatField(null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('secondary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_down_payment', models.CharField(max_length=255, null=True)),
                ('secondary_include_pmi', models.BooleanField(default=False, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_total_remodel_cost', models.FloatField(null=True)),
                ('cash_for_home_purchase_price', models.FloatField(null=True)),
                ('cash_for_home_lender_points', models.FloatField(null=True)),
                ('cash_for_home_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAEstResaleValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_square_footage', models.CharField(max_length=255, null=True)),
                ('average_lot_size', models.CharField(max_length=255, null=True)),
                ('average_purchase_price', models.CharField(max_length=255, null=True)),
                ('number_of_comps', models.IntegerField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAClosingCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('closing_cost_option_resell', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=1, max_length=255, null=True)),
                ('closing_cost_option_purchase', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=0, max_length=255, null=True)),
                ('commission_on_resale_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('commission_on_resale', models.FloatField(null=True)),
                ('closing_cost_credit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAACarryingMonths',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('carrying_cost_owning_months', models.FloatField(null=True)),
                ('carrying_cost_owning_months_option', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PropertyPermitFees',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_permit_impact_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('city_permit_impact_fees', models.FloatField(null=True)),
                ('title_and_escrow_fees', models.FloatField(null=True)),
                ('architectural_fees', models.FloatField(null=True)),
                ('monthly_home_insurance', models.FloatField(null=True)),
                ('monthly_hoa_dues', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PropertyManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_management_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('property_management_fees', models.FloatField(null=True)),
                ('annual_maintenance_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('annual_maintenance', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='OtherCosts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('miscellaneous', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='OneTimeSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subscription', models.CharField(default=None, max_length=255, null=True)),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Used', 'Used')], default='Pending', max_length=255)),
                ('customer', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='register.stripecustomer')),
            ],
        ),
        migrations.CreateModel(
            name='NonAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NewConstructionQuestions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, max_length=255, null=True)),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_remodel_cost', models.FloatField(null=True)),
                ('primary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('primary_wholesale_fee', models.FloatField(null=True)),
                ('primary_wholesale_fee_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('primary_loan_amount', models.FloatField(null=True)),
                ('primary_down_payment', models.FloatField(null=True)),
                ('primary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_term_of_loan_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_annual_interest_rate_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_interest_only_term_secondary_option', models.BooleanField(default=False, null=True)),
                ('cash_amount', models.FloatField(null=True)),
                ('secondary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('secondary_wholesale_fee', models.FloatField(null=True)),
                ('secondary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_down_payment', models.FloatField(null=True)),
                ('secondary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('rental_income_option', models.BooleanField(default=False, null=True)),
                ('monthly_rental_income', models.FloatField(null=True)),
                ('property_manager_option', models.BooleanField(default=False, null=True)),
                ('investors_option', models.BooleanField(default=False, null=True)),
                ('information', models.TextField(null=True)),
                ('contact_name', models.CharField(max_length=255, null=True)),
                ('contact_phone', models.CharField(max_length=255, null=True)),
                ('contact_email', models.CharField(max_length=255, null=True)),
                ('square_footage', models.FloatField(null=True)),
                ('demolishing_cost_option', models.BooleanField(default=False, null=True)),
                ('demolishing_cost', models.FloatField(null=True)),
                ('est_development_cost', models.FloatField(null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCTaxes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_and_county_tax', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCRentalIncome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('est_rental_income_per_month', models.FloatField(null=True)),
                ('vacancy_months', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCRemodelCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('est_remodel_cost_psqft', models.FloatField(null=True)),
                ('est_new_construction_cost_psqft', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCPropertyPermitFees',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_permit_impact_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('city_permit_impact_fees', models.FloatField(null=True)),
                ('title_and_escrow_fees', models.FloatField(null=True)),
                ('architectural_fees', models.FloatField(null=True)),
                ('monthly_home_insurance', models.FloatField(null=True)),
                ('monthly_hoa_dues', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCPropertyManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_management_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('property_management_fees', models.FloatField(null=True)),
                ('annual_maintenance_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('annual_maintenance', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCOtherCosts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('miscellaneous', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCNonAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCInvestors',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investor_value', models.FloatField(null=True)),
                ('new_construction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='investors', to='deal_analyzer.newconstructionquestions')),
            ],
        ),
        migrations.CreateModel(
            name='NCInvestorProfit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investment_values_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('investor', models.CharField(max_length=255, null=True)),
                ('equity', models.FloatField(null=True)),
                ('profit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCFinanceOptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('primary_wholesale_fee', models.IntegerField(null=True)),
                ('primary_wholesale_fee_options', models.CharField(choices=[(0, 'Add Wholesale Fee to Loan Amount'), (1, 'Add Wholesale Fee to Upfront Closing Cost'), (2, 'Add Wholesale Fee to Back End Closing Cost')], default=0, max_length=255, null=True)),
                ('primary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment', models.CharField(max_length=255, null=True)),
                ('primary_include_pmi', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('primary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_total_remodel_cost', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('secondary_purchase_price', models.FloatField(null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('secondary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_down_payment', models.CharField(max_length=255, null=True)),
                ('secondary_include_pmi', models.BooleanField(default=False, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_total_remodel_cost', models.FloatField(null=True)),
                ('cash_for_home_purchase_price', models.FloatField(null=True)),
                ('cash_for_home_lender_points', models.FloatField(null=True)),
                ('cash_for_home_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Single Loan for Land and New Construction'), (1, 'Obtain Primary Loan For Land and A Secondary Loan for New Construction '), (2, 'Obtain Primary Loan For New Construction and Pay Cash For Land'), (3, 'Obtain a Primary Loan For Land and Pay Cash New Construction'), (4, 'Pay Cash for Land and New Construction')], default=0, max_length=100, null=True)),
                ('new_construction_cost', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCEstResaleValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_square_footage', models.CharField(max_length=255, null=True)),
                ('average_lot_size', models.CharField(max_length=255, null=True)),
                ('average_purchase_price', models.CharField(max_length=255, null=True)),
                ('number_of_comps', models.IntegerField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCDevelopmentCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('new_construction_cost_psqft', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCDemolishingCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('demolishing_cost_option', models.BooleanField(default=False, null=True)),
                ('demolishing_cost_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('demolishing_cost', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCClosingCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('closing_cost_option_resell', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=1, max_length=255, null=True)),
                ('closing_cost_option_purchase', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=0, max_length=255, null=True)),
                ('commission_on_resale_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('commission_on_resale', models.FloatField(null=True)),
                ('closing_cost_credit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCCarryingMonths',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('carrying_cost_owning_months', models.FloatField(null=True)),
                ('carrying_cost_owning_months_option', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Investors',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investor_value', models.FloatField(null=True)),
                ('straight_aesthetics', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='investors', to='deal_analyzer.straightaestheticsquestions')),
            ],
        ),
        migrations.CreateModel(
            name='InvestorProfit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investment_values_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('investor', models.CharField(max_length=255, null=True)),
                ('equity', models.FloatField(null=True)),
                ('profit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='FinanceOptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('primary_wholesale_fee', models.IntegerField(null=True)),
                ('primary_wholesale_fee_options', models.CharField(choices=[(0, 'Add Wholesale Fee to Loan Amount'), (1, 'Add Wholesale Fee to Upfront Closing Cost'), (2, 'Add Wholesale Fee to Back End Closing Cost')], default=0, max_length=255, null=True)),
                ('primary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment', models.CharField(max_length=255, null=True)),
                ('primary_include_pmi', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('primary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_total_remodel_cost', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('secondary_purchase_price', models.FloatField(null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('secondary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_down_payment', models.CharField(max_length=255, null=True)),
                ('secondary_include_pmi', models.BooleanField(default=False, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_total_remodel_cost', models.FloatField(null=True)),
                ('cash_for_home_purchase_price', models.FloatField(null=True)),
                ('cash_for_home_lender_points', models.FloatField(null=True)),
                ('cash_for_home_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('cash_for_home_wholesale_fee', models.IntegerField(null=True)),
                ('cash_for_home_wholesale_fee_options', models.CharField(choices=[(0, 'Add Wholesale Fee to Loan Amount'), (1, 'Add Wholesale Fee to Upfront Closing Cost'), (2, 'Add Wholesale Fee to Back End Closing Cost')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='EstimatedResaleValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_square_footage', models.CharField(max_length=255, null=True)),
                ('average_lot_size', models.CharField(max_length=255, null=True)),
                ('average_purchase_price', models.CharField(max_length=255, null=True)),
                ('number_of_comps', models.IntegerField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ClosingCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('closing_cost_option_resell', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=1, max_length=255, null=True)),
                ('closing_cost_option_purchase', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=0, max_length=255, null=True)),
                ('commission_on_resale_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('commission_on_resale', models.FloatField(null=True)),
                ('closing_cost_credit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CarryingCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('carrying_cost_owning_months', models.FloatField(null=True)),
                ('carrying_cost_owning_months_option', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BuyAndRentQuestions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_address', models.CharField(blank=True, max_length=255, null=True)),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_remodel_cost', models.FloatField(null=True)),
                ('primary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('primary_wholesale_fee', models.FloatField(null=True)),
                ('primary_wholesale_fee_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('primary_loan_amount', models.FloatField(null=True)),
                ('primary_down_payment', models.FloatField(null=True)),
                ('primary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_term_of_loan_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_annual_interest_rate_secondary_option', models.BooleanField(default=False, null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_interest_only_term_secondary_option', models.BooleanField(default=False, null=True)),
                ('cash_amount', models.FloatField(null=True)),
                ('secondary_wholesale_fee_option', models.BooleanField(default=False, null=True)),
                ('secondary_wholesale_fee', models.FloatField(null=True)),
                ('secondary_lender_points_option', models.BooleanField(default=False, null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], max_length=255, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_down_payment', models.FloatField(null=True)),
                ('secondary_down_payment_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('rental_income_option', models.BooleanField(default=False, null=True)),
                ('monthly_rental_income', models.FloatField(null=True)),
                ('property_manager_option', models.BooleanField(default=False, null=True)),
                ('investors_option', models.BooleanField(default=False, null=True)),
                ('information', models.TextField(null=True)),
                ('contact_name', models.CharField(max_length=255, null=True)),
                ('contact_phone', models.CharField(max_length=255, null=True)),
                ('contact_email', models.CharField(max_length=255, null=True)),
                ('square_footage', models.FloatField(null=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRTaxes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_and_county_tax', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRRentalIncome',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('owning_months', models.FloatField(null=True)),
                ('owning_months_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('est_rental_income_per_month', models.FloatField(null=True)),
                ('vacancy_months', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRRemodelCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('est_remodel_cost_psqft', models.FloatField(null=True)),
                ('est_new_construction_cost_psqft', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRPropertyPermitFees',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('city_permit_impact_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('city_permit_impact_fees', models.FloatField(null=True)),
                ('title_and_escrow_fees', models.FloatField(null=True)),
                ('architectural_fees', models.FloatField(null=True)),
                ('monthly_home_insurance', models.FloatField(null=True)),
                ('monthly_hoa_dues', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRPropertyManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('property_management_fees_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('property_management_fees', models.FloatField(null=True)),
                ('annual_maintenance_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=50, null=True)),
                ('annual_maintenance', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BROtherCosts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('miscellaneous', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRNonAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRInvestors',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investor_value', models.FloatField(null=True)),
                ('buy_and_rent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='investors', to='deal_analyzer.buyandrentquestions')),
            ],
        ),
        migrations.CreateModel(
            name='BRInvestorProfit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investment_values_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('investor', models.CharField(max_length=255, null=True)),
                ('equity', models.FloatField(null=True)),
                ('profit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRFinanceOptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deal_finance_option', models.CharField(choices=[(0, 'Obtain Primary Home Loan and a Secondary Loan'), (1, 'Obtain Primary Home Loan and Use Cash for Remodel'), (2, 'Obtain Single Primary Home Loan For Home and Remodel'), (3, 'Obtain a Primary Remodel Loan and Pay Cash For Home'), (4, 'Pay All Cash for Home and Remodel'), (5, 'Obtain a Primary Loan for Rental')], default=0, max_length=100, null=True)),
                ('primary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('primary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('primary_purchase_price', models.FloatField(null=True)),
                ('primary_lender_points', models.FloatField(null=True)),
                ('primary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('primary_wholesale_fee', models.IntegerField(null=True)),
                ('primary_wholesale_fee_options', models.CharField(choices=[(0, 'Add Wholesale Fee to Loan Amount'), (1, 'Add Wholesale Fee to Upfront Closing Cost'), (2, 'Add Wholesale Fee to Back End Closing Cost')], default=0, max_length=255, null=True)),
                ('primary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('primary_down_payment', models.CharField(max_length=255, null=True)),
                ('primary_include_pmi', models.BooleanField(default=False, null=True)),
                ('primary_interest_rate', models.FloatField(null=True)),
                ('primary_term_of_loan', models.FloatField(null=True)),
                ('primary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('primary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_annual_interest_rate', models.FloatField(null=True)),
                ('primary_interest_only_term', models.FloatField(null=True)),
                ('primary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('primary_total_remodel_cost', models.FloatField(null=True)),
                ('secondary_loan_type', models.CharField(choices=[(0, 'Conventional Loan'), (1, 'Private Hard Money'), (2, 'Seller Financing')], default=0, max_length=20, null=True)),
                ('secondary_sub_loan_type', models.CharField(choices=[(0, 'Conventional Mortgage Loan'), (1, 'Interest Only Mortgage Loan')], default=0, max_length=255, null=True)),
                ('secondary_purchase_price', models.FloatField(null=True)),
                ('secondary_lender_points', models.FloatField(null=True)),
                ('secondary_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('secondary_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('secondary_down_payment', models.CharField(max_length=255, null=True)),
                ('secondary_include_pmi', models.BooleanField(default=False, null=True)),
                ('secondary_loan_amount', models.FloatField(null=True)),
                ('secondary_interest_rate', models.FloatField(null=True)),
                ('secondary_term_of_loan', models.FloatField(null=True)),
                ('secondary_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_mortgage_term_of_loan', models.FloatField(null=True)),
                ('secondary_annual_interest_rate', models.FloatField(null=True)),
                ('secondary_interest_only_term', models.FloatField(null=True)),
                ('secondary_interest_only_term_month_or_year', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('secondary_total_remodel_cost', models.FloatField(null=True)),
                ('cash_for_home_purchase_price', models.FloatField(null=True)),
                ('cash_for_home_lender_points', models.FloatField(null=True)),
                ('cash_for_home_lender_points_options', models.CharField(choices=[(0, 'Add Lender Points to Loan Amount'), (1, 'Add Lender Points to Upfront Closing Cost'), (2, 'Add Lender Points to Back End Closing Costs')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BREstResaleValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('average_square_footage', models.CharField(max_length=255, null=True)),
                ('average_lot_size', models.CharField(max_length=255, null=True)),
                ('average_purchase_price', models.CharField(max_length=255, null=True)),
                ('number_of_comps', models.IntegerField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRClosingCost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('closing_cost_option_resell', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=1, max_length=255, null=True)),
                ('closing_cost_option_purchase', models.CharField(choices=[(0, 'Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost'), (1, 'Seller Pays Add Title and Escrow Fees to Backend Closing Cost'), (2, 'Split Title and Escrow Fees'), (3, 'Not Applicable')], default=0, max_length=255, null=True)),
                ('commission_on_resale_dollar_or_percent', models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True)),
                ('commission_on_resale', models.FloatField(null=True)),
                ('closing_cost_credit', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRCarryingMonths',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('carrying_cost_owning_months', models.FloatField(null=True)),
                ('carrying_cost_owning_months_option', models.CharField(choices=[(0, 'Months'), (1, 'Year')], default=0, max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRAestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AestheticItems',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
