# Generated by Django 3.2.15 on 2022-12-06 22:02

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='brpropertypermitfees',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brremodelcost',
            name='remodel_cost_option',
            field=models.CharField(choices=[(0, 'Basic Remodel Cost Calculations'), (1, 'Advanced Remodel Cost Calculations')], default=0, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='brtaxes',
            name='city_and_county_tax_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncpropertypermitfees',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncremodelcost',
            name='remodel_cost_option',
            field=models.CharField(choices=[(0, 'Basic Remodel Cost Calculations'), (1, 'Advanced Remodel Cost Calculations')], default=0, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='nctaxes',
            name='city_and_county_tax_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='propertypermitfees',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raapropertypermitfees',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaremodelcost',
            name='remodel_cost_option',
            field=models.CharField(choices=[(0, 'Basic Remodel Cost Calculations'), (1, 'Advanced Remodel Cost Calculations')], default=0, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='raataxes',
            name='city_and_county_tax_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertypermitfees',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaremodelcost',
            name='remodel_cost_option',
            field=models.CharField(choices=[(0, 'Basic Remodel Cost Calculations'), (1, 'Advanced Remodel Cost Calculations')], default=0, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='rdsataxes',
            name='city_and_county_tax_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdspropertypermitfees',
            name='title_and_escrow_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsremodelcost',
            name='remodel_cost_option',
            field=models.CharField(choices=[(0, 'Basic Remodel Cost Calculations'), (1, 'Advanced Remodel Cost Calculations')], default=0, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='rdstaxes',
            name='city_and_county_tax_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='remodelcost',
            name='remodel_cost_option',
            field=models.CharField(choices=[(0, 'Basic Remodel Cost Calculations'), (1, 'Advanced Remodel Cost Calculations')], default=0, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='taxes',
            name='city_and_county_tax_fees_dollar_or_percent',
            field=models.CharField(choices=[(0, 'Dollar'), (1, 'Percent')], default=0, max_length=255, null=True),
        ),
        migrations.CreateModel(
            name='RDSAdditionRemodelItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSAAdditionRemodelItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAAAdditionRemodelItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCAdditionRemodelItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRAdditionRemodelItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AdditionRemodelItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('item', models.CharField(max_length=255, null=True)),
                ('value', models.FloatField(null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
