# Generated by Django 3.2.15 on 2024-04-22 08:00

import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0020_zillowcomps_agent_zillowcompssummary_agent'),
    ]

    operations = [
        migrations.AddField(
            model_name='brfinanceoptions',
            name='pmi_value',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='brpropertyinfo',
            name='carpet_or_garage',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='financeoptions',
            name='pmi_value',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncfinanceoptions',
            name='pmi_value',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='carpet_or_garage',
            field=models.Char<PERSON>ield(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='carpet_or_garage',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raafinanceoptions',
            name='pmi_value',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='raapropertyinfo',
            name='carpet_or_garage',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsafinanceoptions',
            name='pmi_value',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='carpet_or_garage',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsfinanceoptions',
            name='pmi_value',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='rdspropertyinfo',
            name='carpet_or_garage',
            field=models.CharField(blank=True, default=datetime.datetime.now, max_length=255, null=True),
        ),
    ]
