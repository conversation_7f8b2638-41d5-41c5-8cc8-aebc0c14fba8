# Generated by Django 3.2.15 on 2023-01-04 18:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0005_auto_20221219_1813'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomComps',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(max_length=255, null=True)),
                ('close_date', models.CharField(max_length=255, null=True)),
                ('per_sqft', models.FloatField(null=True)),
                ('price', models.FloatField(null=True)),
                ('bedrooms', models.FloatField(null=True)),
                ('bathrooms', models.FloatField(null=True)),
                ('year_built', models.CharField(max_length=255, null=True)),
                ('sqft', models.FloatField(null=True)),
                ('lot_size', models.Char<PERSON>ield(max_length=255, null=True)),
                ('property_info', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.propertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
