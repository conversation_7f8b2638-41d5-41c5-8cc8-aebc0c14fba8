# Generated by Django 3.2.15 on 2023-02-22 14:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0013_estimatedresalevaluecomps_garage'),
    ]

    operations = [
        migrations.CreateModel(
            name='RDSSummaryText',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(blank=True, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSASummaryText',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(blank=True, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAASummaryText',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(blank=True, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCSummaryText',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(blank=True, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRSummaryText',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField(blank=True, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
