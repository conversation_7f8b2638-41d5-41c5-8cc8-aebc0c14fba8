# Generated by Django 3.2.15 on 2024-06-29 22:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0023_auto_20240525_2112'),
    ]

    operations = [
        migrations.AddField(
            model_name='brpropertyinfo',
            name='basement_psqf',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='brremodelcost',
            name='basement_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='basement_psqf',
            field=models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='basement_psqf',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raapropertyinfo',
            name='basement_psqf',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='raaremodelcost',
            name='basement_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='basement_psqf',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsaremodelcost',
            name='basement_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdspropertyinfo',
            name='basement_psqf',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='rdsremodelcost',
            name='basement_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='remodelcost',
            name='basement_price',
            field=models.CharField(blank=True, default='', max_length=255, null=True),
        ),
    ]
