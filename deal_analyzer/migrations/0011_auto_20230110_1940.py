# Generated by Django 3.2.15 on 2023-01-10 19:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0010_auto_20230107_0829'),
    ]

    operations = [
        migrations.AddField(
            model_name='brestresalevalue',
            name='desired_resale_value',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='estimatedresalevalue',
            name='desired_resale_value',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='ncestresalevalue',
            name='desired_resale_value',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='raaestresalevalue',
            name='desired_resale_value',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='rdsaestresalevalue',
            name='desired_resale_value',
            field=models.Integer<PERSON>ield(null=True),
        ),
        migrations.AddField(
            model_name='rdsestresalevalue',
            name='desired_resale_value',
            field=models.IntegerField(null=True),
        ),
    ]
