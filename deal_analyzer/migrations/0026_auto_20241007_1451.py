# Generated by Django 3.2.15 on 2024-10-07 14:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0025_onetimesubscription_sub_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='brpropertyinfo',
            name='additional_data',
            field=models.JSO<PERSON>ield(default=dict, null=True),
        ),
        migrations.AddField(
            model_name='ncpropertyinfo',
            name='additional_data',
            field=models.JSONField(default=dict, null=True),
        ),
        migrations.AddField(
            model_name='propertyinfo',
            name='additional_data',
            field=models.J<PERSON><PERSON>ield(default=dict, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='raapropertyinfo',
            name='additional_data',
            field=models.JSONField(default=dict, null=True),
        ),
        migrations.AddField(
            model_name='rdsapropertyinfo',
            name='additional_data',
            field=models.J<PERSON><PERSON>ield(default=dict, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='rdspropertyinfo',
            name='additional_data',
            field=models.JSO<PERSON>ield(default=dict, null=True),
        ),
    ]
