# Generated by Django 3.2.15 on 2023-03-01 17:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('deal_analyzer', '0014_brsummarytext_ncsummarytext_raasummarytext_rdsasummarytext_rdssummarytext'),
    ]

    operations = [
        migrations.CreateModel(
            name='RDSCustomComps',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(max_length=255, null=True)),
                ('close_date', models.CharField(max_length=255, null=True)),
                ('per_sqft', models.FloatField(null=True)),
                ('price', models.FloatField(null=True)),
                ('bedrooms', models.FloatField(null=True)),
                ('bathrooms', models.FloatField(null=True)),
                ('year_built', models.CharField(max_length=255, null=True)),
                ('sqft', models.FloatField(null=True)),
                ('lot_size', models.CharField(max_length=255, null=True)),
                ('analyzer', models.CharField(max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdspropertyinfo')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RDSACustomComps',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(max_length=255, null=True)),
                ('close_date', models.CharField(max_length=255, null=True)),
                ('per_sqft', models.FloatField(null=True)),
                ('price', models.FloatField(null=True)),
                ('bedrooms', models.FloatField(null=True)),
                ('bathrooms', models.FloatField(null=True)),
                ('year_built', models.CharField(max_length=255, null=True)),
                ('sqft', models.FloatField(null=True)),
                ('lot_size', models.CharField(max_length=255, null=True)),
                ('analyzer', models.CharField(max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.rdsapropertyinfo')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='RAACustomComps',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(max_length=255, null=True)),
                ('close_date', models.CharField(max_length=255, null=True)),
                ('per_sqft', models.FloatField(null=True)),
                ('price', models.FloatField(null=True)),
                ('bedrooms', models.FloatField(null=True)),
                ('bathrooms', models.FloatField(null=True)),
                ('year_built', models.CharField(max_length=255, null=True)),
                ('sqft', models.FloatField(null=True)),
                ('lot_size', models.CharField(max_length=255, null=True)),
                ('analyzer', models.CharField(max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.raapropertyinfo')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='NCCustomComps',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(max_length=255, null=True)),
                ('close_date', models.CharField(max_length=255, null=True)),
                ('per_sqft', models.FloatField(null=True)),
                ('price', models.FloatField(null=True)),
                ('bedrooms', models.FloatField(null=True)),
                ('bathrooms', models.FloatField(null=True)),
                ('year_built', models.CharField(max_length=255, null=True)),
                ('sqft', models.FloatField(null=True)),
                ('lot_size', models.CharField(max_length=255, null=True)),
                ('analyzer', models.CharField(max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.ncpropertyinfo')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BRCustomComps',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(max_length=255, null=True)),
                ('close_date', models.CharField(max_length=255, null=True)),
                ('per_sqft', models.FloatField(null=True)),
                ('price', models.FloatField(null=True)),
                ('bedrooms', models.FloatField(null=True)),
                ('bathrooms', models.FloatField(null=True)),
                ('year_built', models.CharField(max_length=255, null=True)),
                ('sqft', models.FloatField(null=True)),
                ('lot_size', models.CharField(max_length=255, null=True)),
                ('analyzer', models.CharField(max_length=255, null=True)),
                ('property_info', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deal_analyzer.brpropertyinfo')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
