# Generated by Django 3.2.15 on 2023-08-22 01:02

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('deal_analyzer', '0018_zillowcomps'),
    ]

    operations = [
        migrations.CreateModel(
            name='ZillowCompsSummary',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('average_total_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('average_gross_living_area', models.PositiveIntegerField()),
                ('average_lot_size', models.PositiveIntegerField()),
                ('average_average_price_per_square_footage', models.DecimalField(decimal_places=2, max_digits=10)),
                ('model_type', models.Char<PERSON>ield(max_length=255)),
                ('model_id', models.PositiveIntegerField()),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
