from operator import or_
from django.db.models import Q
from functools import reduce
from itertools import chain
import logging
import requests
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from deal_analyzer.exceptions.errors import CompsDoesNotExists
from deal_analyzer.models.abstract_models import Contractor

from deal_analyzer.models.straight_aesthetics_remodel import (
    EstimatedResaleValueComps,
)
from deal_analyzer.services.report import ReportTypeSwitch

# Get an instance of a logger
logger = logging.getLogger("aws_logger")
DETAIL = "queried comps returned"


def find_nearby_addresses(address, radius):
    # Replace YOUR_API_KEY with your actual Google Maps API key
    api_key = "AIzaSyB3pDExnW1mb1XA2Ig8Rm2hjH_A2l6trkc"
    base_url = "https://maps.googleapis.com/maps/api/geocode/json?"

    # Make a request to the Google Maps API to get the latitude and longitude of the given address
    params = {"address": address, "key": api_key}
    response = requests.get(base_url, params=params)
    data = response.json()

    # Extract the latitude and longitude of the given address
    lat = data["results"][0]["geometry"]["location"]["lat"]
    lng = data["results"][0]["geometry"]["location"]["lng"]

    # Use the latitude and longitude to find other addresses within the given radius
    nearby_url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?"
    nearby_params = {"location": f"{lat},{lng}", "radius": radius, "key": api_key}
    nearby_response = requests.get(nearby_url, params=nearby_params)
    nearby_data = nearby_response.json()
    # Extract the addresses of the nearby places
    nearby_addresses = [result["vicinity"] for result in nearby_data["results"]]

    return nearby_addresses


class GetComps(APIView):
    """Get all available comps for estimated resale value"""

    def get(self, *args, **kwargs):
        query = self.request.GET.get("query", None)

        # perform filter lookups
        if query:
            queryset = EstimatedResaleValueComps.objects.filter(
                Q(street__icontains=query)
                | Q(city__icontains=query)
                | Q(state_or_province__icontains=query)
            ).exclude(lot_size__isnull=True)[:10]
            return Response(
                {
                    "status": "success",
                    "detail": DETAIL,
                    "data": queryset.values(),
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "no comps available for query",
            },
            status=status.HTTP_404_NOT_FOUND,
        )

    def post(self, *args, **kwargs):
        request = self.request

        address_request = request.data.get("address", "")
        address = address_request if address_request else ""

        bed_request = request.data.get("beds", 0)
        beds = int(bed_request) if bed_request else 0

        bath_request = request.data.get("bath", 0)
        bath = int(bath_request) if bath_request else 0

        sqft_request = request.data.get("sqft", 0)
        sqft = int(sqft_request) if sqft_request else 0

        split_address = [x.strip() for x in address.split(",")]

        radius = "10000"  # in meters
        data = find_nearby_addresses(address, radius)
        split_main_address = [x.strip() for x in address.split(",")]
        final_streets = ["default"]  # reduce function requires an inital value
        for i in data:

            split_add = [x.strip() for x in i.split(",")]

            try:
                if split_add[-1] == split_main_address[-3]:
                    second = split_add[0].split(" ")
                    second.pop(0)
                    extracted_street = " ".join(second)
                    final_streets.append(extracted_street)
            except IndexError:
                pass
        final_streets = list(set(final_streets))
        if split_address and address_request:
            try:
                full_street = "".join(split_address[-4]).upper().split(" ")
                street = full_street[1]
                # house_number = int(full_street[0])
                state = "".join(split_address[-2]).upper()
                city = "".join(split_address[-3]).upper()
            except (IndexError, ValueError):
                raise CompsDoesNotExists

            properties_close_proximity = EstimatedResaleValueComps.objects.filter(
                reduce(
                    or_,
                    [
                        Q(
                            street__icontains=c,
                            city__iexact=city,
                            state_or_province__iexact=state,
                            sqft__range=(sqft - 10000, sqft + 10000),
                            bedrooms__range=(beds - 1, beds + 1),
                            bathrooms__range=(bath - 2, bath + 2),
                        )
                        for c in final_streets
                    ],
                )
            ).exclude(street="")[:10]
            property_on_same_street = EstimatedResaleValueComps.objects.filter(
                (
                    # Q(house_number__range=(house_number - 900, house_number + 900))
                    Q(street__icontains=street)
                    & Q(city__icontains=city)
                    & Q(state_or_province__iexact=state)
                ),
                sqft__range=(sqft - 10000, sqft + 10000),
                bedrooms__range=(beds - 1, beds + 1),
                bathrooms__range=(bath - 1, bath + 1),
            ).exclude(street="")[:10]

            result_list = list(
                chain(
                    property_on_same_street.values(),
                    properties_close_proximity.values(),
                )
            )

            return Response(
                {
                    "status": "success",
                    "detail": "Filtered queryset returned successfully",
                    "data": result_list,
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "No comps available for this address",
            },
            status=status.HTTP_404_NOT_FOUND,
        )


class SearchCustomComps(APIView):
    """Get all available comps for estimated resale value"""

    def get(self, *args, **kwargs):
        street = self.request.GET.get("street", None)
        city = self.request.GET.get("city", None)
        state = self.request.GET.get("state", None)

        # perform filter lookups
        queryset = None

        if state:
            queryset = EstimatedResaleValueComps.objects.filter(
                Q(state_or_province__icontains=state.upper())
            ).exclude(lot_size__isnull=True)[:100]

        if city:
            queryset = EstimatedResaleValueComps.objects.filter(
                Q(city__icontains=city.upper())
            ).exclude(lot_size__isnull=True)[:20]

        if street:
            queryset = EstimatedResaleValueComps.objects.filter(
                Q(site_address__icontains=street.upper())
            ).exclude(lot_size__isnull=True)[:1]

        if queryset:

            return Response(
                {
                    "status": "success",
                    "detail": DETAIL,
                    "data": queryset.values(),
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "success", "detail": "no comps available", "data": []},
                status=status.HTTP_200_OK,
            )


class CityAndCountyTaxView(APIView):
    """Get all available city and county tax"""

    def get(self, *args, **kwargs):
        location = self.request.GET.get("location", None)

        # perform filter lookups
        queryset = None
        if location:
            queryset = Contractor.objects.filter(
                Q(location__icontains=location.upper())
            )[:5]

        if queryset:

            return Response(
                {
                    "status": "success",
                    "detail": DETAIL,
                    "data": queryset.values(),
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "success", "detail": "no comps available", "data": []},
                status=status.HTTP_200_OK,
            )


class CreateCustomComps(APIView):
    """Create Custom Comps for the specified analyzer"""

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def put(self, *args, **kwargs):
        property_info = self.request.GET.get("id", None)
        analyzer = self.request.GET.get("analyzer", None)
        user = self.request.user

        request_data = self.request.data.get("custom_comps", None)

        my_switch = ReportTypeSwitch(
            analyzer, property_info, self.request.user, "check_type"
        )
        analyzer_type = my_switch.check_model()

        existing_obj = analyzer_type["custom_comps"].objects.filter(
            property_info_id=property_info, analyzer=analyzer, user=user
        )
        if existing_obj:
            existing_obj.delete()
        serializer = analyzer_type["serializer"](
            data=request_data,
            many=True,
            context={
                "property_info": property_info,
                "analyzer": analyzer,
                "user": user,
                "model_type": analyzer_type,
            },
        )

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "status": "success",
                    "detail": "operation request was successful",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "deal with this error",
                "data": serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
