from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from deal_analyzer.exceptions.errors import InvestorBadRequest, ResourceNotFound

from deal_analyzer.models.questionnaires.remodel_down_to_studs import (
    RemodelDownToStudsQuestions,
)
from deal_analyzer.permissions import SubscriptionPermission
from deal_analyzer.serializers.questionnaire.remodel_down_to_studs import (
    RemodelDownToStudsSerializer,
)


class RemodelDownToStudsView(APIView):

    permission_classes = (IsAuthenticated, SubscriptionPermission)
    authentication_classes = (JWTAuthentication,)

    def get(self, request, *args, **kwargs):
        id = request.GET.get("id", None)

        summaryy = RemodelDownToStudsQuestions.objects.filter(
            user=self.request.user, id=id
        ).values()

        return Response(
            {
                "status": "success",
                "detail": "successfully returned report summary",
                "data": summaryy,
            },
            status=status.HTTP_200_OK,
        )

    def post(self, request, format=None):

        if "investors" not in request.data:
            raise InvestorBadRequest

        serializer = RemodelDownToStudsSerializer(
            data=request.data,
            context={"request": request.data, "user": self.request.user},
        )

        if serializer.is_valid():
            serializer.save(user=self.request.user)
            return Response(
                {
                    "status": "success",
                    "detail": "operation was succeessful",
                    "data": {"id": serializer.data["id"]},
                },
                status=status.HTTP_201_CREATED,
            )

        return Response(
            {
                "status": "failure",
                "detail": "deal with this error",
                "data": serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    def put(self, request, *args, **kwargs):
        if "investors" not in request.data:
            raise InvestorBadRequest

        id = request.GET.get("id", None)
        try:

            instance = RemodelDownToStudsQuestions.objects.get(
                id=id, user=self.request.user
            )
        except RemodelDownToStudsQuestions.DoesNotExist:
            raise ResourceNotFound

        serializer = RemodelDownToStudsSerializer(
            instance,
            data=request.data,
            context={"request": request.data, "user": self.request.user},
        )

        if serializer.is_valid():
            serializer.save(user=self.request.user)
            return Response(
                {
                    "status": "success",
                    "detail": "operation was succeessful",
                    "data": serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )

        return Response(
            {
                "status": "failure",
                "detail": "deal with this error",
                "data": serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
