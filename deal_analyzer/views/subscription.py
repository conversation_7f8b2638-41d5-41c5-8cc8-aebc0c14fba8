from django.utils import timezone
import stripe
from django.db.models import Q
from django.conf import settings
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from deal_analyzer.permissions import SubscriptionPermission

from register.models import StripeSubscription

stripe.api_key = settings.STRIPE_SECRET_KEY
stripe.api_version = settings.STRIPE_API_VERSION


class SubscriptionListView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, request, *args, **kwargs):
        user_data = request.user

        query = StripeSubscription.objects.filter(user=user_data)

        return Response(
            {
                "status": "success",
                "detail": "successfully returned data",
                "data": query.values(),
            },
            status=status.HTTP_200_OK,
        )


class GetUserSubscription(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, *args, **kwargs):

        queryset = SubscriptionPermission().has_permission(self.request)

        return Response(
            {
                "status": "success",
                "detail": "user subscription type retrieved",
                "subscription_data": {"deal_analyzer": queryset},
            },
            status=status.HTTP_200_OK,
        )
