from itertools import chain
from django.db.models import IntegerField, Value

from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from deal_analyzer.exceptions.errors import (
    ClientEmailDoesNotExist,
    ReportResourceNotFound,
    RequestIdNotPaased,
)
from deal_analyzer.models.buy_and_rent import BRPropertyInfo
from deal_analyzer.models.new_construction import NCPropertyInfo
from deal_analyzer.models.remodel_add_addition import RAAPropertyInfo
from deal_analyzer.models.remodel_down_studs import RDSPropertyInfo
from deal_analyzer.models.remodel_down_to_studs_and_addition import RDSAPropertyInfo
from deal_analyzer.models.straight_aesthetics_remodel import PropertyInfo
from deal_analyzer.services.report import ReportTypeSwitch
from register.models import Client


class GetAllAnalysis(APIView):
    """Generate all analysis for a user"""

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):

        completed_report_sar = (
            PropertyInfo.objects.filter(user=self.request.user, completed_round=True)
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("1", output_field=IntegerField()))
        )
        completed_report_raa = (
            RAAPropertyInfo.objects.filter(user=self.request.user, completed_round=True)
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("2", output_field=IntegerField()))
        )
        completed_report_rds = (
            RDSPropertyInfo.objects.filter(user=self.request.user, completed_round=True)
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("3", output_field=IntegerField()))
        )
        completed_report_rdsa = (
            RDSAPropertyInfo.objects.filter(
                user=self.request.user, completed_round=True
            )
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("4", output_field=IntegerField()))
        )
        completed_report_nc = (
            NCPropertyInfo.objects.filter(user=self.request.user, completed_round=True)
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("5", output_field=IntegerField()))
        )
        completed_report_br = (
            BRPropertyInfo.objects.filter(user=self.request.user, completed_round=True)
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("6", output_field=IntegerField()))
        )

        completed_results = sorted(
            chain(
                completed_report_sar,
                completed_report_raa,
                completed_report_rds,
                completed_report_rdsa,
                completed_report_nc,
                completed_report_br,
            ),
            key=lambda instance: instance["created"],
            reverse=True,
        )

        uncompleted_report_sar = (
            PropertyInfo.objects.filter(user=self.request.user, completed_round=False)
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("1", output_field=IntegerField()))
        )
        uncompleted_report_raa = (
            RAAPropertyInfo.objects.filter(
                user=self.request.user, completed_round=False
            )
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("2", output_field=IntegerField()))
        )
        uncompleted_report_rds = (
            RDSPropertyInfo.objects.filter(
                user=self.request.user, completed_round=False
            )
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("3", output_field=IntegerField()))
        )
        uncompleted_report_rdsa = (
            RDSAPropertyInfo.objects.filter(
                user=self.request.user, completed_round=False
            )
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("4", output_field=IntegerField()))
        )
        uncompleted_report_nc = (
            NCPropertyInfo.objects.filter(user=self.request.user, completed_round=False)
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("5", output_field=IntegerField()))
        )
        uncompleted_report_br = (
            BRPropertyInfo.objects.filter(user=self.request.user, completed_round=False)
            .values("id", "property_address", "listing_date", "created", "updated")
            .annotate(report_type=Value("6", output_field=IntegerField()))
        )

        uncompleted_results = sorted(
            chain(
                uncompleted_report_sar,
                uncompleted_report_raa,
                uncompleted_report_rds,
                uncompleted_report_rdsa,
                uncompleted_report_nc,
                uncompleted_report_br,
            ),
            key=lambda instance: instance["created"],
            reverse=True,
        )

        return Response(
            {
                "status": "success",
                "detail": "All users analysis",
                "data": {
                    "completed": completed_results,
                    "uncompleted": uncompleted_results,
                },
            },
            status=status.HTTP_200_OK,
        )


class MarkReportAsCompleted(APIView):

    """Confirm that a report is completed"""

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, *args, **kwargs):

        property_id = self.request.GET.get("id", None)
        report_type = self.request.GET.get("report_type", None)

        report_type_obj = ReportTypeSwitch(
            report_type, property_id, self.request.user
        ).check_model()
        if report_type_obj == "wrong_report":
            raise ReportResourceNotFound

        return Response(
            {
                "status": "success",
                "detail": "operation request was successful",
                "data": {
                    "id": report_type_obj.id,
                    "completed_round": report_type_obj.completed_round,
                    "report_type": report_type,
                },
            },
            status=status.HTTP_200_OK,
        )


class SendReportClient(APIView):

    """Send the report to the client."""

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, *args, **kwargs):

        # property_id = self.request.GET.get("id", None)
        # report_type = self.request.GET.get("report_type", None)
        client_email = self.request.GET.get("client_email", None)

        try:
            report_type_obj = Client.objects.get(user__email=client_email)
        except Client.DoesNotExist:
            raise ClientEmailDoesNotExist
        if report_type_obj == "wrong_report":
            raise ReportResourceNotFound

        return Response(
            {
                "status": "success",
                "detail": "report was sent to client successfully",
                "data": {"id": report_type_obj.id, "gs": report_type_obj.user.email},
            },
            status=status.HTTP_200_OK,
        )


class DeleteReportView(APIView):

    """Delete A Report For Analyzer."""

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get_model_or_serializer(self,  request_ids=[]):
        data = None
        counters = 0
        for i in request_ids:
            my_switch = ReportTypeSwitch(
                i.get('type', None), i.get('id', None), self.request.user, "delete"
            )
            data = my_switch.check_model()

            if data == "wrong_report":
                counters += 1

        return counters

    def post(self, *args, **kwargs):

        request_ids = self.request.data.get("ids", [])
        if request_ids == []:
            raise RequestIdNotPaased
        
        report_type_obj = self.get_model_or_serializer(request_ids)

        if report_type_obj:
            raise ReportResourceNotFound

        return Response(
            {
                "status": "success",
                "detail": "delete operation request was successful",
            },
            status=status.HTTP_204_NO_CONTENT,
        )
