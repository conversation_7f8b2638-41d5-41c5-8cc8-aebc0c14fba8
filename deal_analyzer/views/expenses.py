from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from deal_analyzer import refactor_investors_profit, refactor_data
from deal_analyzer.models.expenses import Expenses, <PERSON>illowComps, ZillowCompsSummary
from deal_analyzer.serializers.expenses import (
    ExpensesSerializer,
    ZillowCompsSerializer,
    ZillowCompsSummarySerializer,
)


class ExpensesAPIView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, request, *args, **kwargs):
        model_type = request.query_params.get("model_type", None)
        model_id = request.query_params.get("model_id", None)

        if model_type and model_id:
            expenses = Expenses.objects.filter(model_type=model_type, model_id=model_id)
        else:
            expenses = Expenses.objects.none()

        serializer = ExpensesSerializer(
            expenses, many=True, context={"request": request}
        )
        data = refactor_data(serializer.data)
        return Response(data, status=status.HTTP_200_OK)

    def put(self, request, *args, **kwargs):
        model_type = request.query_params.get("model_type", None)
        model_id = request.query_params.get("model_id", None)

        if model_type is None or model_id is None:
            return Response(
                {"error": "Both model_type and model_id are required fields."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        request_data = request.data.copy()
        dollar_or_percent = request_data.get("dollar_percent_checker", None)
        investors_profit = request_data.get("investors_profit", [])
        investors_profit_list = refactor_investors_profit(
            investors_profit, dollar_or_percent
        )

        formatted_data = {
            "investment_values_dollar_or_percent": dollar_or_percent,
            "total_liquid_capital_required": request_data.get(
                "totalLiquidRequired", None
            ),
            "total_expenses": request_data.get("totalExpenseAmount", None),
            "estimated_net_profit": request_data.get("estNetProfit", None),
            "investors_profit": investors_profit_list,
            "model_type": model_type,
            "model_id": model_id,
        }

        try:
            expenses = Expenses.objects.get(model_type=model_type, model_id=model_id)
            serializer = ExpensesSerializer(expenses, data=formatted_data)
        except Expenses.DoesNotExist:
            serializer = ExpensesSerializer(data=formatted_data)

        if serializer.is_valid():
            serializer.save()
            return Response({"status": "successful"}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ZillowCompsView(APIView):
    queryset = ZillowComps
    serializer_class = ZillowCompsSerializer

    def get(self, request):
        model_type = request.query_params.get("model_type", None)
        model_id = request.query_params.get("model_id", None)

        if model_type and model_id:
            listings = self.queryset.objects.filter(
                model_type=model_type, model_id=model_id
            )
        else:
            listings = self.queryset.objects.none()

        serializer = self.serializer_class(listings, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request):
        model_type = request.query_params.get("model_type", None)
        model_id = request.query_params.get("model_id", None)
        zillow_comps = request.data.get("zillow_comps", None)

        if model_type is None or model_id is None or zillow_comps is None:
            return Response(
                {"message": "Missing required parameters."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        for zillow_comps_data in zillow_comps:
            zillow_comps_data["model_type"] = model_type
            zillow_comps_data["model_id"] = model_id
            # try:
            #     existing_record = ZillowComps.objects.get(
            #         model_type=model_type, model_id=model_id
            #     )
            #     serializer = self.serializer_class(existing_record, data=zillow_comps_data)
            # except ZillowComps.DoesNotExist:
            serializer = self.serializer_class(data=zillow_comps_data)

            if serializer.is_valid():
                serializer.save()
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # return Response(
        #     {"message": "Zillow Comps updated successfully."}, status=status.HTTP_200_OK
        # )
        return self.get(request)


    def delete(self, request):
        zillowcomps_ids = request.data.get("ids", [])

        if not zillowcomps_ids:
            return Response(
                {"message": "No ZillowComps IDs provided for deletion."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        deleted_ids = []

        for zillowcomps_id in zillowcomps_ids:
            try:
                zillowcomps_instance = self.queryset.objects.get(id=zillowcomps_id)
                zillowcomps_instance.delete()
                deleted_ids.append(zillowcomps_id)
            except ZillowComps.DoesNotExist:
                pass

        if deleted_ids:
            return Response(
                {
                    "message": f"ZillowComps records with IDs {deleted_ids} deleted successfully."
                },
                status=status.HTTP_204_NO_CONTENT,
            )
        else:
            return Response(
                {"message": "No matching ZillowComps records found."},
                status=status.HTTP_404_NOT_FOUND,
            )


class ZillowCompsSummaryView(APIView):
    queryset = ZillowCompsSummary
    serializer_class = ZillowCompsSummarySerializer

    def get(self, request):
        model_type = request.query_params.get("model_type", None)
        model_id = request.query_params.get("model_id", None)

        if model_type is None or model_id is None:
            return Response({'message': 'Missing required parameters.'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            remodel_data = self.queryset.objects.get(model_type=model_type, model_id=model_id)
            serializer = self.serializer_class(remodel_data)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except self.queryset.DoesNotExist:
            return Response({'message': 'Summary not found.'}, status=status.HTTP_404_NOT_FOUND)

    def put(self, request):
        model_type = request.query_params.get("model_type", None)
        model_id = request.query_params.get("model_id", None)
        summary = request.data.get("summary", None)

        if model_type is None or model_id is None or summary is None:
            return Response({'message': 'Missing required parameters.'}, status=status.HTTP_400_BAD_REQUEST)
        summary["model_type"] = model_type
        summary["model_id"] = model_id
        try:
            existing_record = self.queryset.objects.get(model_type=model_type, model_id=model_id)
            serializer = self.serializer_class(existing_record, data=summary)
        except self.queryset.DoesNotExist:
            serializer = self.serializer_class(data=summary)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
