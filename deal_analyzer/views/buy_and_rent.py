from rest_framework import status

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from deal_analyzer.exceptions.errors import (
    RequestIdNotPaased,
    ResourceNotFound,
    WrongModelRequest,
)
from deal_analyzer.models.buy_and_rent import BRPropertyInfo
from deal_analyzer.permissions import SubscriptionPermission
from deal_analyzer.serializers.buy_and_rent import BRPropertyInfoSerializer
from deal_analyzer.services.buy_and_rent import BRModelSwitch


class BuyAndRent(APIView):

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get_model_or_serializer(self, model_name, property_id=None):

        my_switch = BRModelSwitch(self.request.user, property_id)
        data = my_switch.check_model(model_name)
        if data == "wrong_model":
            raise WrongModelRequest
        return data

    def get(self, request, *args, **kwargs):

        property_id = request.GET.get("id", None)
        if not property_id:
            raise RequestIdNotPaased

        data = self.get_model_or_serializer("all_data", property_id)

        return Response(
            {
                "status": "success",
                "detail": "successfully returned data",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    def put(self, request, *args, **kwargs):
        model_name = None
        for key in self.request.data:
            model_name = key

        instance_id = request.GET.get("id", None)
        if not instance_id:
            raise RequestIdNotPaased
        request_data = request.data

        raa_property_info = None

        data = self.get_model_or_serializer(model_name, instance_id)
        if model_name == "property_information":
            instance_model = data["model_name"].objects.filter(
                id=instance_id, user=self.request.user
            )
        else:
            instance_model = data["model_name"].objects.filter(
                property_info=instance_id
            )

        if instance_model:
            try:
                raa_property_info = BRPropertyInfo.objects.get(
                    id=instance_id, user=self.request.user
                )
            except Exception:
                raise ResourceNotFound
            serializer = data["serializer"](
                instance_model.last(),
                data=request_data[model_name],
                context={
                    "request": request_data,
                    "user": self.request.user,
                    "raa_property_info": raa_property_info,
                    "instance_id": instance_id
                },
            )
        else:
            try:
                if model_name != "property_information":
                    raa_property_info = BRPropertyInfo.objects.get(
                        id=instance_id, user=self.request.user
                    )
            except Exception:
                raise ResourceNotFound
            serializer = data["serializer"](
                data=request_data[model_name],
                context={
                    "request": request_data,
                    "user": self.request.user,
                    "raa_property_info": raa_property_info,
                    "instance_id": instance_id
                },
            )

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "status": "success",
                    "detail": "operation request was successful",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "deal with this error",
                "data": serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


class CreateBRPropertyinfo(APIView):
    """Create Initial Property Info"""

    permission_classes = (IsAuthenticated, SubscriptionPermission)
    authentication_classes = (JWTAuthentication,)

    def post(self, *args, **kwargs):
        request_data = {}

        serializer = BRPropertyInfoSerializer(
            data=request_data,
            context={"request": request_data, "user": self.request.user},
        )

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "status": "success",
                    "detail": "operation request was successful",
                    "data": {"id": serializer.data["id"]},
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "deal with this error",
                "data": serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


class BuyAndRentSummary(APIView):

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get_model_or_serializer(self, model_name, property_id=None):

        my_switch = BRModelSwitch(self.request.user, property_id)
        data = my_switch.check_model(model_name)
        if data == "wrong_model":
            raise WrongModelRequest
        return data

    def get(self, *args, **kwargs):

        property_id = self.request.GET.get("id", None)
        if not property_id:
            raise RequestIdNotPaased

        report_summary = self.get_model_or_serializer("summary_service", property_id)

        return Response(
            {
                "status": "success",
                "detail": "successfully returned report summary",
                "data": report_summary,
            },
            status=status.HTTP_200_OK,
        )
