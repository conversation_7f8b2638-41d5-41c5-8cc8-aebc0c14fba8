from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from deal_analyzer.models.straight_aesthetics_remodel import PropertyInfo
from deal_analyzer.exceptions.errors import (
    RequestIdNotPaased,
    ResourceNotFound,
    WrongModelRequest,
)
from deal_analyzer.serializers.straight_aesthetics_remodel import PropertyInfoSerializer
from deal_analyzer.services.straight_aesthetics_remodel import ModelSwitch
from deal_analyzer.permissions import SubscriptionPermission


class StraightRemodelAddSqft(APIView):

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get_model_or_serializer(self, model_name, property_id=None):

        my_switch = ModelSwitch(self.request.user, property_id)
        data = my_switch.check_model(model_name)
        if data == "wrong_model":
            raise WrongModelRequest
        return data

    def get(self, request, *args, **kwargs):

        property_id = request.GET.get("id", None)
        if not property_id:
            raise RequestIdNotPaased
        data = self.get_model_or_serializer("all_data", property_id)

        return Response(
            {
                "status": "success",
                "detail": "successfully returned data",
                "data": data,
            },
            status=status.HTTP_200_OK,
        )

    def put(self, request, *args, **kwargs):
        model_name = None
        for key in self.request.data:
            model_name = key

        instance_id = request.GET.get("id", None)
        if not instance_id:
            raise RequestIdNotPaased
        request_data = request.data

        property_info = None

        data = self.get_model_or_serializer(model_name, instance_id)
        if model_name == "property_information":
            instance_model = data["model_name"].objects.filter(
                id=instance_id, user=self.request.user
            )
        else:
            instance_model = data["model_name"].objects.filter(
                property_info=instance_id
            )

        if instance_model:

            try:
                property_info = PropertyInfo.objects.get(
                    id=instance_id, user=self.request.user
                )

            except Exception:
                raise ResourceNotFound
            serializer = data["serializer"](
                instance_model.last(),
                data=request_data[model_name],
                context={
                    "request": request_data,
                    "user": self.request.user,
                    "property_info": property_info,
                    "instance_id": instance_id
                },
            )

        else:
            try:
                if model_name != "property_information":
                    property_info = PropertyInfo.objects.get(
                        id=instance_id, user=self.request.user
                    )
            except Exception:
                raise ResourceNotFound
            serializer = data["serializer"](
                data=request_data[model_name],
                context={
                    "request": request_data,
                    "user": self.request.user,
                    "property_info": property_info,
                    "instance_id": instance_id
                },
            )

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "status": "success",
                    "detail": "operation request was successful",
                    "data": serializer.data,
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "deal with this error",
                "data": serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


class StraightRemodelAddSqfSummary(APIView):

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get_model_or_serializer(self, model_name, property_id=None):

        my_switch = ModelSwitch(self.request.user, property_id)
        data = my_switch.check_model(model_name)
        if data == "wrong_model":
            raise WrongModelRequest
        return data

    def get(self, *args, **kwargs):

        property_id = self.request.GET.get("id", None)
        if not property_id:
            raise RequestIdNotPaased

        report_summary = self.get_model_or_serializer("summary_service", property_id)

        return Response(
            {
                "status": "success",
                "detail": "successfully returned report summary",
                "data": report_summary,
            },
            status=status.HTTP_200_OK,
        )


class CheckUserHasPendingRound(APIView):
    """Check if user has pending round and return its id"""

    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        property_info = (
            PropertyInfo.objects.filter(user=self.request.user, completed_round=False)
            .values()
            .last()
        )

        if property_info:
            return Response(
                {
                    "status": "success",
                    "pending": True,
                    "report_id": property_info["id"],
                },
                status=status.HTTP_200_OK,
            )
        return Response(
            {
                "status": "failure",
                "pending": False,
            },
            status=status.HTTP_404_NOT_FOUND,
        )


class CreatePropertyinfo(APIView):
    """Create Initial Property Info"""

    permission_classes = [IsAuthenticated]
    # SubscriptionPermission
    authentication_classes = (JWTAuthentication,)

    def post(self, *args, **kwargs):

        request_data = {}

        serializer = PropertyInfoSerializer(
            data=request_data,
            context={"request": request_data, "user": self.request.user},
        )

        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "status": "success",
                    "detail": "operation request was successful",
                    "data": {"id": serializer.data["id"]},
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {
                "status": "failure",
                "detail": "deal with this error",
                "data": serializer.errors,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
