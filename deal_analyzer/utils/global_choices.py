LISTING_SOURCE_TYPE = (
    (0, "BPO Homes"),
    (1, "Mls"),
    (2, "<PERSON>illow"),
    (3, "Others"),
)


PROPERTY_TYPE = (
    (0, "Single Family Home"),
    (1, "Townhouse"),
    (2, "Condo"),
    (3, "Duplex"),
    (4, "Triplex"),
    (5, "Fourplex"),
)

DEAL_FINANCE_OPTION = (
    (0, "Obtain Primary Home Loan and a Secondary Loan"),
    (1, "Obtain Primary Home Loan and Use Cash for Remodel"),
    (2, "Obtain Single Primary Home Loan For Home and Remodel"),
    (3, "Obtain a Primary Remodel Loan and Pay Cash For Home"),
    (4, "Pay All Cash for Home and Remodel"),
    (5, "Obtain a Primary Loan for Rental"),
)

DEAL_FINANCE_OPTION_NEW_CONSTRUCTIOn = (
    (0, "Obtain Single Loan for Land and New Construction"),
    (1, "Obtain Primary Loan For Land and A Secondary Loan for New Construction "),
    (2, "Obtain Primary Loan For New Construction and Pay Cash For Land"),
    (3, "Obtain a Primary Loan For Land and Pay Cash New Construction"),
    (4, "Pay Cash for Land and New Construction"),
)

LOAN_TYPE = (
    (0, "Conventional Loan"),
    (1, "Private Hard Money"),
    (2, "Seller Financing"),
)

SUB_LOAN_TYPE = ((0, "Conventional Mortgage Loan"), (1, "Interest Only Mortgage Loan"))


DollarOrPercent = ((0, "Dollar"), (1, "Percent"))

MonthsYearOption = ((0, "Months"), (1, "Year"))

LENDER_POINTS_OPTIONS = (
    (0, "Add Lender Points to Loan Amount"),
    (1, "Add Lender Points to Upfront Closing Cost"),
    (2, "Add Lender Points to Back End Closing Costs"),
)

WHOLESALE_FEE_OPTIONS = (
    (0, "Add Wholesale Fee to Loan Amount"),
    (1, "Add Wholesale Fee to Upfront Closing Cost"),
    (2, "Add Wholesale Fee to Back End Closing Cost"),
)

PROPERTY_TYPE = (
    (0, "Single Family Home"),
    (1, "Townhouse"),
    (2, "Condo"),
    (3, "Duplex"),
    (4, "Triplex"),
    (5, "Fourplex"),
    (6, "Others"),
)


INTERVAL_MONTHLY = "monthly"
INTERVAL_YEARLY = "yearly"

INTERVAL_CHOICES = [
    (INTERVAL_MONTHLY, INTERVAL_MONTHLY),
    (INTERVAL_YEARLY, INTERVAL_YEARLY),
]

STATUS_ACTIVE = "active"
STATUS_PAST_DUE = "past_due"
STATUS_UNPAID = "unpaid"
STATUS_CANCELED = "canceled"
STATUS_INCOMPLETE = "incomplete"
STATUS_INCOMPLETE_EXPIRED = "incomplete_expired"
STATUS_TRIALING = "trialing"

STATUS_CHOICES = [
    (STATUS_ACTIVE, STATUS_ACTIVE),
    (STATUS_PAST_DUE, STATUS_PAST_DUE),
    (STATUS_UNPAID, STATUS_UNPAID),
    (STATUS_CANCELED, STATUS_CANCELED),
    (STATUS_INCOMPLETE, STATUS_INCOMPLETE),
    (STATUS_INCOMPLETE_EXPIRED, STATUS_INCOMPLETE_EXPIRED),
    (STATUS_TRIALING, STATUS_TRIALING),
]

STATE_PENDING = 0
STATE_PROCESSING = 1
STATE_PROCESSED = 2
STATE_FAILED = 3
STATE_CHOICES = [
    (STATE_PENDING, "Pending"),
    (STATE_PROCESSING, "Processing"),
    (STATE_PROCESSED, "Processed"),
    (STATE_FAILED, "Failed"),
]

PENDING = "Pending"
USED = "Used"
ONETIME_STATUS = ((PENDING, "Pending"), (USED, "Used"))
SUBSCRIPTION_TYPE = (("internship", "internship"), ("deal_analyzer", "deal_analyzer"))

TITLEANDESCROWFEES = (
    (0, "Buy Payes and Add Title and Escrow Fees to Frontend Closing Cost"),
    (1, "Seller Pays Add Title and Escrow Fees to Backend Closing Cost"),
    (2, "Split Title and Escrow Fees"),
    (3, "Not Applicable"),
)

REMODEL_COST_OPTION = (
    (0, "Basic Remodel Cost Calculations"),
    (1, "Advanced Remodel Cost Calculations"),
)

COMPS_OPTION = (
    (1, "Custom Comps"),
    (2, "BPOHomes Comps"),
    (3, "Both Custom Comps and BPOHomes Comps"),
)

RESALE_VALUE_OPTION = ((1, "BPO Estimate"), (2, "Desired Resale Value"))
