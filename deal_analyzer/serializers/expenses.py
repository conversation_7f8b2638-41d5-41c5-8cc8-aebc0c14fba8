from rest_framework import serializers
from deal_analyzer.models.expenses import (
    InvestorProfitCalc,
    Expenses,
    ZillowComps,
    ZillowCompsSummary,
)


class InvestorProfitSerializer(serializers.ModelSerializer):
    class Meta:
        model = InvestorProfitCalc
        fields = "__all__"


class ExpensesSerializer(serializers.ModelSerializer):
    investors_profit = InvestorProfitSerializer(many=True)

    class Meta:
        model = Expenses
        fields = "__all__"

    def create(self, validated_data):
        investors_profit_data = validated_data.pop("investors_profit")
        expenses_instance = Expenses.objects.create(**validated_data)
        self._create_and_link_investor_profits(expenses_instance, investors_profit_data)
        return expenses_instance

    def update(self, instance, validated_data):
        investors_profit_data = validated_data.pop("investors_profit")
        instance = super().update(instance, validated_data)
        self._replace_investor_profits(instance, investors_profit_data)
        return instance

    def _create_and_link_investor_profits(
        self, expenses_instance, investors_profit_data
    ):
        for investor_data in investors_profit_data:
            investor_profit = InvestorProfitCalc.objects.create(
                expenses=expenses_instance, **investor_data
            )
            expenses_instance.investors_profit.add(investor_profit)

    def _replace_investor_profits(self, expenses_instance, investors_profit_data):
        expenses_instance.investors_profit.all().delete()

        for investor_data in investors_profit_data:
            investor_profit = InvestorProfitCalc.objects.create(
                expenses=expenses_instance, **investor_data
            )
            expenses_instance.investors_profit.add(investor_profit)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["investors_profit"] = InvestorProfitSerializer(
            instance.investors_profit.all(), many=True
        ).data
        return representation


class ZillowCompsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ZillowComps
        fields = "__all__"


class ZillowCompsSummarySerializer(serializers.ModelSerializer):
    class Meta:
        model = ZillowCompsSummary
        fields = "__all__"
