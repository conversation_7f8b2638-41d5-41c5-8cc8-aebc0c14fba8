from django.shortcuts import get_object_or_404
from rest_framework import serializers
from deal_analyzer.models.remodel_add_addition import (
    RAAAdditionRemodelItems,
    RAAAestheticItems,
    RAACarryingMonths,
    RAAClosingCost,
    RAACustomComps,
    RAAEstResaleValue,
    RAAFinanceOptions,
    RAAInvestorProfit,
    RAANonAestheticItems,
    RAAOtherCosts,
    RAAPropertyInfo,
    RAAPropertyManagement,
    RAAPropertyPermitFees,
    RAARemodelCost,
    RAARentalIncome,
    RAASummaryText,
    RAATaxes,
)
from deal_analyzer.serializers.global_serializer import (
    BaseNullValuesSerializer,
    create_advanced_remodel_cost,
)
from deal_analyzer.services.gloabl_service import calculate_average_values_for_comps


class RAAPropertyInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAPropertyInfo
        fields = "__all__"
        extra_kwargs = {"user": {"read_only": True}}

    def create(self, validated_data):
        user = self.context["user"]

        validated_data.update({"user": user})
        instance = super(RAAPropertyInfoSerializer, self).create(validated_data)

        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAAPropertyInfoSerializer, self).update(
            instance, validated_data
        )

        return instance


class RAAFinanceOptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAFinanceOptions
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAAFinanceOptionsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAAFinanceOptionsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAAPropertyPermitFeesSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAPropertyPermitFees
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAAPropertyPermitFeesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAAPropertyPermitFeesSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAATaxesSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAATaxes
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAATaxesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAATaxesSerializer, self).update(instance, validated_data)
        return instance


class RAAAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RAAAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAAAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAAAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAANonAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RAANonAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(RAANonAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAANonAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAAAdditionRemodelItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RAAAdditionRemodelItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(RAAAdditionRemodelItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAAAdditionRemodelItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAARemodelCostSerializer(serializers.ModelSerializer):
    aesthetic_items = RAAAestheticItemsSerializer(many=True, required=False)
    non_aesthetic_items = RAANonAestheticItemsSerializer(many=True, required=False)
    additional_remodel_items = RAAAdditionRemodelItemsSerializer(many=True, required=False)

    class Meta:
        model = RAARemodelCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        validated_data.update({"property_info": raa_property_info})

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            RAANonAestheticItems,
            RAAAestheticItems,
            RAAAdditionRemodelItems
        )

        instance = RAARemodelCost.objects.create(
            property_info=raa_property_info,
            est_remodel_cost_psqft=validated_data.get("est_remodel_cost_psqft", None),
            est_new_construction_cost_psqft=validated_data.get(
                "est_new_construction_cost_psqft", None
            ),
        )

        validated_data.update({"id": instance.id})
        return validated_data

    def update(self, instance, validated_data, *args, **kwargs):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            RAANonAestheticItems,
            RAAAestheticItems,
            RAAAdditionRemodelItems
        )
        instance = super(RAARemodelCostSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAAClosingCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAClosingCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAAClosingCostSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAAClosingCostSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAARentalIncomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAARentalIncome
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAARentalIncomeSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAARentalIncomeSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAAPropertyManagementSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAPropertyManagement
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAAPropertyManagementSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAAPropertyManagementSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAAOtherCostsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAOtherCosts
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAAOtherCostsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAAOtherCostsSerializer, self).update(instance, validated_data)
        return instance


class RAACarryingMonthsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAACarryingMonths
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAACarryingMonthsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAACarryingMonthsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RAAEstResaleValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAEstResaleValue
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        user = self.context["user"]
        est_resale_value = self.context["request"]["est_resale_value"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]
        validated_data.update({"property_info": raa_property_info})
        data = calculate_average_values_for_comps(
            est_resale_value, raa_property_info, user, analyzer,  instance_id, RAACustomComps
        )
        validated_data.update(data)

        instance = super(RAAEstResaleValueSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        est_resale_value = self.context["request"]["est_resale_value"]
        property_info = self.context["raa_property_info"]
        instance_id = self.context["instance_id"]
        user = self.context["user"]
        analyzer = est_resale_value.get("analyzer", None)
        data = calculate_average_values_for_comps(
            est_resale_value, property_info, user, analyzer, instance_id, RAACustomComps
        )
        validated_data.update(data)
        instance = super(RAAEstResaleValueSerializer, self).update(
            instance, validated_data
        )
        return instance


def create_investor_profit(
    property_info, investment_values, investment_values_dollar_or_percent
):

    existing_obj = RAAInvestorProfit.objects.filter(property_info_id=property_info.id)
    _dict = []

    if existing_obj:
        existing_obj.delete()

    for data in investment_values:
        non_instance, created = RAAInvestorProfit.objects.update_or_create(
            investor=data["investor"],
            property_info_id=property_info.id,
            defaults={
                "equity": data["equity"],
                "profit": data["profit"],
                "investment_values_dollar_or_percent": investment_values_dollar_or_percent,
            },
        )
        if created:
            non_instance.save()

        _dict.append(non_instance)
    return _dict


class RAAInvestorProfitSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAInvestorProfit
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"raa_property_info": raa_property_info})
        try:
            investment_values = request.get("investment_values", None)
        except KeyError:
            return []
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        instance = create_investor_profit(
            raa_property_info,
            investment_values,
            investment_values_dollar_or_percent,
        )

        return instance[0] if instance else instance

    def update(self, instance, validated_data, *args, **kwargs):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        investment_values = request.get("investment_values", None)
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        all_objects = create_investor_profit(
            raa_property_info, investment_values, investment_values_dollar_or_percent
        )

        return all_objects[0] if all_objects else all_objects


class RAASummaryTextSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAASummaryText
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RAASummaryTextSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RAASummaryTextSerializer, self).update(instance, validated_data)
        return instance


class RAACustomCompsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAACustomComps
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        analyzer = self.context["analyzer"]
        user = self.context["user"]
        model_type = self.context["model_type"]
        property_instance = get_object_or_404(model_type['model'], id=property_info)
        price = validated_data.get("price", None)
        sqft = validated_data.get("sqft", None)
        try:
            per_sqft = round((price / sqft), 2)
        except Exception:
            per_sqft = 0

        validated_data.update(
            {
                "property_info": property_instance,
                "analyzer": analyzer,
                "user": user,
                "per_sqft": per_sqft,
            }
        )

        instance = super(RAACustomCompsSerializer, self).create(validated_data)
        return instance
