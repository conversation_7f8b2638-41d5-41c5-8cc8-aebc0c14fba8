import logging
from django.shortcuts import get_object_or_404
from rest_framework import serializers
from deal_analyzer.models.straight_aesthetics_remodel import (
    AdditionRemodelItems,
    AestheticItems,
    CarryingCost,
    ClosingCost,
    CustomComps,
    EstimatedResaleValue,
    FinanceOptions,
    InvestorProfit,
    NonAestheticItems,
    OtherCosts,
    PropertyInfo,
    PropertyManagement,
    PropertyPermitFees,
    RemodelCost,
    RentalIncome,
    SummaryText,
    Taxes,
)
from deal_analyzer.serializers.global_serializer import (
    BaseNullValuesSerializer,
    create_advanced_remodel_cost,
)

from deal_analyzer.services.gloabl_service import calculate_average_values_for_comps

logger = logging.getLogger("aws_logger")


class PropertyInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyInfo
        fields = "__all__"
        extra_kwargs = {"user": {"read_only": True}}

    def create(self, validated_data):
        user = self.context["user"]

        validated_data.update({"user": user})
        instance = super(PropertyInfoSerializer, self).create(validated_data)

        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(PropertyInfoSerializer, self).update(instance, validated_data)

        return instance


class FinanceOptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = FinanceOptions
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(FinanceOptionsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(FinanceOptionsSerializer, self).update(
            instance, validated_data
        )
        return instance



class PropertyPermitFeesSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyPermitFees
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(PropertyPermitFeesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(PropertyPermitFeesSerializer, self).update(
            instance, validated_data
        )
        return instance


class TaxesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Taxes
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(TaxesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(TaxesSerializer, self).update(instance, validated_data)
        return instance


class AestheticItemsSerializer(BaseNullValuesSerializer, serializers.ModelSerializer):
    class Meta:
        model = AestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(AestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(AestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class NonAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = NonAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info.last()})
        instance = super(NonAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NonAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class AdditionRemodelItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = AdditionRemodelItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info.last()})
        instance = super(AdditionRemodelItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(AdditionRemodelItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RemodelCostSerializer(serializers.ModelSerializer):
    aesthetic_items = AestheticItemsSerializer(many=True, required=False)
    non_aesthetic_items = NonAestheticItemsSerializer(many=True, required=False)

    class Meta:
        model = RemodelCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = None

        validated_data.update({"property_info": property_info})

        create_advanced_remodel_cost(
            property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            NonAestheticItems,
            AestheticItems,
            AdditionRemodelItems,
        )

        instance = RemodelCost.objects.create(
            property_info=property_info,
            est_remodel_cost_psqft=validated_data.get("est_remodel_cost_psqft", None),
        )

        validated_data.update({"id": instance.id})
        return validated_data

    def update(self, instance, validated_data, *args, **kwargs):
        property_info = self.context["property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = None

        create_advanced_remodel_cost(
            property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            NonAestheticItems,
            AestheticItems,
            AdditionRemodelItems,
        )
        instance = super(RemodelCostSerializer, self).update(instance, validated_data)
        return instance


class ClosingCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClosingCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(ClosingCostSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(ClosingCostSerializer, self).update(instance, validated_data)
        return instance


class RentalIncomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = RentalIncome
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(RentalIncomeSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RentalIncomeSerializer, self).update(instance, validated_data)
        return instance


class PropertyManagementSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyManagement
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(PropertyManagementSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(PropertyManagementSerializer, self).update(
            instance, validated_data
        )
        return instance


class OtherCostsSerializer(serializers.ModelSerializer):
    class Meta:
        model = OtherCosts
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(OtherCostsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(OtherCostsSerializer, self).update(instance, validated_data)
        return instance


class CarryingCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = CarryingCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(CarryingCostSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(CarryingCostSerializer, self).update(instance, validated_data)
        return instance


class EstimatedResaleValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = EstimatedResaleValue
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        user = self.context["user"]
        est_resale_value = self.context["request"]["est_resale_value"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]

        validated_data.update({"property_info": property_info})
        data = calculate_average_values_for_comps(
            est_resale_value, property_info, user, analyzer, instance_id, CustomComps
        )
        validated_data.update(data)

        instance = super(EstimatedResaleValueSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        est_resale_value = self.context["request"]["est_resale_value"]
        property_info = self.context["property_info"]
        user = self.context["user"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]
        data = calculate_average_values_for_comps(
            est_resale_value, property_info, user, analyzer, instance_id, CustomComps
        )
        validated_data.update(data)
        instance = super(EstimatedResaleValueSerializer, self).update(
            instance, validated_data
        )
        return instance


def create_investor_profit(
    property_info, investment_values, investment_values_dollar_or_percent
):

    existing_obj = InvestorProfit.objects.filter(property_info_id=property_info.id)
    _dict = []

    if existing_obj:
        existing_obj.delete()
    if investment_values:
        for data in investment_values:
            non_instance, created = InvestorProfit.objects.update_or_create(
                investor=data["investor"],
                property_info_id=property_info.id,
                defaults={
                    "equity": data["equity"],
                    "profit": data["profit"],
                    "investment_values_dollar_or_percent": investment_values_dollar_or_percent,
                },
            )
            if created:
                non_instance.save()

            _dict.append(non_instance)
    return _dict


class InvestorProfitSerializer(serializers.ModelSerializer):
    class Meta:
        model = InvestorProfit
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        request = self.context["request"]["investor_profit"]
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        try:
            investment_values = request.get("investment_values", None)
        except KeyError:
            return []
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        instance = create_investor_profit(
            property_info, investment_values, investment_values_dollar_or_percent
        )

        return instance[0] if instance else instance

    def update(self, instance, validated_data, *args, **kwargs):
        request = self.context["request"]["investor_profit"]
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        investment_values = request.get("investment_values", None)
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        all_objects = create_investor_profit(
            property_info, investment_values, investment_values_dollar_or_percent
        )

        return all_objects[0] if all_objects else all_objects


class SummaryTextSerializer(serializers.ModelSerializer):
    class Meta:
        model = SummaryText
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info})
        instance = super(SummaryTextSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(SummaryTextSerializer, self).update(instance, validated_data)
        return instance


class CustomCompsSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomComps
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        analyzer = self.context["analyzer"]
        user = self.context["user"]
        model_type = self.context["model_type"]
        property_instance = get_object_or_404(model_type['model'], id=property_info)
        price = validated_data.get("price", None)
        sqft = validated_data.get("sqft", None)
        try:
            per_sqft = round((price / sqft), 2)
        except Exception:
            per_sqft = 0

        validated_data.update(
            {
                "property_info": property_instance,
                "analyzer": analyzer,
                "user": user,
                "per_sqft": per_sqft,
            }
        )

        instance = super(CustomCompsSerializer, self).create(validated_data)
        return instance
