from django.shortcuts import get_object_or_404
from rest_framework import serializers
from deal_analyzer.models.new_construction import (
    NCAdditionRemodelItems,
    NCAestheticItems,
    NCCarryingMonths,
    NCClosingCost,
    NCCustomComps,
    NCDemolishingCost,
    NCDevelopmentCost,
    NCEstResaleValue,
    NCFinanceOptions,
    NCInvestorProfit,
    NCNonAestheticItems,
    NCOtherCosts,
    NCPropertyInfo,
    NCPropertyManagement,
    NCPropertyPermitFees,
    NCRentalIncome,
    NCSummaryText,
    NCTaxes,
)
from deal_analyzer.serializers.global_serializer import (
    BaseNullValuesSerializer,
    create_advanced_remodel_cost,
)
from deal_analyzer.services.gloabl_service import calculate_average_values_for_comps


class NCPropertyInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCPropertyInfo
        fields = "__all__"
        extra_kwargs = {"user": {"read_only": True}}

    def create(self, validated_data):
        user = self.context["user"]

        validated_data.update({"user": user})
        instance = super(NCPropertyInfoSerializer, self).create(validated_data)

        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCPropertyInfoSerializer, self).update(
            instance, validated_data
        )

        return instance


class NCFinanceOptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCFinanceOptions
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCFinanceOptionsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCFinanceOptionsSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCPropertyPermitFeesSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCPropertyPermitFees
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCPropertyPermitFeesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCPropertyPermitFeesSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCTaxesSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCTaxes
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCTaxesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCTaxesSerializer, self).update(instance, validated_data)
        return instance


class NCAestheticItemsSerializer(BaseNullValuesSerializer, serializers.ModelSerializer):
    class Meta:
        model = NCAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCNonAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = NCNonAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(NCNonAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCNonAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCAdditionRemodelItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = NCAdditionRemodelItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(NCAdditionRemodelItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCAdditionRemodelItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCDevelopmentCostSerializer(serializers.ModelSerializer):
    aesthetic_items = NCAestheticItemsSerializer(many=True, required=False)
    non_aesthetic_items = NCNonAestheticItemsSerializer(many=True, required=False)
    additional_remodel_items = NCAdditionRemodelItemsSerializer(many=True, required=False)

    class Meta:
        model = NCDevelopmentCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        validated_data.update({"property_info": raa_property_info})

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            NCNonAestheticItems,
            NCAestheticItems,
            NCAdditionRemodelItems
        )

        instance = NCDevelopmentCost.objects.create(
            property_info=raa_property_info,
            new_construction_cost_psqft=validated_data.get(
                "new_construction_cost_psqft", None
            ),
        )

        validated_data.update({"id": instance.id})
        return validated_data

    def update(self, instance, validated_data, *args, **kwargs):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            NCNonAestheticItems,
            NCAestheticItems,
            NCAdditionRemodelItems
        )
        instance = super(NCDevelopmentCostSerializer, self).update(instance, validated_data)
        return instance


class NCDemolishingCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCDemolishingCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCDemolishingCostSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCDemolishingCostSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCClosingCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCClosingCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCClosingCostSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCClosingCostSerializer, self).update(instance, validated_data)
        return instance


class NCRentalIncomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCRentalIncome
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCRentalIncomeSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCRentalIncomeSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCPropertyManagementSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCPropertyManagement
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCPropertyManagementSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCPropertyManagementSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCOtherCostsSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCOtherCosts
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCOtherCostsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCOtherCostsSerializer, self).update(instance, validated_data)
        return instance


class NCCarryingMonthsSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCCarryingMonths
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCCarryingMonthsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCCarryingMonthsSerializer, self).update(
            instance, validated_data
        )
        return instance


class NCEstResaleValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCEstResaleValue
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        user = self.context["user"]
        est_resale_value = self.context["request"]["est_resale_value"]
        analyzer = est_resale_value.get("analyzer", None)
        validated_data.update({"property_info": raa_property_info})
        instance_id = self.context["instance_id"]
        data = calculate_average_values_for_comps(
            est_resale_value, raa_property_info, user, analyzer, instance_id, NCCustomComps
        )
        validated_data.update(data)

        instance = super(NCEstResaleValueSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        est_resale_value = self.context["request"]["est_resale_value"]
        property_info = self.context["raa_property_info"]
        user = self.context["user"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]
        data = calculate_average_values_for_comps(
            est_resale_value, property_info, user, analyzer, instance_id, NCCustomComps
        )
        validated_data.update(data)
        instance = super(NCEstResaleValueSerializer, self).update(
            instance, validated_data
        )
        return instance


def create_investor_profit(
    property_info, investment_values, investment_values_dollar_or_percent
):

    existing_obj = NCInvestorProfit.objects.filter(property_info_id=property_info.id)
    _dict = []

    if existing_obj:
        existing_obj.delete()

    for data in investment_values:
        non_instance, created = NCInvestorProfit.objects.update_or_create(
            investor=data["investor"],
            property_info_id=property_info.id,
            defaults={
                "equity": data["equity"],
                "profit": data["profit"],
                "investment_values_dollar_or_percent": investment_values_dollar_or_percent,
            },
        )
        if created:
            non_instance.save()

        _dict.append(non_instance)
    return _dict


class NCInvestorProfitSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCInvestorProfit
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"raa_property_info": raa_property_info})
        try:
            investment_values = request.get("investment_values", None)
        except KeyError:
            return []
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        instance = create_investor_profit(
            raa_property_info,
            investment_values,
            investment_values_dollar_or_percent,
        )

        return instance[0] if instance else instance

    def update(self, instance, validated_data, *args, **kwargs):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        investment_values = request.get("investment_values", None)
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        all_objects = create_investor_profit(
            raa_property_info, investment_values, investment_values_dollar_or_percent
        )

        return all_objects[0] if all_objects else all_objects


class NCSummaryTextSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCSummaryText
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(NCSummaryTextSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(NCSummaryTextSerializer, self).update(instance, validated_data)
        return instance


class NCCustomCompsSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCCustomComps
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        analyzer = self.context["analyzer"]
        user = self.context["user"]
        model_type = self.context["model_type"]
        property_instance = get_object_or_404(model_type['model'], id=property_info)
        price = validated_data.get("price", None)
        sqft = validated_data.get("sqft", None)
        try:
            per_sqft = round((price / sqft), 2)
        except Exception:
            per_sqft = 0

        validated_data.update(
            {
                "property_info": property_instance,
                "analyzer": analyzer,
                "user": user,
                "per_sqft": per_sqft,
            }
        )

        instance = super(NCCustomCompsSerializer, self).create(validated_data)
        return instance
