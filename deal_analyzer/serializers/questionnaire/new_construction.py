from rest_framework import serializers
from deal_analyzer.models.questionnaires.new_construction import NCInvestors, NewConstructionQuestions



class NCInvestorsSerializer(serializers.ModelSerializer):
    class Meta:
        model = NCInvestors
        fields = "__all__"
           
class NewConstructionSerializer(serializers.ModelSerializer):
    investors = NCInvestorsSerializer(many=True, required=False, read_only=True)
    
    class Meta:
        model = NewConstructionQuestions
        fields = "__all__"
        extra_kwargs = {"investors": {"read_only": True}}


    def create(self, validated_data):
        instance = NewConstructionQuestions.objects.create(**validated_data)

        return instance

    def update(self, instance, validated_data):
        investors = self.context["request"]["investors"]
        instance = super(NewConstructionSerializer, self).update(instance, validated_data)
        instance.save()
        existing_obj = NCInvestors.objects.filter(
                new_construction=instance.id)

        if existing_obj:
            existing_obj.delete()
        _dict = []
        
        for i in investors:                
            non_instance = NCInvestors.objects.create(
                investor_value=i["investor_value"], new_construction=instance
            )
            _dict.append(non_instance)
        
        return instance