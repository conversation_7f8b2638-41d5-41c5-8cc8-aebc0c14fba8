from rest_framework import serializers
from deal_analyzer.models.questionnaires.remodel_add_addition import RemodelAddAdditionQuestions, RAAInvestors



class RAAInvestorsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RAAInvestors
        fields = "__all__"
           
class RemodelAddAdditionSerializer(serializers.ModelSerializer):
    investors = RAAInvestorsSerializer(many=True, required=False, read_only=True)
    
    class Meta:
        model = RemodelAddAdditionQuestions
        fields = "__all__"
        extra_kwargs = {"investors": {"read_only": True}}

    def create(self, validated_data):
        instance = RemodelAddAdditionQuestions.objects.create(**validated_data)

        return instance

    def update(self, instance, validated_data):
        investors = self.context["request"]["investors"]
        instance = super(RemodelAddAdditionSerializer, self).update(instance, validated_data)
        instance.save()
        existing_obj = RAAInvestors.objects.filter(
                remodel_add_addition=instance.id)

        if existing_obj:
            existing_obj.delete()
        _dict = []
        
        for i in investors:                
            non_instance = RAAInvestors.objects.create(
                investor_value=i["investor_value"], remodel_add_addition=instance
            )
            _dict.append(non_instance)
        
        return instance