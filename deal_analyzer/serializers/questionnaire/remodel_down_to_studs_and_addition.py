from rest_framework import serializers
from deal_analyzer.models.questionnaires.remodel_down_to_studs_and_addition import RemodelDownToStudsAndAdditionQuestions, RDSAInvestors



class RDSAInvestorsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAInvestors
        fields = "__all__"
           
class RemodelDownToStudsAndAdditionSerializer(serializers.ModelSerializer):
    investors = RDSAInvestorsSerializer(many=True, required=False, read_only=True)
    
    class Meta:
        model = RemodelDownToStudsAndAdditionQuestions
        fields = "__all__"
        extra_kwargs = {"investors": {"read_only": True}}


    def create(self, validated_data):
        instance = RemodelDownToStudsAndAdditionQuestions.objects.create(**validated_data)

        return instance

    def update(self, instance, validated_data):
        investors = self.context["request"]["investors"]
        instance = super(RemodelDownToStudsAndAdditionSerializer, self).update(instance, validated_data)
        instance.save()
        existing_obj = RDSAInvestors.objects.filter(
                remodel_down_to_studs_and_addition=instance.id)

        if existing_obj:
            existing_obj.delete()
        _dict = []
        
        for i in investors:                
            non_instance = RDSAInvestors.objects.create(
                investor_value=i["investor_value"], remodel_down_to_studs_and_addition=instance
            )
            _dict.append(non_instance)
        
        return instance
