from rest_framework import serializers
from deal_analyzer.models.questionnaires.straight_aesthetics import (
    StraightAestheticsQuestions,
    Investors,
)


class InvestorsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Investors
        fields = "__all__"


class StraightAestheticsSerializer(serializers.ModelSerializer):
    investors = InvestorsSerializer(many=True, required=False, read_only=True)

    class Meta:
        model = StraightAestheticsQuestions
        fields = "__all__"
        extra_kwargs = {"investors": {"read_only": True}}

    def create(self, validated_data):
        instance = StraightAestheticsQuestions.objects.create(**validated_data)

        return instance

    def update(self, instance, validated_data):
        investors = self.context["request"]["investors"]
        instance = super(StraightAestheticsSerializer, self).update(instance, validated_data)
        instance.save()
        existing_obj = Investors.objects.filter(
                straight_aesthetics=instance.id)

        if existing_obj:
            existing_obj.delete()
        _dict = []
        
        for i in investors: 
            investor_value = i.get("investor_value", None)             
            non_instance = Investors.objects.create(
                investor_value=investor_value, straight_aesthetics=instance
            )
            _dict.append(non_instance)
        
        return instance
