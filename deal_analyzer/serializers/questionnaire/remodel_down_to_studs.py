from rest_framework import serializers
from deal_analyzer.models.questionnaires.remodel_down_to_studs import RDSInvestors, RemodelDownToStudsQuestions



class RDSInvestorsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSInvestors
        fields = "__all__"
           
class RemodelDownToStudsSerializer(serializers.ModelSerializer):
    investors = RDSInvestorsSerializer(many=True, required=False, read_only=True)
    
    class Meta:
        model = RemodelDownToStudsQuestions
        fields = "__all__"
        extra_kwargs = {"investors": {"read_only": True}}


    def create(self, validated_data):
        instance = RemodelDownToStudsQuestions.objects.create(**validated_data)

        return instance

    def update(self, instance, validated_data):
        investors = self.context["request"]["investors"]
        instance = super(RemodelDownToStudsSerializer, self).update(instance, validated_data)
        instance.save()
        existing_obj = RDSInvestors.objects.filter(
                remodel_down_to_studs=instance.id)

        if existing_obj:
            existing_obj.delete()
        _dict = []
        
        for i in investors:                
            non_instance = RDSInvestors.objects.create(
                investor_value=i["investor_value"], remodel_down_to_studs=instance
            )
            _dict.append(non_instance)
        
        return instance
