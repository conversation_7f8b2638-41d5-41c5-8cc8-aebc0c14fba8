from rest_framework import serializers
from deal_analyzer.models.questionnaires.buy_and_rent import BuyAndRentQuestions, BRInvestors



class BRInvestorsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRInvestors
        fields = "__all__"
           
class BuyAndRentQuestionsSerializer(serializers.ModelSerializer):
    investors = BRInvestorsSerializer(many=True, required=False, read_only=True)
    
    class Meta:
        model = BuyAndRentQuestions
        fields = "__all__"
        extra_kwargs = {"investors": {"read_only": True}}


    def create(self, validated_data):
        instance = BuyAndRentQuestions.objects.create(**validated_data)

        return instance

    def update(self, instance, validated_data):
        investors = self.context["request"]["investors"]
        instance = super(BuyAndRentQuestionsSerializer, self).update(instance, validated_data)
        instance.save()
        existing_obj = BRInvestors.objects.filter(
                buy_and_rent=instance.id)

        if existing_obj:
            existing_obj.delete()
        _dict = []
        
        for i in investors:                
            non_instance = BRInvestors.objects.create(
                investor_value=i["investor_value"], buy_and_rent=instance
            )
            _dict.append(non_instance)
        
        return instance
