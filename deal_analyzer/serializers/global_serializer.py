from rest_framework import serializers

# All of them are open to reform. Reduce from O(n^2) to O(n)


def create_aesthetics_items(property_info, aesthetic_items, aesthetic_items_model):
    existing_obj = aesthetic_items_model.objects.filter(
        property_info_id=property_info.id
    )
    if existing_obj:
        existing_obj.delete()
    if aesthetic_items:
        for data in aesthetic_items:
            if not data["value"]:
                continue
            if not data["item"]:
                continue
            non_instance, created = aesthetic_items_model.objects.update_or_create(
                item=data["item"],
                property_info_id=property_info.id,
                defaults={"value": data["value"]},
            )
            if created:
                non_instance.save()


def create_nonaesthetics_items(
    property_info, non_aesthetic_items, non_aesthetic_items_model
):
    existing_obj = non_aesthetic_items_model.objects.filter(
        property_info_id=property_info.id
    )
    if existing_obj:
        existing_obj.delete()

    if non_aesthetic_items:
        for data in non_aesthetic_items:
            if not data["value"]:
                continue
            if not data["item"]:
                continue
            non_instance, created = non_aesthetic_items_model.objects.update_or_create(
                item=data["item"],
                property_info_id=property_info.id,
                defaults={"value": data["value"]},
            )
            if created:
                non_instance.save()


def create_additional_remodel_items(
    property_info, additional_remodel_items, additional_remodel_items_model
):
    existing_obj = additional_remodel_items_model.objects.filter(
        property_info_id=property_info.id
    )
    if existing_obj:
        existing_obj.delete()

    if additional_remodel_items:
        for data in additional_remodel_items:
            if not data["value"]:
                continue
            if not data["item"]:
                continue
            non_instance, created = additional_remodel_items_model.objects.update_or_create(
                item=data["item"],
                property_info_id=property_info.id,
                defaults={"value": data["value"]},
            )
            if created:
                non_instance.save()


def create_advanced_remodel_cost(
    property_info,
    non_aesthetic_items,
    aesthetic_items,
    additional_remodel_items,
    non_aesthetic_items_model,
    aesthetic_items_model,
    additional_remodel_items_model,
):

    create_nonaesthetics_items(
        property_info, non_aesthetic_items, non_aesthetic_items_model
    )
    create_aesthetics_items(property_info, aesthetic_items, aesthetic_items_model)
    create_additional_remodel_items(
        property_info, additional_remodel_items, additional_remodel_items_model
    )


class BaseNullValuesSerializer(serializers.ModelSerializer):
    item = serializers.CharField(
        default="", required=False, allow_null=True, allow_blank=True
    )
    value = serializers.CharField(
        default=0, required=False, allow_null=True, allow_blank=True
    )
