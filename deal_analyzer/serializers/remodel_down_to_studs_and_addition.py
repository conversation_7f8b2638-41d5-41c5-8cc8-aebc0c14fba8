from django.shortcuts import get_object_or_404
from rest_framework import serializers


from deal_analyzer.models.remodel_down_to_studs_and_addition import (
    RDSAAdditionRemodelItems,
    RDSAAestheticItems,
    RDSACarryingMonths,
    RDSAClosingCost,
    RDSACustomComps,
    RDSAEstResaleValue,
    RDSAFinanceOptions,
    RDSAInvestorProfit,
    RDSANonAestheticItems,
    RDSAOtherCosts,
    RDSAPropertyInfo,
    RDSAPropertyManagement,
    RDSAPropertyPermitFees,
    RDSARemodelCost,
    RDSARentalIncome,
    RDSASummaryText,
    RDSATaxes,
)
from deal_analyzer.serializers.global_serializer import (
    BaseNullValuesSerializer,
    create_advanced_remodel_cost,
)
from deal_analyzer.services.gloabl_service import calculate_average_values_for_comps


class RDSAPropertyInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAPropertyInfo
        fields = "__all__"
        extra_kwargs = {"user": {"read_only": True}}

    def create(self, validated_data):
        user = self.context["user"]

        validated_data.update({"user": user})
        instance = super(RDSAPropertyInfoSerializer, self).create(validated_data)

        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAPropertyInfoSerializer, self).update(
            instance, validated_data
        )

        return instance


class RDSAFinanceOptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAFinanceOptions
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSAFinanceOptionsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAFinanceOptionsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSAPropertyPermitFeesSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAPropertyPermitFees
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSAPropertyPermitFeesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAPropertyPermitFeesSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSATaxesSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSATaxes
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSATaxesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSATaxesSerializer, self).update(instance, validated_data)
        return instance


class RDSAAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RDSAAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSAAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSANonAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RDSANonAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(RDSANonAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSANonAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSAAdditionRemodelItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RDSAAdditionRemodelItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(RDSAAdditionRemodelItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAAdditionRemodelItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSARemodelCostSerializer(serializers.ModelSerializer):
    aesthetic_items = RDSAAestheticItemsSerializer(many=True, required=False)
    non_aesthetic_items = RDSANonAestheticItemsSerializer(many=True, required=False)
    additional_remodel_items = RDSAAdditionRemodelItemsSerializer(many=True, required=False)

    class Meta:
        model = RDSARemodelCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        validated_data.update({"property_info": raa_property_info})

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            RDSANonAestheticItems,
            RDSAAestheticItems,
            RDSAAdditionRemodelItems
        )

        instance = RDSARemodelCost.objects.create(
            property_info=raa_property_info,
            est_remodel_cost_psqft=validated_data.get("est_remodel_cost_psqft", None),
            est_new_construction_cost_psqft=validated_data.get(
                "est_new_construction_cost_psqft", None
            ),
        )

        validated_data.update({"id": instance.id})
        return validated_data

    def update(self, instance, validated_data, *args, **kwargs):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            RDSANonAestheticItems,
            RDSAAestheticItems,
            RDSAAdditionRemodelItems
        )
        instance = super(RDSARemodelCostSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSAClosingCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAClosingCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSAClosingCostSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAClosingCostSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSARentalIncomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSARentalIncome
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSARentalIncomeSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSARentalIncomeSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSAPropertyManagementSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAPropertyManagement
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSAPropertyManagementSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAPropertyManagementSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSAOtherCostsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAOtherCosts
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSAOtherCostsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAOtherCostsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSACarryingMonthsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSACarryingMonths
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSACarryingMonthsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSACarryingMonthsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSAEstResaleValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAEstResaleValue
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        user = self.context["user"]
        est_resale_value = self.context["request"]["est_resale_value"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]
        validated_data.update({"property_info": raa_property_info})
        data = calculate_average_values_for_comps(
            est_resale_value, raa_property_info, user, analyzer, instance_id, RDSACustomComps
        )
        validated_data.update(data)

        instance = super(RDSAEstResaleValueSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        est_resale_value = self.context["request"]["est_resale_value"]
        property_info = self.context["raa_property_info"]
        user = self.context["user"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]
        data = calculate_average_values_for_comps(
            est_resale_value, property_info, user, analyzer, instance_id, RDSACustomComps
        )
        validated_data.update(data)
        instance = super(RDSAEstResaleValueSerializer, self).update(
            instance, validated_data
        )
        return instance


def create_investor_profit(
    property_info, investment_values, investment_values_dollar_or_percent
):

    existing_obj = RDSAInvestorProfit.objects.filter(property_info_id=property_info.id)
    _dict = []

    if existing_obj:
        existing_obj.delete()

    for data in investment_values:
        non_instance, created = RDSAInvestorProfit.objects.update_or_create(
            investor=data["investor"],
            property_info_id=property_info.id,
            defaults={
                "equity": data["equity"],
                "profit": data["profit"],
                "investment_values_dollar_or_percent": investment_values_dollar_or_percent,
            },
        )
        if created:
            non_instance.save()

        _dict.append(non_instance)
    return _dict


class RDSAInvestorProfitSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSAInvestorProfit
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"raa_property_info": raa_property_info})
        try:
            investment_values = request.get("investment_values", None)
        except KeyError:
            return []
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        instance = create_investor_profit(
            raa_property_info,
            investment_values,
            investment_values_dollar_or_percent,
        )

        return instance[0] if instance else instance

    def update(self, instance, validated_data, *args, **kwargs):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        investment_values = request.get("investment_values", None)
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        all_objects = create_investor_profit(
            raa_property_info, investment_values, investment_values_dollar_or_percent
        )

        return all_objects[0] if all_objects else all_objects


class RDSASummaryTextSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSASummaryText
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSASummaryTextSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSASummaryTextSerializer, self).update(instance, validated_data)
        return instance


class RDSACustomCompsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSACustomComps
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        analyzer = self.context["analyzer"]
        user = self.context["user"]
        model_type = self.context["model_type"]
        property_instance = get_object_or_404(model_type['model'], id=property_info)
        price = validated_data.get("price", None)
        sqft = validated_data.get("sqft", None)
        try:
            per_sqft = round((price / sqft), 2)
        except Exception:
            per_sqft = 0

        validated_data.update(
            {
                "property_info": property_instance,
                "analyzer": analyzer,
                "user": user,
                "per_sqft": per_sqft,
            }
        )

        instance = super(RDSACustomCompsSerializer, self).create(validated_data)
        return instance
