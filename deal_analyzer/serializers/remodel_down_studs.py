from django.shortcuts import get_object_or_404
from rest_framework import serializers
from deal_analyzer.models.remodel_down_studs import (
    RDSAdditionRemodelItems,
    RDSAestheticItems,
    RDSCarryingMonths,
    RDSClosingCost,
    RDSCustomComps,
    RDSEstResaleValue,
    RDSFinanceOptions,
    RDSInvestorProfit,
    RDSNonAestheticItems,
    RDSOtherCosts,
    RDSPropertyInfo,
    RDSPropertyManagement,
    RDSPropertyPermitFees,
    RDSRemodelCost,
    RDSRentalIncome,
    RDSSummaryText,
    RDSTaxes,
)
from deal_analyzer.serializers.global_serializer import (
    BaseNullValuesSerializer,
    create_advanced_remodel_cost,
)
from deal_analyzer.services.gloabl_service import calculate_average_values_for_comps


class RDSPropertyInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSPropertyInfo
        fields = "__all__"
        extra_kwargs = {"user": {"read_only": True}}

    def create(self, validated_data):
        user = self.context["user"]

        validated_data.update({"user": user})
        instance = super(RDSPropertyInfoSerializer, self).create(validated_data)

        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSPropertyInfoSerializer, self).update(
            instance, validated_data
        )

        return instance


class RDSFinanceOptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSFinanceOptions
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSFinanceOptionsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSFinanceOptionsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSPropertyPermitFeesSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSPropertyPermitFees
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSPropertyPermitFeesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSPropertyPermitFeesSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSTaxesSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSTaxes
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSTaxesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSTaxesSerializer, self).update(instance, validated_data)
        return instance


class RDSAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RDSAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSNonAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RDSNonAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(RDSNonAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSNonAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSAdditionRemodelItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = RDSAdditionRemodelItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        validated_data.update({"property_info": property_info.last()})
        instance = super(RDSAdditionRemodelItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSAdditionRemodelItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSRemodelCostSerializer(serializers.ModelSerializer):
    aesthetic_items = RDSAestheticItemsSerializer(many=True, required=False)
    non_aesthetic_items = RDSNonAestheticItemsSerializer(many=True, required=False)
    additional_remodel_items = RDSAdditionRemodelItemsSerializer(many=True, required=False)

    class Meta:
        model = RDSRemodelCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        validated_data.update({"property_info": raa_property_info})

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            RDSNonAestheticItems,
            RDSAestheticItems,
            RDSAdditionRemodelItems
        )

        instance = RDSRemodelCost.objects.create(
            property_info=raa_property_info,
            est_remodel_cost_psqft=validated_data.get("est_remodel_cost_psqft", None),
            est_new_construction_cost_psqft=validated_data.get(
                "est_new_construction_cost_psqft", None
            ),
        )

        validated_data.update({"id": instance.id})
        return validated_data

    def update(self, instance, validated_data, *args, **kwargs):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            RDSNonAestheticItems,
            RDSAestheticItems,
            RDSAdditionRemodelItems
        )
        instance = super(RDSRemodelCostSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSClosingCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSClosingCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSClosingCostSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSClosingCostSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSRentalIncomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSRentalIncome
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSRentalIncomeSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSRentalIncomeSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSPropertyManagementSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSPropertyManagement
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSPropertyManagementSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSPropertyManagementSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSOtherCostsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSOtherCosts
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSOtherCostsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSOtherCostsSerializer, self).update(instance, validated_data)
        return instance


class RDSCarryingMonthsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSCarryingMonths
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSCarryingMonthsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSCarryingMonthsSerializer, self).update(
            instance, validated_data
        )
        return instance


class RDSEstResaleValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSEstResaleValue
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        user = self.context["user"]
        est_resale_value = self.context["request"]["est_resale_value"]
        analyzer = est_resale_value.get("analyzer", None)
        validated_data.update({"property_info": raa_property_info})
        instance_id = self.context["instance_id"]
        data = calculate_average_values_for_comps(
            est_resale_value, raa_property_info, user, analyzer, instance_id, RDSCustomComps
        )
        validated_data.update(data)

        instance = super(RDSEstResaleValueSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        est_resale_value = self.context["request"]["est_resale_value"]
        property_info = self.context["raa_property_info"]
        user = self.context["user"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]
        data = calculate_average_values_for_comps(
            est_resale_value, property_info, user, analyzer, instance_id, RDSCustomComps
        )
        validated_data.update(data)
        instance = super(RDSEstResaleValueSerializer, self).update(
            instance, validated_data
        )
        return instance


def create_investor_profit(
    property_info, investment_values, investment_values_dollar_or_percent
):

    existing_obj = RDSInvestorProfit.objects.filter(property_info_id=property_info.id)
    _dict = []

    if existing_obj:
        existing_obj.delete()

    for data in investment_values:
        non_instance, created = RDSInvestorProfit.objects.update_or_create(
            investor=data["investor"],
            property_info_id=property_info.id,
            defaults={
                "equity": data["equity"],
                "profit": data["profit"],
                "investment_values_dollar_or_percent": investment_values_dollar_or_percent,
            },
        )
        if created:
            non_instance.save()

        _dict.append(non_instance)
    return _dict


class RDSInvestorProfitSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSInvestorProfit
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"raa_property_info": raa_property_info})
        try:
            investment_values = request.get("investment_values", None)
        except KeyError:
            return []
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        instance = create_investor_profit(
            raa_property_info,
            investment_values,
            investment_values_dollar_or_percent,
        )

        return instance[0] if instance else instance

    def update(self, instance, validated_data, *args, **kwargs):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        investment_values = request.get("investment_values", None)
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        all_objects = create_investor_profit(
            raa_property_info, investment_values, investment_values_dollar_or_percent
        )

        return all_objects[0] if all_objects else all_objects


class RDSSummaryTextSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSSummaryText
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(RDSSummaryTextSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(RDSSummaryTextSerializer, self).update(instance, validated_data)
        return instance


class RDSCustomCompsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RDSCustomComps
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        analyzer = self.context["analyzer"]
        user = self.context["user"]
        model_type = self.context["model_type"]
        property_instance = get_object_or_404(model_type['model'], id=property_info)
        price = validated_data.get("price", None)
        sqft = validated_data.get("sqft", None)
        try:
            per_sqft = round((price / sqft), 2)
        except Exception:
            per_sqft = 0

        validated_data.update(
            {
                "property_info": property_instance,
                "analyzer": analyzer,
                "user": user,
                "per_sqft": per_sqft,
            }
        )

        instance = super(RDSCustomCompsSerializer, self).create(validated_data)
        return instance
