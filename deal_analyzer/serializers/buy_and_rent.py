from django.shortcuts import get_object_or_404
from rest_framework import serializers
from deal_analyzer.models.buy_and_rent import (
    BRAdditionRemodelItems,
    BRAestheticItems,
    BRCarryingMonths,
    BRClosingCost,
    BRCustomComps,
    BREstResaleValue,
    BRFinanceOptions,
    BRInvestorProfit,
    BRNonAestheticItems,
    BROtherCosts,
    BRPropertyInfo,
    BRPropertyManagement,
    BRPropertyPermitFees,
    BRRemodelCost,
    BRRentalIncome,
    BRSummaryText,
    BRTaxes,
)
from deal_analyzer.serializers.global_serializer import (
    BaseNullValuesSerializer,
    create_advanced_remodel_cost,
)
from deal_analyzer.services.gloabl_service import calculate_average_values_for_comps


class BRPropertyInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRPropertyInfo
        fields = "__all__"
        extra_kwargs = {"user": {"read_only": True}}

    def create(self, validated_data):
        user = self.context["user"]

        validated_data.update({"user": user})
        instance = super(BRPropertyInfoSerializer, self).create(validated_data)

        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRPropertyInfoSerializer, self).update(
            instance, validated_data
        )

        return instance


class BRFinanceOptionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRFinanceOptions
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRFinanceOptionsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRFinanceOptionsSerializer, self).update(
            instance, validated_data
        )
        return instance


class BRPropertyPermitFeesSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRPropertyPermitFees
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRPropertyPermitFeesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRPropertyPermitFeesSerializer, self).update(
            instance, validated_data
        )
        return instance


class BRTaxesSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRTaxes
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRTaxesSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRTaxesSerializer, self).update(instance, validated_data)
        return instance


class BRAestheticItemsSerializer(BaseNullValuesSerializer, serializers.ModelSerializer):
    class Meta:
        model = BRAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class BRNonAestheticItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = BRNonAestheticItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(BRNonAestheticItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRNonAestheticItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class BRAdditionRemodelItemsSerializer(
    BaseNullValuesSerializer, serializers.ModelSerializer
):
    class Meta:
        model = BRAdditionRemodelItems
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info.last()})
        instance = super(BRAdditionRemodelItemsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRAdditionRemodelItemsSerializer, self).update(
            instance, validated_data
        )
        return instance


class BRRemodelCostSerializer(serializers.ModelSerializer):
    aesthetic_items = BRAestheticItemsSerializer(many=True, required=False)
    non_aesthetic_items = BRNonAestheticItemsSerializer(many=True, required=False)
    additional_remodel_items = BRAdditionRemodelItemsSerializer(many=True, required=False)

    class Meta:
        model = BRRemodelCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        validated_data.update({"property_info": raa_property_info})

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            BRNonAestheticItems,
            BRAestheticItems,
            BRAdditionRemodelItems
        )

        instance = BRRemodelCost.objects.create(
            property_info=raa_property_info,
            est_remodel_cost_psqft=validated_data.get("est_remodel_cost_psqft", None),
            est_new_construction_cost_psqft=validated_data.get(
                "est_new_construction_cost_psqft", None
            ),
        )

        validated_data.update({"id": instance.id})
        return validated_data

    def update(self, instance, validated_data, *args, **kwargs):
        raa_property_info = self.context["raa_property_info"]
        non_aesthetic_items = validated_data.get("non_aesthetic_items", None)
        aesthetic_items = validated_data.get("aesthetic_items", None)
        additional_remodel_items = validated_data.get("additional_remodel_items", None)

        create_advanced_remodel_cost(
            raa_property_info,
            non_aesthetic_items,
            aesthetic_items,
            additional_remodel_items,
            BRNonAestheticItems,
            BRAestheticItems,
            BRAdditionRemodelItems
        )
        instance = super(BRRemodelCostSerializer, self).update(instance, validated_data)
        return instance


class BRClosingCostSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRClosingCost
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRClosingCostSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRClosingCostSerializer, self).update(instance, validated_data)
        return instance


class BRRentalIncomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRRentalIncome
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRRentalIncomeSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRRentalIncomeSerializer, self).update(
            instance, validated_data
        )
        return instance


class BRPropertyManagementSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRPropertyManagement
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRPropertyManagementSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRPropertyManagementSerializer, self).update(
            instance, validated_data
        )
        return instance


class BROtherCostsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BROtherCosts
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BROtherCostsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BROtherCostsSerializer, self).update(instance, validated_data)
        return instance


class BRCarryingMonthsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRCarryingMonths
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRCarryingMonthsSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRCarryingMonthsSerializer, self).update(
            instance, validated_data
        )
        return instance


class BREstResaleValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = BREstResaleValue
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        user = self.context["user"]
        est_resale_value = self.context["request"]["est_resale_value"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]
        validated_data.update({"property_info": raa_property_info})
        data = calculate_average_values_for_comps(
            est_resale_value, raa_property_info, user, analyzer, instance_id, BRCustomComps
        )
        validated_data.update(data)

        instance = super(BREstResaleValueSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        est_resale_value = self.context["request"]["est_resale_value"]
        property_info = self.context["raa_property_info"]
        user = self.context["user"]
        analyzer = est_resale_value.get("analyzer", None)
        instance_id = self.context["instance_id"]
        data = calculate_average_values_for_comps(
            est_resale_value, property_info, user, analyzer, instance_id, BRCustomComps
        )
        validated_data.update(data)
        instance = super(BREstResaleValueSerializer, self).update(
            instance, validated_data
        )
        return instance


def create_investor_profit(
    property_info, investment_values, investment_values_dollar_or_percent
):

    existing_obj = BRInvestorProfit.objects.filter(property_info_id=property_info.id)
    _dict = []

    if existing_obj:
        existing_obj.delete()

    for data in investment_values:
        non_instance, created = BRInvestorProfit.objects.update_or_create(
            investor=data["investor"],
            property_info_id=property_info.id,
            defaults={
                "equity": data["equity"],
                "profit": data["profit"],
                "investment_values_dollar_or_percent": investment_values_dollar_or_percent,
            },
        )
        if created:
            non_instance.save()

        _dict.append(non_instance)
    return _dict


class BRInvestorProfitSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRInvestorProfit
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"raa_property_info": raa_property_info})
        try:
            investment_values = request.get("investment_values", None)
        except KeyError:
            return []
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        instance = create_investor_profit(
            raa_property_info,
            investment_values,
            investment_values_dollar_or_percent,
        )

        return instance[0] if instance else instance

    def update(self, instance, validated_data, *args, **kwargs):
        request = self.context["request"]["investor_profit"]
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        investment_values = request.get("investment_values", None)
        investment_values_dollar_or_percent = request.get("investment_values_dollar_or_percent", None)

        all_objects = create_investor_profit(
            raa_property_info, investment_values, investment_values_dollar_or_percent
        )

        return all_objects[0] if all_objects else all_objects


class BRSummaryTextSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRSummaryText
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        raa_property_info = self.context["raa_property_info"]
        validated_data.update({"property_info": raa_property_info})
        instance = super(BRSummaryTextSerializer, self).create(validated_data)
        return instance

    def update(self, instance, validated_data, *args, **kwargs):
        instance = super(BRSummaryTextSerializer, self).update(instance, validated_data)
        return instance


class BRCustomCompsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BRCustomComps
        fields = "__all__"
        extra_kwargs = {"property_info": {"read_only": True}}

    def create(self, validated_data):
        property_info = self.context["property_info"]
        analyzer = self.context["analyzer"]
        user = self.context["user"]
        model_type = self.context["model_type"]
        property_instance = get_object_or_404(model_type['model'], id=property_info)
        price = validated_data.get("price", None)
        sqft = validated_data.get("sqft", None)
        try:
            per_sqft = round((price / sqft), 2)
        except Exception:
            per_sqft = 0

        validated_data.update(
            {
                "property_info": property_instance,
                "analyzer": analyzer,
                "user": user,
                "per_sqft": per_sqft,
            }
        )

        instance = super(BRCustomCompsSerializer, self).create(validated_data)
        return instance
