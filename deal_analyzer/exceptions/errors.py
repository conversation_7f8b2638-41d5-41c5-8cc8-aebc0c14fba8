from rest_framework.exceptions import APIException


class WrongModelRequest(APIException):
    status_code = 400
    default_detail = {
        "status": "failure",
        "detail": "Please provide a valid model request name",
    }


class ResourceNotFound(APIException):
    status_code = 404
    default_detail = {
        "status": "failure",
        "detail": "the given id did not return any data",
    }


class CompleteInitialRound(APIException):
    status_code = 403

    def __init__(self, detail, status_code=status_code):
        self.status_code = status_code
        self.detail = {
            "status": "failure",
            "detail": "kindly complete the already started round",
            "uncompleted_round_id": detail,
        }


class NoStartedDealRound(APIException):
    status_code = 404
    default_detail = {
        "status": "failure",
        "detail": "You have not started / no pending analysis",
    }


class RequestIdNotPaased(APIException):
    status_code = 404
    default_detail = {
        "status": "failure",
        "detail": "Kindly pass the 'id' in the params/body",
    }


class NoCalculationsForGivenId(APIException):
    status_code = 404
    default_detail = {
        "status": "failure",
        "detail": "You have no calculations for given id/ no analysis",
    }


class InvestorBadRequest(APIException):
    default_code = 400
    default_detail = {
        "status": "failure",
        "detail": "at least pass in empty 'investors' in request body",
    }


class UserHasNoStripeCustomerID(APIException):
    default_code = 404
    default_detail = {"status": "failure", "detail": "User has no stripe customer_id"}


class DealAnalyzerSubscriptionExists(APIException):
    default_code = 403
    default_detail = {
        "status": "failure",
        "detail": "You have already subscribed to a deal_analyzer ",
    }



class BPORportTierSubscriptionExists(APIException):
    default_code = 403
    default_detail = {
        "status": "failure",
        "detail": "You have already subscribed to a bpo report tier ",
    }



class CompsDoesNotExists(APIException):
    default_code = 403
    default_detail = {
        "status": "failure",
        "detail": "No comparables available for the given address. Please provide a correct property address. ",
    }


class ReportResourceNotFound(APIException):
    status_code = 400
    default_detail = {
        "status": "failure",
        "detail": "the given id / report type did not return any data",
    }
    

class ClientEmailDoesNotExist(APIException):
    status_code = 400
    default_detail = {
        "status": "failure",
        "detail": "No user with the given email address exists.",
    }


class InvalidCompsOptionException(APIException):
    status_code = 400
    default_detail = {
        "status": "failure",
        "detail": "Kindly specify the comps option",
    }


class InvalidAnalyzerException(APIException):
    status_code = 400
    default_detail = {
        "status": "failure",
        "detail": "Kindly specify the analyzer type",
    }