from django.contrib import admin
from deal_analyzer.models.abstract_models import Contractor
from import_export.admin import ImportExportModelAdmin
from deal_analyzer.models.subscription import OneTimeSubscription


class ContractorsInfoAdmin(ImportExportModelAdmin, admin.ModelAdmin):
    model = Contractor
    search_fields = ("location",)
    list_display = ("location", "final_remodel", "final_new_construction")
    ordering = ("location",)


admin.site.register(Contractor, ContractorsInfoAdmin)
admin.site.register(OneTimeSubscription)
