from django.urls.conf import path
from deal_analyzer.views import report
from deal_analyzer.views import straight_aesthetics_remodel as sar
from deal_analyzer.views import remodel_add_addition as raa
from deal_analyzer.views import remodel_down_studs as rds
from deal_analyzer.views import remodel_down_to_studs_and_addition as rdsa
from deal_analyzer.views import buy_and_rent as br
from deal_analyzer.views import new_construction as nc
from deal_analyzer.views.expenses import (
    ExpensesAPIView,
    ZillowCompsView,
    ZillowCompsSummaryView,
)
from deal_analyzer.views.global_comps import (
    CityAndCountyTaxView,
    CreateCustomComps,
    GetComps,
    SearchCustomComps,
)
from deal_analyzer.views.questionnaire.buy_and_rent import BuyAndRentView
from deal_analyzer.views.questionnaire.new_construction import NewConstructionView
from deal_analyzer.views.questionnaire.straight_aesthetics import (
    StraightAestheticsQuestionsView,
)
from deal_analyzer.views.questionnaire.remodel_add_addition import (
    RemodelAddAdditionView,
)
from deal_analyzer.views.questionnaire.remodel_down_to_studs import (
    RemodelDownToStudsView,
)
from deal_analyzer.views.questionnaire.remodel_down_to_studs_and_addition import (
    RemodelDownToStudsAndAdditionView,
)
from deal_analyzer.views.subscription import GetUserSubscription, SubscriptionListView

urlpatterns_straight_model = [
    path(
        "create_straight_model",
        sar.CreatePropertyinfo.as_view(),
        name="create_straight_model",
    ),
    path(
        "straight_model",
        sar.StraightRemodelAddSqft.as_view(),
        name="straight_aesthetics_remodel",
    ),
    path(
        "straight_model_calculations",
        sar.StraightRemodelAddSqfSummary.as_view(),
        name="straight_model_calculations",
    ),
    path(
        "straight_model/check_pending_round",
        sar.CheckUserHasPendingRound.as_view(),
        name="check_pending_round",
    ),
]

urlpatterns_globals = [
    path("get_comps", GetComps.as_view(), name="get_comps"),
    path("search_comps", SearchCustomComps.as_view(), name="search_comps"),
    path("city_county_tax", CityAndCountyTaxView.as_view(), name="city_county_tax"),
    path("custom_comps", CreateCustomComps.as_view(), name="custom_comps"),
    path("expenses", ExpensesAPIView.as_view(), name="expenses"),
    path("zillow-comps", ZillowCompsView.as_view(), name="zillow-comps"),
    path("zillow-comps/summary", ZillowCompsSummaryView.as_view(), name="zillow-comps-summary"),

]

urlpatterns_remodel_add_addition = [
    path(
        "remodel_add_addition",
        raa.RemodelAddAddition.as_view(),
        name="remodel_add_addition",
    ),
    path(
        "create_remodel_add_addition",
        raa.CreateRAAPropertyinfo.as_view(),
        name="create_remodel_add_addition",
    ),
    path(
        "remodel_add_addition_calculations",
        raa.RemodelAddSummary.as_view(),
        name="remodel_add_addition_calculations",
    ),
]

urlpatterns_remodel_down_studs = [
    path(
        "remodel_down_studs",
        rds.RemodelDownStuds.as_view(),
        name="remodel_down_studs",
    ),
    path(
        "create_remodel_down_studs",
        rds.CreateRDSPropertyinfo.as_view(),
        name="create_remodel_down_studs",
    ),
    path(
        "remodel_down_studs_calculations",
        rds.RemodelDownStudsSummary.as_view(),
        name="remodel_down_studs_calculations",
    ),
]

urlpatterns_remodel_down_studs_addition = [
    path(
        "remodel_down_studs_addition",
        rdsa.RemodelDownStudsAddition.as_view(),
        name="remodel_down_studs_addition",
    ),
    path(
        "create_remodel_down_studs_addition",
        rdsa.CreateRDSAPropertyinfo.as_view(),
        name="create_remodel_down_studs_addition",
    ),
    path(
        "remodel_down_studs_addition_calculations",
        rdsa.RemodelDownStudsAdditionSummary.as_view(),
        name="remodel_down_studs_addition_calculations",
    ),
]

urlpatterns_buy_and_rent = [
    path(
        "buy_and_rent",
        br.BuyAndRent.as_view(),
        name="buy_and_rent",
    ),
    path(
        "create_buy_and_rent",
        br.CreateBRPropertyinfo.as_view(),
        name="create_buy_and_rent",
    ),
    path(
        "buy_and_rent_calculations",
        br.BuyAndRentSummary.as_view(),
        name="buy_and_rent_calculations",
    ),
]

urlpatterns_new_construction = [
    path(
        "new_construction",
        nc.NewConstruction.as_view(),
        name="new_construction",
    ),
    path(
        "create_new_construction",
        nc.CreateNCPropertyinfo.as_view(),
        name="create_new_construction",
    ),
    path(
        "new_construction_calculations",
        nc.NewConstructionSummary.as_view(),
        name="new_construction_calculations",
    ),
]

urlpatterns_questionnaire = [
    path(
        "straight_aesthetics/questionnaire",
        StraightAestheticsQuestionsView.as_view(),
        name="straight_aesthetics_questions",
    ),
    path(
        "remodel_add_addition/questionnaire",
        RemodelAddAdditionView.as_view(),
        name="remodel_add_addition_questions",
    ),
    path(
        "remodel_down_to_studs_and_addition/questionnaire",
        RemodelDownToStudsAndAdditionView.as_view(),
        name="remodel_down_to_studs_and_addition_questions",
    ),
    path(
        "remodel_down_to_studs/questionnaire",
        RemodelDownToStudsView.as_view(),
        name="remodel_down_to_studs_questions",
    ),
    path(
        "buy_and_rent/questionnaire",
        BuyAndRentView.as_view(),
        name="buy_and_rent_questions",
    ),
    path(
        "new_construction/questionnaire",
        NewConstructionView.as_view(),
        name="new_construction_questions",
    ),
]

urlpatterns_subscription = [
    path("subscription", GetUserSubscription.as_view(), name="user_subscription"),
    path(
        "subscription/user_data",
        SubscriptionListView.as_view(),
        name="usuer_subscription",
    ),
]

urlpatterns_report = [
    path(
        "report",
        report.GetAllAnalysis.as_view(),
        name="report_analysis",
    ),
    path(
        "complete_report",
        report.MarkReportAsCompleted.as_view(),
        name="complete_report",
    ),
    path(
        "send_report",
        report.SendReportClient.as_view(),
        name="send_report",
    ),
    path(
        "delete_report",
        report.DeleteReportView.as_view(),
        name="delete_report",
    )
    
]

urlpatterns = urlpatterns_straight_model
urlpatterns += urlpatterns_remodel_add_addition
urlpatterns += urlpatterns_remodel_down_studs
urlpatterns += urlpatterns_remodel_down_studs_addition
urlpatterns += urlpatterns_buy_and_rent
urlpatterns += urlpatterns_new_construction
urlpatterns += urlpatterns_questionnaire
urlpatterns += urlpatterns_subscription
urlpatterns += urlpatterns_globals
urlpatterns += urlpatterns_report
