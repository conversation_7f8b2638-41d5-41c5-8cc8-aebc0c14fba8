from deal_analyzer.calculations.abstract_calculations.main_calculations import (
    AbstractCalculations,
)
from deal_analyzer.models.straight_aesthetics_remodel import (
    AdditionRemodelItems,
    AestheticItems,
    CarryingCost,
    ClosingCost,
    EstimatedResaleValue,
    FinanceOptions,
    InvestorProfit,
    NonAestheticItems,
    OtherCosts,
    PropertyInfo,
    PropertyManagement,
    PropertyPermitFees,
    RemodelCost,
    RentalIncome,
    SummaryText,
    Taxes,
)


class StraightAetheticsRemodelCalculations(AbstractCalculations):
    def __init__(self, property_id, user, *args, **kwargs):
        self.property_id = property_id
        self.user = user
        self.analyzer = "straight_aesthetics"

    def summary(self):
        data_summary = {
            "user": self.get_user_object(),
            "primary_finance_option_summary": self.primary_finance_options(
                FinanceOptions, RemodelCost, AestheticItems, NonAestheticItems, AdditionRemodelItems, PropertyInfo
            ),
            "secondary_finance_option_summary": self.secondary_finance_options(
                FinanceOptions, RemodelCost, AestheticItems, NonAestheticItems, AdditionRemodelItems, PropertyInfo
            ),
            "monthly_tax_payment": self.taxes(Taxes, FinanceOptions),
            "city_and_county_tax": self.city_and_county_tax(Taxes, FinanceOptions),
            "remodel_cost": self.remodel_cost_summary(
                RemodelCost,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                PropertyInfo
            ),
            "closing_cost": self.closing_cost_summary(
                ClosingCost,
                CarryingCost,
                FinanceOptions,
                PropertyPermitFees,
                Taxes,
                EstimatedResaleValue,
                RemodelCost,
                PropertyInfo,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems
            ),
            "rental_income": self.rental_income_summary(RentalIncome),
            "property_management": self.property_management_summary(
                PropertyManagement,
                PropertyPermitFees,
                FinanceOptions,
                RentalIncome,
                Taxes,
                RemodelCost,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                PropertyInfo
            ),
            "other_costs": self.other_costs_summary(OtherCosts),
            "carrying_months": self.carrying_months_summary(
                CarryingCost,
                FinanceOptions,
                Taxes,
                PropertyManagement,
                PropertyPermitFees,
                RentalIncome,
                OtherCosts,
                RemodelCost,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                PropertyInfo
            ),
            "est_resale_value": self.est_resale_value_summary(
                EstimatedResaleValue,
                FinanceOptions,
                CarryingCost,
                RemodelCost,
                PropertyInfo,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems
            ),
            "capitalization_rate": self.capitalization_rate(
                FinanceOptions,
                PropertyManagement,
                PropertyPermitFees,
                RentalIncome,
                Taxes,
                RemodelCost,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                PropertyInfo
            ),
            "other_expenses": self.other_expenses(
                RentalIncome,
                FinanceOptions,
                RemodelCost,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                PropertyInfo,
                PropertyPermitFees,
                OtherCosts,
                ClosingCost,
                CarryingCost,
                Taxes,
                PropertyManagement,
                EstimatedResaleValue,
            ),
            "net_profit": self.net_profit(
                EstimatedResaleValue,
                FinanceOptions,
                CarryingCost,
                RemodelCost,
                PropertyInfo,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                OtherCosts,
                InvestorProfit,
                RentalIncome,
                ClosingCost,
                PropertyPermitFees,
                Taxes,
                PropertyManagement,
            ),
            "total_liquid_capital_required": self.total_liquid_capital_required_summary(
                RentalIncome,
                ClosingCost,
                CarryingCost,
                FinanceOptions,
                PropertyPermitFees,
                Taxes,
                RemodelCost,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                PropertyInfo,
                PropertyManagement,
                OtherCosts,
                EstimatedResaleValue,
            ),
            "investors_profit": self.investors_profit_summary(
                InvestorProfit,
                RentalIncome,
                ClosingCost,
                CarryingCost,
                FinanceOptions,
                PropertyPermitFees,
                Taxes,
                RemodelCost,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                PropertyInfo,
                PropertyManagement,
                OtherCosts,
                EstimatedResaleValue,
            ),
            "operational_expenses": self.operational_expenses_summary(
                PropertyManagement,
                PropertyPermitFees,
                FinanceOptions,
                RentalIncome,
                Taxes,
                OtherCosts,
                RemodelCost,
                AestheticItems,
                NonAestheticItems,
                AdditionRemodelItems,
                PropertyInfo
            ),
            "summary": self.summary_text(SummaryText),
        }
        return data_summary
