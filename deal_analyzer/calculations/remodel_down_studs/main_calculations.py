from deal_analyzer.calculations.abstract_calculations.main_calculations import (
    AbstractCalculations,
)
from deal_analyzer.models.remodel_down_studs import (
    RDSAdditionRemodelItems,
    RDSAestheticItems,
    RDSCarryingMonths,
    RDSClosingCost,
    RDSEstResaleValue,
    RDSFinanceOptions,
    RDSInvestorProfit,
    RDSNonAestheticItems,
    RDSOtherCosts,
    RDSPropertyInfo,
    RDSPropertyManagement,
    RDSPropertyPermitFees,
    RDSRemodelCost,
    RDSRentalIncome,
    RDSSummaryText,
    RDSTaxes,
)


class RemodelDownStudsCalculations(AbstractCalculations):
    def __init__(self, property_id, user, *args, **kwargs):
        self.property_id = property_id
        self.user = user
        self.analyzer = "remodel_down_studs"

    def summary(self):
        data_summary = {
            "user": self.get_user_object(),
            "primary_finance_option_summary": self.primary_finance_options(
                RDSFinanceOptions, RDSRemodelCost, RDSAestheticItems,
                RDSNonAestheticItems, RDSAdditionRemodelItems, RDSPropertyInfo
            ),
            "secondary_finance_option_summary": self.secondary_finance_options(
                RDSFinanceOptions, RDSRemodelCost, RDSAestheticItems,
                RDSNonAestheticItems, RDSAdditionRemodelItems, RDSPropertyInfo
            ),
            "monthly_tax_payment": self.taxes(RDSTaxes, RDSFinanceOptions),
            "city_and_county_tax": self.city_and_county_tax(
                RDSTaxes, RDSFinanceOptions
            ),
            "remodel_cost": self.remodel_cost_summary(
                RDSRemodelCost,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSPropertyInfo,
            ),
            "closing_cost": self.closing_cost_summary(
                RDSClosingCost,
                RDSCarryingMonths,
                RDSFinanceOptions,
                RDSPropertyPermitFees,
                RDSTaxes,
                RDSEstResaleValue,
                RDSRemodelCost,
                RDSPropertyInfo,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems
            ),
            "rental_income": self.rental_income_summary(RDSRentalIncome),
            "property_management": self.property_management_summary(
                RDSPropertyManagement,
                RDSPropertyPermitFees,
                RDSFinanceOptions,
                RDSRentalIncome,
                RDSTaxes,
                RDSRemodelCost,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSPropertyInfo
            ),
            "other_costs": self.other_costs_summary(RDSOtherCosts),
            "carrying_months": self.carrying_months_summary(
                RDSCarryingMonths,
                RDSFinanceOptions,
                RDSTaxes,
                RDSPropertyManagement,
                RDSPropertyPermitFees,
                RDSRentalIncome,
                RDSOtherCosts,
                RDSRemodelCost,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSPropertyInfo
            ),
            "est_resale_value": self.est_resale_value_summary(
                RDSEstResaleValue,
                RDSFinanceOptions,
                RDSCarryingMonths,
                RDSRemodelCost,
                RDSPropertyInfo,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems
            ),
            "capitalization_rate": self.capitalization_rate(
                RDSFinanceOptions,
                RDSPropertyManagement,
                RDSPropertyPermitFees,
                RDSRentalIncome,
                RDSTaxes,
                RDSRemodelCost,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSPropertyInfo
            ),
            "other_expenses": self.other_expenses(
                RDSRentalIncome,
                RDSFinanceOptions,
                RDSRemodelCost,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSPropertyInfo,
                RDSPropertyPermitFees,
                RDSOtherCosts,
                RDSClosingCost,
                RDSCarryingMonths,
                RDSTaxes,
                RDSPropertyManagement,
                RDSEstResaleValue,
            ),
            "net_profit": self.net_profit(
                RDSEstResaleValue,
                RDSFinanceOptions,
                RDSCarryingMonths,
                RDSRemodelCost,
                RDSPropertyInfo,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSOtherCosts,
                RDSInvestorProfit,
                RDSRentalIncome,
                RDSClosingCost,
                RDSPropertyPermitFees,
                RDSTaxes,
                RDSPropertyManagement,
            ),
            "total_liquid_capital_required": self.total_liquid_capital_required_summary(
                RDSRentalIncome,
                RDSClosingCost,
                RDSCarryingMonths,
                RDSFinanceOptions,
                RDSPropertyPermitFees,
                RDSTaxes,
                RDSRemodelCost,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSPropertyInfo,
                RDSPropertyManagement,
                RDSOtherCosts,
                RDSEstResaleValue,
            ),
            "investors_profit": self.investors_profit_summary(
                RDSInvestorProfit,
                RDSRentalIncome,
                RDSClosingCost,
                RDSCarryingMonths,
                RDSFinanceOptions,
                RDSPropertyPermitFees,
                RDSTaxes,
                RDSRemodelCost,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSPropertyInfo,
                RDSPropertyManagement,
                RDSOtherCosts,
                RDSEstResaleValue,
            ),
            "operational_expenses": self.operational_expenses_summary(
                RDSPropertyManagement,
                RDSPropertyPermitFees,
                RDSFinanceOptions,
                RDSRentalIncome,
                RDSTaxes,
                RDSOtherCosts,
                RDSRemodelCost,
                RDSAestheticItems,
                RDSNonAestheticItems,
                RDSAdditionRemodelItems,
                RDSPropertyInfo
            ),
            "summary": self.summary_text(RDSSummaryText),
        }
        return data_summary
