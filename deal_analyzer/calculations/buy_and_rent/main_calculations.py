from deal_analyzer.calculations.abstract_calculations.main_calculations import (
    AbstractCalculations,
)
from deal_analyzer.models.buy_and_rent import (
    BRAdditionRemodelItems,
    BRAestheticItems,
    BRCarryingMonths,
    BRClosingCost,
    BREstResaleValue,
    BRFinanceOptions,
    BRInvestorProfit,
    BRNonAestheticItems,
    BROtherCosts,
    BRPropertyInfo,
    BRPropertyManagement,
    BRPropertyPermitFees,
    BRRemodelCost,
    BRRentalIncome,
    BRSummaryText,
    BRTaxes,
)


class BuyAndRentCalculations(AbstractCalculations):
    def __init__(self, property_id, user, *args, **kwargs):
        self.property_id = property_id
        self.user = user
        self.analyzer = "buy_and_rent"

    def summary(self):
        data_summary = {
            "user": self.get_user_object(),
            "primary_finance_option_summary": self.primary_finance_options(
                BRFinanceOptions, BRRemodelCost, BRAestheticItems,
                BRNonAestheticItems, BRAdditionRemodelItems, BRPropertyInfo
            ),
            "secondary_finance_option_summary": self.secondary_finance_options(
                BRFinanceOptions, BRRemodelCost, BRAestheticItems,
                BRNonAestheticItems, BRAdditionRemodelItems, BRPropertyInfo
            ),
            "monthly_tax_payment": self.taxes(BRTaxes, BRFinanceOptions),
            "city_and_county_tax": self.city_and_county_tax(
                BRTaxes, BRFinanceOptions
            ),
            "remodel_cost": self.remodel_cost_summary(
                BRRemodelCost,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BRPropertyInfo,
            ),
            "closing_cost": self.closing_cost_summary(
                BRClosingCost,
                BRCarryingMonths,
                BRFinanceOptions,
                BRPropertyPermitFees,
                BRTaxes,
                BREstResaleValue,
                BRRemodelCost,
                BRPropertyInfo,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems
            ),
            "rental_income": self.rental_income_summary(BRRentalIncome),
            "property_management": self.property_management_summary(
                BRPropertyManagement,
                BRPropertyPermitFees,
                BRFinanceOptions,
                BRRentalIncome,
                BRTaxes,
                BRRemodelCost,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BRPropertyInfo
            ),
            "other_costs": self.other_costs_summary(BROtherCosts),
            "carrying_months": self.carrying_months_summary(
                BRCarryingMonths,
                BRFinanceOptions,
                BRTaxes,
                BRPropertyManagement,
                BRPropertyPermitFees,
                BRRentalIncome,
                BROtherCosts,
                BRRemodelCost,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BRPropertyInfo
            ),
            "est_resale_value": self.est_resale_value_summary(
                BREstResaleValue,
                BRFinanceOptions,
                BRCarryingMonths,
                BRRemodelCost,
                BRPropertyInfo,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems
            ),
            "capitalization_rate": self.capitalization_rate(
                BRFinanceOptions,
                BRPropertyManagement,
                BRPropertyPermitFees,
                BRRentalIncome,
                BRTaxes,
                BRRemodelCost,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BRPropertyInfo
            ),
            "other_expenses": self.other_expenses(
                BRRentalIncome,
                BRFinanceOptions,
                BRRemodelCost,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BRPropertyInfo,
                BRPropertyPermitFees,
                BROtherCosts,
                BRClosingCost,
                BRCarryingMonths,
                BRTaxes,
                BRPropertyManagement,
                BREstResaleValue,
            ),
            "net_profit": self.net_profit(
                BREstResaleValue,
                BRFinanceOptions,
                BRCarryingMonths,
                BRRemodelCost,
                BRPropertyInfo,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BROtherCosts,
                BRInvestorProfit,
                BRRentalIncome,
                BRClosingCost,
                BRPropertyPermitFees,
                BRTaxes,
                BRPropertyManagement,
            ),
            "total_liquid_capital_required": self.total_liquid_capital_required_summary(
                BRRentalIncome,
                BRClosingCost,
                BRCarryingMonths,
                BRFinanceOptions,
                BRPropertyPermitFees,
                BRTaxes,
                BRRemodelCost,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BRPropertyInfo,
                BRPropertyManagement,
                BROtherCosts,
                BREstResaleValue,
            ),
            "investors_profit": self.investors_profit_summary(
                BRInvestorProfit,
                BRRentalIncome,
                BRClosingCost,
                BRCarryingMonths,
                BRFinanceOptions,
                BRPropertyPermitFees,
                BRTaxes,
                BRRemodelCost,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BRPropertyInfo,
                BRPropertyManagement,
                BROtherCosts,
                BREstResaleValue,
            ),
            "operational_expenses": self.operational_expenses_summary(
                BRPropertyManagement,
                BRPropertyPermitFees,
                BRFinanceOptions,
                BRRentalIncome,
                BRTaxes,
                BROtherCosts,
                BRRemodelCost,
                BRAestheticItems,
                BRNonAestheticItems,
                BRAdditionRemodelItems,
                BRPropertyInfo
            ),
            "summary": self.summary_text(BRSummaryText),
        }
        return data_summary
