from deal_analyzer.models.new_construction import (
    NCAdditionRemodelItems,
    NCAestheticItems,
    NCCarryingMonths,
    NCClosingCost,
    NCDevelopmentCost,
    NCEstResaleValue,
    NCFinanceOptions,
    NCInvestorProfit,
    NCNonAestheticItems,
    NCOtherCosts,
    NCPropertyInfo,
    NCPropertyManagement,
    NCPropertyPermitFees,
    NCRentalIncome,
    NCSummaryText,
    NCTaxes,
)

from deal_analyzer.calculations.abstract_calculations.main_calculations import (
    AbstractCalculations,
)


class NewConstructionCalculations(AbstractCalculations):
    def __init__(self, property_id, user, *args, **kwargs):
        self.property_id = property_id
        self.user = user
        self.analyzer = "new_construction"

    def summary(self):
        data_summary = {
            "user": self.get_user_object(),
            "primary_finance_option_summary": self.primary_finance_options(
                NCFinanceOptions,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
            ),
            "secondary_finance_option_summary": self.secondary_finance_options(
                NCFinanceOptions,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
            ),
            "monthly_tax_payment": self.taxes(NCTaxes, NCFinanceOptions),
            "city_and_county_tax": self.city_and_county_tax(
                NCTaxes, NCFinanceOptions
            ),
            "remodel_cost": self.remodel_cost_summary(
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
            ),
            "closing_cost": self.closing_cost_summary(
                NCClosingCost,
                NCCarryingMonths,
                NCFinanceOptions,
                NCPropertyPermitFees,
                NCTaxes,
                NCEstResaleValue,
                NCDevelopmentCost,
                NCPropertyInfo,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
            ),
            "rental_income": self.rental_income_summary(NCRentalIncome),
            "property_management": self.property_management_summary(
                NCPropertyManagement,
                NCPropertyPermitFees,
                NCFinanceOptions,
                NCRentalIncome,
                NCTaxes,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
            ),
            "other_costs": self.other_costs_summary(NCOtherCosts),
            "carrying_months": self.carrying_months_summary(
                NCCarryingMonths,
                NCFinanceOptions,
                NCTaxes,
                NCPropertyManagement,
                NCPropertyPermitFees,
                NCRentalIncome,
                NCOtherCosts,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
            ),
            "est_resale_value": self.est_resale_value_summary(
                NCEstResaleValue,
                NCFinanceOptions,
                NCCarryingMonths,
                NCDevelopmentCost,
                NCPropertyInfo,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
            ),
            "capitalization_rate": self.capitalization_rate(
                NCFinanceOptions,
                NCPropertyManagement,
                NCPropertyPermitFees,
                NCRentalIncome,
                NCTaxes,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
            ),
            "other_expenses": self.other_expenses(
                NCRentalIncome,
                NCFinanceOptions,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
                NCPropertyPermitFees,
                NCOtherCosts,
                NCClosingCost,
                NCCarryingMonths,
                NCTaxes,
                NCPropertyManagement,
                NCEstResaleValue,
            ),
            "net_profit": self.net_profit(
                NCEstResaleValue,
                NCFinanceOptions,
                NCCarryingMonths,
                NCDevelopmentCost,
                NCPropertyInfo,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCOtherCosts,
                NCInvestorProfit,
                NCRentalIncome,
                NCClosingCost,
                NCPropertyPermitFees,
                NCTaxes,
                NCPropertyManagement,
            ),
            "total_liquid_capital_required": self.total_liquid_capital_required_summary(
                NCRentalIncome,
                NCClosingCost,
                NCCarryingMonths,
                NCFinanceOptions,
                NCPropertyPermitFees,
                NCTaxes,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
                NCPropertyManagement,
                NCOtherCosts,
                NCEstResaleValue,
            ),
            "investors_profit": self.investors_profit_summary(
                NCInvestorProfit,
                NCRentalIncome,
                NCClosingCost,
                NCCarryingMonths,
                NCFinanceOptions,
                NCPropertyPermitFees,
                NCTaxes,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
                NCPropertyManagement,
                NCOtherCosts,
                NCEstResaleValue,
            ),
            "operational_expenses": self.operational_expenses_summary(
                NCPropertyManagement,
                NCPropertyPermitFees,
                NCFinanceOptions,
                NCRentalIncome,
                NCTaxes,
                NCOtherCosts,
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
            ),
            "development_cost": self.remodel_cost_summary(
                NCDevelopmentCost,
                NCAestheticItems,
                NCNonAestheticItems,
                NCAdditionRemodelItems,
                NCPropertyInfo,
            ),
            "summary": self.summary_text(NCSummaryText),
        }
        return data_summary
