from deal_analyzer.calculations.abstract_calculations.main_calculations import (
    AbstractCalculations,
)
from deal_analyzer.models.remodel_add_addition import (
    RAAAdditionRemodelItems,
    RAAAestheticItems,
    RAACarryingMonths,
    RAAClosingCost,
    RAAEstResaleValue,
    RAAFinanceOptions,
    RAAInvestorProfit,
    RAANonAestheticItems,
    RAAOtherCosts,
    RAAPropertyInfo,
    RAAPropertyManagement,
    RAAPropertyPermitFees,
    RAARemodelCost,
    RAARentalIncome,
    RAASummaryText,
    RAATaxes,
)


class RemodelAddAdditionCalculations(AbstractCalculations):
    def __init__(self, property_id, user, *args, **kwargs):
        self.property_id = property_id
        self.user = user
        self.analyzer = "remodel_add_addition"

    def summary(self):
        data_summary = {
            "user": self.get_user_object(),
            "primary_finance_option_summary": self.primary_finance_options(
                RAAFinanceOptions,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
            ),
            "secondary_finance_option_summary": self.secondary_finance_options(
                RAAFinanceOptions,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
            ),
            "monthly_tax_payment": self.taxes(RAATaxes, RAAFinanceOptions),
            "city_and_county_tax": self.city_and_county_tax(
                RAATaxes, RAAFinanceOptions
            ),
            "remodel_cost": self.remodel_cost_summary(
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
            ),
            "closing_cost": self.closing_cost_summary(
                RAAClosingCost,
                RAACarryingMonths,
                RAAFinanceOptions,
                RAAPropertyPermitFees,
                RAATaxes,
                RAAEstResaleValue,
                RAARemodelCost,
                RAAPropertyInfo,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
            ),
            "rental_income": self.rental_income_summary(RAARentalIncome),
            "property_management": self.property_management_summary(
                RAAPropertyManagement,
                RAAPropertyPermitFees,
                RAAFinanceOptions,
                RAARentalIncome,
                RAATaxes,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
            ),
            "other_costs": self.other_costs_summary(RAAOtherCosts),
            "carrying_months": self.carrying_months_summary(
                RAACarryingMonths,
                RAAFinanceOptions,
                RAATaxes,
                RAAPropertyManagement,
                RAAPropertyPermitFees,
                RAARentalIncome,
                RAAOtherCosts,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
            ),
            "est_resale_value": self.est_resale_value_summary(
                RAAEstResaleValue,
                RAAFinanceOptions,
                RAACarryingMonths,
                RAARemodelCost,
                RAAPropertyInfo,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
            ),
            "capitalization_rate": self.capitalization_rate(
                RAAFinanceOptions,
                RAAPropertyManagement,
                RAAPropertyPermitFees,
                RAARentalIncome,
                RAATaxes,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
            ),
            "other_expenses": self.other_expenses(
                RAARentalIncome,
                RAAFinanceOptions,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
                RAAPropertyPermitFees,
                RAAOtherCosts,
                RAAClosingCost,
                RAACarryingMonths,
                RAATaxes,
                RAAPropertyManagement,
                RAAEstResaleValue,
            ),
            "net_profit": self.net_profit(
                RAAEstResaleValue,
                RAAFinanceOptions,
                RAACarryingMonths,
                RAARemodelCost,
                RAAPropertyInfo,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAOtherCosts,
                RAAInvestorProfit,
                RAARentalIncome,
                RAAClosingCost,
                RAAPropertyPermitFees,
                RAATaxes,
                RAAPropertyManagement,
            ),
            "total_liquid_capital_required": self.total_liquid_capital_required_summary(
                RAARentalIncome,
                RAAClosingCost,
                RAACarryingMonths,
                RAAFinanceOptions,
                RAAPropertyPermitFees,
                RAATaxes,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
                RAAPropertyManagement,
                RAAOtherCosts,
                RAAEstResaleValue,
            ),
            "investors_profit": self.investors_profit_summary(
                RAAInvestorProfit,
                RAARentalIncome,
                RAAClosingCost,
                RAACarryingMonths,
                RAAFinanceOptions,
                RAAPropertyPermitFees,
                RAATaxes,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
                RAAPropertyManagement,
                RAAOtherCosts,
                RAAEstResaleValue,
            ),
            "operational_expenses": self.operational_expenses_summary(
                RAAPropertyManagement,
                RAAPropertyPermitFees,
                RAAFinanceOptions,
                RAARentalIncome,
                RAATaxes,
                RAAOtherCosts,
                RAARemodelCost,
                RAAAestheticItems,
                RAANonAestheticItems,
                RAAAdditionRemodelItems,
                RAAPropertyInfo,
            ),
            "summary": self.summary_text(RAASummaryText),
        }
        return data_summary
