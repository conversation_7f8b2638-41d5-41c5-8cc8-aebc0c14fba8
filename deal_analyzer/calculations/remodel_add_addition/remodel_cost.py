import logging
logger = logging.getLogger("aws_logger")


class RAARemodelCostCalculation:
    def __init__(
        self,
        property_id,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        propertyinfo_model,
    ):
        self.property_id = property_id
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.propertyinfo_model = propertyinfo_model
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.remodel_cost_model.objects.get(property_info=self.property_id)
            return obj
        except self.remodel_cost_model.DoesNotExist:
            return None

    def estimated_total_remodel_cost(self):

        if self.get_object() is None:
            return None

        obj = self.get_object()

        try:
            property_info = self.propertyinfo_model.objects.get(id=self.property_id)
            pre_existing_livable_square_footage = (
                property_info.pre_existing_livable_sqft
            )
        except Exception:
            return None

        est_remodel_cost_psqft = obj.est_remodel_cost_psqft
        estimated_total_remodel_cost = float(
            (pre_existing_livable_square_footage or 0)
        ) * float((est_remodel_cost_psqft or 0))
        estimated_total_remodel_cost += (
            self.aesthetic_items() + self.non_aesthetic_items()
        )
        return estimated_total_remodel_cost

    def aesthetic_items(self):
        """
        checks for aesthetic_items and computes the total amount
        """
        aesthetic_items_total = 0
        try:

            aesthetic_items = self.aesthetic_items_model.objects.filter(
                property_info=self.property_id
            )
            if aesthetic_items:
                for i in range(len(aesthetic_items)):
                    aesthetic_items_total += aesthetic_items[i].value
            return aesthetic_items_total
        except Exception as e:
            logger.error("Aesthetics exception %s", e)
            return aesthetic_items_total

    def non_aesthetic_items(self):
        """
        checks for non_aesthetic_items and computes the total amount
        """
        non_aesthetic_items_total = 0
        try:

            non_aesthetic_items = self.non_aesthetic_items_model.objects.filter(
                property_info=self.property_id
            )
            if non_aesthetic_items:
                for i in range(len(non_aesthetic_items)):
                    non_aesthetic_items_total += non_aesthetic_items[i].value
            return non_aesthetic_items_total

        except Exception as e:
            logger.error("Non aesthetics exception %s", e)
            return non_aesthetic_items_total

    def remodel_expense(self):

        remodel_expense = self.aesthetic_items() + self.non_aesthetic_items()
        return remodel_expense

    def remodel_summary(self):
        data = {
            "estimated_total_remodel_cost": self.estimated_total_remodel_cost(),
            "remodel_expense": self.remodel_expense(),
        }
        return data
