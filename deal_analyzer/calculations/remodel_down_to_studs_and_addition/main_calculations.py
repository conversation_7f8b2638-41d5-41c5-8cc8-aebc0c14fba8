from deal_analyzer.calculations.abstract_calculations.main_calculations import (
    AbstractCalculations,
)
from deal_analyzer.models.remodel_down_to_studs_and_addition import (
    RDSAAdditionRemodelItems,
    RDSAAestheticItems,
    RDSACarryingMonths,
    RDSAClosingCost,
    RDSAEstResaleValue,
    RDSAFinanceOptions,
    RDSAInvestorProfit,
    RDSANonAestheticItems,
    RDSAOtherCosts,
    RDSAPropertyInfo,
    RDSAPropertyManagement,
    RDSAPropertyPermitFees,
    RDSARemodelCost,
    RDSARentalIncome,
    RDSASummaryText,
    RDSATaxes,
)


class RemodelDownStudsAdditionCalculations(AbstractCalculations):
    def __init__(self, property_id, user, *args, **kwargs):
        self.property_id = property_id
        self.user = user
        self.analyzer = "remodel_down_studs_addition"

    def summary(self):
        data_summary = {
            "user": self.get_user_object(),
            "primary_finance_option_summary": self.primary_finance_options(
                RDSAFinanceOptions,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
            ),
            "secondary_finance_option_summary": self.secondary_finance_options(
                RDSAFinanceOptions,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
            ),
            "monthly_tax_payment": self.taxes(RDSATaxes, RDSAFinanceOptions),
            "city_and_county_tax": self.city_and_county_tax(
                RDSATaxes, RDSAFinanceOptions
            ),
            "remodel_cost": self.remodel_cost_summary(
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
            ),
            "closing_cost": self.closing_cost_summary(
                RDSAClosingCost,
                RDSACarryingMonths,
                RDSAFinanceOptions,
                RDSAPropertyPermitFees,
                RDSATaxes,
                RDSAEstResaleValue,
                RDSARemodelCost,
                RDSAPropertyInfo,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
            ),
            "rental_income": self.rental_income_summary(RDSARentalIncome),
            "property_management": self.property_management_summary(
                RDSAPropertyManagement,
                RDSAPropertyPermitFees,
                RDSAFinanceOptions,
                RDSARentalIncome,
                RDSATaxes,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
            ),
            "other_costs": self.other_costs_summary(RDSAOtherCosts),
            "carrying_months": self.carrying_months_summary(
                RDSACarryingMonths,
                RDSAFinanceOptions,
                RDSATaxes,
                RDSAPropertyManagement,
                RDSAPropertyPermitFees,
                RDSARentalIncome,
                RDSAOtherCosts,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
            ),
            "est_resale_value": self.est_resale_value_summary(
                RDSAEstResaleValue,
                RDSAFinanceOptions,
                RDSACarryingMonths,
                RDSARemodelCost,
                RDSAPropertyInfo,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
            ),
            "capitalization_rate": self.capitalization_rate(
                RDSAFinanceOptions,
                RDSAPropertyManagement,
                RDSAPropertyPermitFees,
                RDSARentalIncome,
                RDSATaxes,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
            ),
            "other_expenses": self.other_expenses(
                RDSARentalIncome,
                RDSAFinanceOptions,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
                RDSAPropertyPermitFees,
                RDSAOtherCosts,
                RDSAClosingCost,
                RDSACarryingMonths,
                RDSATaxes,
                RDSAPropertyManagement,
                RDSAEstResaleValue,
            ),
            "net_profit": self.net_profit(
                RDSAEstResaleValue,
                RDSAFinanceOptions,
                RDSACarryingMonths,
                RDSARemodelCost,
                RDSAPropertyInfo,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAOtherCosts,
                RDSAInvestorProfit,
                RDSARentalIncome,
                RDSAClosingCost,
                RDSAPropertyPermitFees,
                RDSATaxes,
                RDSAPropertyManagement,
            ),
            "total_liquid_capital_required": self.total_liquid_capital_required_summary(
                RDSARentalIncome,
                RDSAClosingCost,
                RDSACarryingMonths,
                RDSAFinanceOptions,
                RDSAPropertyPermitFees,
                RDSATaxes,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
                RDSAPropertyManagement,
                RDSAOtherCosts,
                RDSAEstResaleValue,
            ),
            "investors_profit": self.investors_profit_summary(
                RDSAInvestorProfit,
                RDSARentalIncome,
                RDSAClosingCost,
                RDSACarryingMonths,
                RDSAFinanceOptions,
                RDSAPropertyPermitFees,
                RDSATaxes,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
                RDSAPropertyManagement,
                RDSAOtherCosts,
                RDSAEstResaleValue,
            ),
            "operational_expenses": self.operational_expenses_summary(
                RDSAPropertyManagement,
                RDSAPropertyPermitFees,
                RDSAFinanceOptions,
                RDSARentalIncome,
                RDSATaxes,
                RDSAOtherCosts,
                RDSARemodelCost,
                RDSAAestheticItems,
                RDSANonAestheticItems,
                RDSAAdditionRemodelItems,
                RDSAPropertyInfo,
            ),
            "summary": self.summary_text(RDSASummaryText),
        }
        return data_summary
