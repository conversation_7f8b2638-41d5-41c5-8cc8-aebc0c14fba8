import logging

logger = logging.getLogger("aws_logger")


class RemodelCostForAll:
    def __init__(
            self,
            property_id,
            analyzer,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
    ):
        self.property_id = property_id
        self.analyzer = analyzer
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.remodel_cost_model.objects.get(property_info=self.property_id)
            return obj
        except self.remodel_cost_model.DoesNotExist:
            return None

    def pre_existing_livable_sqft(self):
        try:
            property_info = self.propertyinfo_model.objects.get(id=self.property_id)
            pre_existing_livable_sqft = property_info.pre_existing_livable_sqft
            return pre_existing_livable_sqft
        except Exception:
            return None

    def est_remodel_cost_psqft(self):
        if self.remodel_calculation_options() == 0:
            try:
                obj = self.get_object()
                est_remodel_cost_psqft = obj.est_remodel_cost_psqft
                return est_remodel_cost_psqft
            except Exception:
                return None
        else:
            try:
                est_remodel_cost = (
                                           self.aesthetic_items() + self.non_aesthetic_items()
                                   ) / float((self.pre_existing_livable_sqft() or 0))
            except ZeroDivisionError:
                est_remodel_cost = 0
            return round(est_remodel_cost, 2)

    def _estimated_remodel_cost(self):
        if self.remodel_calculation_options() == 0:
            est_remodel_cost = float((self.pre_existing_livable_sqft() or 0)) * float(
                (self.est_remodel_cost_psqft() or 0)
            )
            return round(est_remodel_cost, 2)
        else:
            est_remodel_cost = self.aesthetic_items() + self.non_aesthetic_items()
            return est_remodel_cost

    def aesthetic_items(self):
        """
        checks for aesthetic_items and computes the total amount
        """
        aesthetic_items_total = 0
        try:

            aesthetic_items = self.aesthetic_items_model.objects.filter(
                property_info=self.property_id
            )
            if aesthetic_items:
                for i in range(len(aesthetic_items)):
                    aesthetic_items_total += aesthetic_items[i].value
            return aesthetic_items_total
        except Exception as e:
            logger.error("Aesthetics exception %s", e)
            return aesthetic_items_total

    def non_aesthetic_items(self):
        """
        checks for non_aesthetic_items and computes the total amount
        """
        non_aesthetic_items_total = 0
        try:

            non_aesthetic_items = self.non_aesthetic_items_model.objects.filter(
                property_info=self.property_id
            )
            if non_aesthetic_items:
                for i in range(len(non_aesthetic_items)):
                    non_aesthetic_items_total += non_aesthetic_items[i].value
            return non_aesthetic_items_total

        except Exception as e:
            logger.error("Non aesthetics exception %s", e)
            return non_aesthetic_items_total

    def additional_remodel_items(self):
        """
        checks for additional_remodel_items and computes the total amount
        """
        additional_remodel_items_total = 0
        try:

            additional_remodel_items = (
                self.additional_remodel_items_model.objects.filter(
                    property_info=self.property_id
                )
            )
            if additional_remodel_items:
                for i in range(len(additional_remodel_items)):
                    additional_remodel_items_total += additional_remodel_items[i].value
            return additional_remodel_items_total

        except Exception as e:
            logger.error("Additional Items exception %s", e)
            return additional_remodel_items_total

    def remodel_expense(self):
        """Returns the total advanced remodel cost items"""
        remodel_expense = (
                self.aesthetic_items()
                + self.non_aesthetic_items()
                + self.additional_remodel_items()
        )
        return round(remodel_expense, 2)

    def remodel_calculation_options(self):
        """Returns 0 if user opted for basic remodel calculation otherwise 1"""

        obj = self.get_object()
        remodel_cost_option = 0
        if obj:
            remodel_cost_option = obj.remodel_cost_option
        return int(remodel_cost_option)


class StraightAestheticsRemodelCost(RemodelCostForAll):
    def estimated_total_remodel_cost(self):
        if self.get_object() is None:
            return None

        return (
            self._estimated_remodel_cost()
            if self.remodel_calculation_options() == 0
            else self.remodel_expense()
        )

    def remodel_summary(self):
        data = {
            "est_remodel_cost_psqft": self.est_remodel_cost_psqft(),
            "estimated_total_remodel_cost": self.estimated_total_remodel_cost(),
            "remodel_cost_option": self.remodel_calculation_options(),
            "remodel_expense": self.remodel_expense(),
        }
        return data


class RemodelAddAdditionRemodelCost(RemodelCostForAll):
    def additional_sqft(self):
        try:
            property_info = self.propertyinfo_model.objects.get(id=self.property_id)
            additional_sqft = property_info.additional_sqrft
        except self.propertyinfo_model.DoesNotExist:
            return None

        if self.remodel_calculation_options() == 0:
            return additional_sqft
        else:
            additional_sqft_after = self.additional_remodel_items() / additional_sqft
            return round(additional_sqft_after, 2)

    def est_new_construction_cost_psqft(self):
        if self.remodel_calculation_options() == 0:
            try:
                obj = self.get_object()
                est_new_construction_cost_psqft = obj.est_new_construction_cost_psqft
                return est_new_construction_cost_psqft
            except Exception:
                return None
        else:
            return 0

    def estimated_new_construction_cost(self):
        if self.remodel_calculation_options() == 0:
            obj = self.get_object()
            try:
                est_new_construction_cost_psqft = obj.est_new_construction_cost_psqft
            except Exception:
                est_new_construction_cost_psqft = 0

            est_new_construction_cost = float(
                (self.additional_sqft() or 0)
                * float(est_new_construction_cost_psqft or 0)
            )
            return est_new_construction_cost
        else:
            est_new_construction_cost = self.additional_remodel_items()
            return est_new_construction_cost

    def estimated_total_remodel_cost(self):
        if self.remodel_calculation_options() == 0:
            estimated_total_remodel_cost = (
                    self._estimated_remodel_cost() + self.estimated_new_construction_cost()
            )
        else:
            estimated_total_remodel_cost = self.remodel_expense()
        return estimated_total_remodel_cost

    def remodel_summary(self):
        data = {
            "additional_sqft": self.additional_sqft(),
            "est_remodel_cost_psqft": self.est_remodel_cost_psqft(),
            "estimated_total_remodel_cost": self.estimated_total_remodel_cost(),
            "estimated_remodel_cost": self._estimated_remodel_cost(),
            "estimated_new_construction_cost": self.estimated_new_construction_cost(),
            "est_new_construction_cost_psqft": self.est_new_construction_cost_psqft(),
            "remodel_cost_option": self.remodel_calculation_options(),
            "remodel_expense": self.remodel_expense(),
        }
        return data


class RemodelDownToStudsRemodelCost(RemodelCostForAll):
    def est_new_construction_cost_psqft(self):
        if self.remodel_calculation_options() == 0:
            try:
                obj = self.get_object()
                est_new_construction_cost_psqft = obj.est_new_construction_cost_psqft
                return est_new_construction_cost_psqft
            except Exception:
                return None
        else:
            return 0

    def est_remodel_down_to_studs_psqft(self):
        down_to_studs = round(
            (
                    (self.est_remodel_cost_psqft() or 0)
                    + (self.est_new_construction_cost_psqft() or 0)
            )
            / 2,
            2,
        )
        return down_to_studs

    def estimated_total_remodel_cost(self):
        if self.remodel_calculation_options() == 0:
            total_remodel_cost = self.est_remodel_down_to_studs_psqft() * int(
                self.pre_existing_livable_sqft() or 0
            )
        else:
            total_remodel_cost = self.remodel_expense()
        return total_remodel_cost

    def remodel_summary(self):
        data = {
            "est_remodel_cost_psqft": self.est_remodel_cost_psqft(),
            "est_new_construction_cost_psqft": self.est_new_construction_cost_psqft(),
            "estimated_total_remodel_cost": self.estimated_total_remodel_cost(),
            "est_remodel_down_to_studs_psqft": self.est_remodel_down_to_studs_psqft(),
            "remodel_cost_option": self.remodel_calculation_options(),
            "remodel_expense": self.remodel_expense(),
        }
        return data


class RemodelDownToStudsAndAddAdditionCost(RemodelCostForAll):
    def est_new_construction_cost_psqft(self):
        if self.remodel_calculation_options() == 0:
            try:
                obj = self.get_object()
                est_new_construction_cost_psqft = obj.est_new_construction_cost_psqft
                return est_new_construction_cost_psqft
            except Exception:
                return None
        else:
            additional_sqft_after = self.additional_remodel_items() / (
                    self.additional_sqft() or 1
            )
            return round(additional_sqft_after, 2)

    def additional_sqft(self):
        try:
            property_info = self.propertyinfo_model.objects.get(id=self.property_id)
            additional_sqft = property_info.additional_sqrft
            return additional_sqft
        except self.propertyinfo_model.DoesNotExist:
            return None

    def est_remodel_cost_down_to_studs_psqft(self):
        down_to_studs = round(
            (
                    (self.est_remodel_cost_psqft() or 0)
                    + (self.est_new_construction_cost_psqft() or 0)
            )
            / 2,
            2,
        )
        return down_to_studs

    def est_total_remodel_cost_down_to_studs(self):
        if self.remodel_calculation_options() == 0:
            total_remodel_cost = self.est_remodel_cost_down_to_studs_psqft() * int(
                self.pre_existing_livable_sqft() or 0
            )
            return total_remodel_cost
        else:
            return self.aesthetic_items() + self.non_aesthetic_items()

    def total_additional_sqft_cost(self):
        if self.remodel_calculation_options() == 0:
            additional_sqft_cost = round(
                float(
                    (self.additional_sqft() or 0)
                    * (self.est_new_construction_cost_psqft() or 0)
                ),
                2,
            )
            return additional_sqft_cost
        else:
            return self.additional_remodel_items()

    def remodel_cost_plus_additional_sqft(self):
        if self.remodel_calculation_options() == 0:
            total = (
                    self.est_total_remodel_cost_down_to_studs()
                    + self.total_additional_sqft_cost()
            )
        else:
            total = self.remodel_expense()
        return total

    def estimated_total_remodel_cost(self):
        return self.remodel_cost_plus_additional_sqft()

    def remodel_summary(self):
        data = {
            "est_remodel_cost_psqft": self.est_remodel_cost_psqft(),
            "est_new_construction_cost_psqft": self.est_new_construction_cost_psqft(),
            "est_remodel_cost_down_to_studs_psqft": self.est_remodel_cost_down_to_studs_psqft(),
            "est_total_remodel_cost_down_to_studs": self.est_total_remodel_cost_down_to_studs(),
            "est_total_additional_sqft_cost": self.total_additional_sqft_cost(),
            "est_remodel_cost_down__to_studs_plus_additional_sqft": self.remodel_cost_plus_additional_sqft(),
            "remodel_cost_option": self.remodel_calculation_options(),
            "remodel_expense": self.remodel_expense(),
        }
        return data


class NewConstruction(RemodelCostForAll):
    def new_construction_cost_psqft(self):
        if self.remodel_calculation_options() == 0:
            try:
                obj = self.get_object()
                new_construction_cost_psqft = obj.new_construction_cost_psqft
                return new_construction_cost_psqft
            except Exception:
                new_construction_cost_psqft = 0
                return new_construction_cost_psqft
        else:
            try:
                new_construction_cost_psqft = self.remodel_expense() / int(self.pre_existing_livable_sqft() or 0)
            except ZeroDivisionError:
                new_construction_cost_psqft = 0
            return round(new_construction_cost_psqft, 2)

    def estimated_total_remodel_cost(self):
        if self.remodel_calculation_options() == 0:
            total_remodel_cost = (self.new_construction_cost_psqft() or 0) * int(
                self.pre_existing_livable_sqft() or 0
            )
        else:
            total_remodel_cost = self.remodel_expense()
        return total_remodel_cost

    def total_new_construction_build_cost(self):
        return self.estimated_total_remodel_cost()

    def remodel_summary(self):
        data = {
            "new_construction_cost_psqft": self.new_construction_cost_psqft(),
            "estimated_total_remodel_cost": self.estimated_total_remodel_cost(),
            "remodel_expense": self.remodel_expense(),
            "remodel_cost_option": self.remodel_calculation_options(),
            "total_new_construction_build_cost": self.total_new_construction_build_cost(),
        }
        return data


class BuyAndRent(RemodelCostForAll):
    def additional_sqft(self):
        try:
            property_info = self.propertyinfo_model.objects.get(id=self.property_id)
            additional_sqft = property_info.additional_sqrft
        except self.propertyinfo_model.DoesNotExist:
            return None

        if self.remodel_calculation_options() == 0:
            return additional_sqft
        else:
            additional_sqft_after = self.additional_remodel_items() / additional_sqft
            return round(additional_sqft_after, 2)

    def estimated_new_construction_cost(self):

        if self.remodel_calculation_options() == 0:
            obj = self.get_object()
            try:
                est_new_construction_cost_psqft = obj.est_new_construction_cost_psqft
            except Exception:
                est_new_construction_cost_psqft = 0

            est_new_construction_cost = float(
                (self.additional_sqft() or 0)
                * float(est_new_construction_cost_psqft or 0)
            )
            return est_new_construction_cost
        else:
            est_new_construction_cost = self.additional_remodel_items()
            return est_new_construction_cost

    def estimated_total_remodel_cost(self):
        if self.remodel_calculation_options() == 0:
            estimated_total_remodel_cost = (
                    self._estimated_remodel_cost() + self.estimated_new_construction_cost()
            )
        else:
            estimated_total_remodel_cost = self.remodel_expense()
        return estimated_total_remodel_cost

    def remodel_summary(self):
        data = {
            "estimated_total_remodel_cost": self.estimated_total_remodel_cost(),
            "estimated_remodel_cost": self._estimated_remodel_cost(),
            "estimated_new_construction_cost": self.estimated_new_construction_cost(),
            "remodel_cost_option": self.remodel_calculation_options(),
            "remodel_expense": self.remodel_expense(),
        }
        return data
