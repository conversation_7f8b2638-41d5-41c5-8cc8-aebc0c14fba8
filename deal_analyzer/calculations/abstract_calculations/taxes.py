class AbstractTaxCalculations:
    def __init__(self, property_id, tax_model, finance_options):
        self.property_id = property_id
        self.tax_model = tax_model
        self.finance_options = finance_options
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.tax_model.objects.get(property_info=self.property_id)
            return obj
        except self.tax_model.DoesNotExist:
            return None

    def purchase_price(self):
        primary_purchase_price = 0
        try:
            finance_options = self.finance_options.objects.get(
                property_info=self.property_id
            )
            primary_purchase_price = finance_options.primary_purchase_price or 0
            primary_purchase_price += finance_options.primary_total_remodel_cost or 0
            return primary_purchase_price
        except Exception:
            return primary_purchase_price

    def total_tax(self):
        if self.get_object() is None:
            return None

        city_and_county_tax = self.city_and_county_tax()
        tax = ((self.purchase_price() or 0) / 1000) * (city_and_county_tax or 0)
        return round(float(tax), 2)

    def city_and_county_tax(self):
        if self.get_object() is None:
            return None

        obj = self.get_object()
        city_and_county_tax = obj.city_and_county_tax or 0
        tax_option = obj.city_and_county_tax_fees_dollar_or_percent or None

        if tax_option == "1":
            city_and_county_tax = self.purchase_price() * round(
                (city_and_county_tax / 100), 5
            )

        return round(float(city_and_county_tax), 2)

    def monthly_tax_payment(self):
        return round(float(self.city_and_county_tax() / 12), 2)

    def taxes(self):
        if self.get_object() is None:
            return None

        monthly_tax_payment = self.monthly_tax_payment()

        return monthly_tax_payment
