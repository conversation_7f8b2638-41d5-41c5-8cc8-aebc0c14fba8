from deal_analyzer.calculations.abstract_calculations.closing_cost import (
    AbstractClosingCostCalculation,
)
from deal_analyzer.calculations.abstract_calculations.finance_options import (
    AbstractFinanceCalculations,
)
from deal_analyzer.calculations.abstract_calculations.other_costs import (
    AbstractOtherCostsCalculation,
)
from deal_analyzer.calculations.abstract_calculations.property_management import (
    AbstractPropertyManagementCalculation,
)
from deal_analyzer.calculations.abstract_calculations.remodel_cost import (
    AbstractRemodelCostCalculation,
)
from deal_analyzer.calculations.abstract_calculations.rental_income import (
    AbstractRentalIncomeCalculation,
)
from deal_analyzer.calculations.abstract_calculations.taxes import (
    AbstractTaxCalculations,
)
from deal_analyzer.models.new_construction import NCDemolishingCost

DEFAULT_VALUE = 0


def amortize_calculator(value, rate, pmt_yrs, owning_mths):
    interest_rate = rate
    totalpmts = pmt_yrs
    payment = (interest_rate * value) / (1 - pow(1 + interest_rate, -totalpmts))
    total_interest = 0
    owning_months = owning_mths
    balance_owed = 0
    while value > 0:
        interest = value * interest_rate

        principle = payment - interest
        if value - payment < 0:
            principle = value

        value = value - principle
        if owning_months > 0:
            total_interest += interest
            owning_months = owning_months - 1
            balance_owed = value

    data = {
        "total_interest": round(total_interest, 2),
        "balance_owed": round(balance_owed, 2),
    }
    return data


class AbstractOtherExpensesCalculations:
    def __init__(
            self,
            property_id,
            analyzer,
            rental_income_model,
            finance_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
            property_permit_model,
            other_costs_model,
            closing_cost_model,
            carrying_cost_model,
            tax_model,
            property_mgnt_model,
            est_resale_value_model,
    ):
        self.property_id = property_id
        self.analyzer = analyzer
        self.rental_income_model = rental_income_model
        self.finance_model = finance_model
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model
        self.property_permit_model = property_permit_model
        self.other_costs_model = other_costs_model
        self.closing_cost_model = closing_cost_model
        self.carrying_cost_model = carrying_cost_model
        self.tax_model = tax_model
        self.property_mgnt_model = property_mgnt_model
        self.est_resale_value_model = est_resale_value_model

    def owning_months(self):
        return AbstractRentalIncomeCalculation(
            self.property_id, self.rental_income_model
        ).owning_months()

    def finance_options(self):
        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
        except self.finance_model.DoesNotExist:
            return None
        return finance_options

    def check_cash_payment(self):
        try:
            deal_option = self.finance_options().deal_finance_option

            # if deal_option == "1" or deal_option == "3" or deal_option == "4":
            if deal_option == "3" or deal_option == "4":
                return True
            else:
                return False
        except Exception:
            return False

    def acquisition_cost(self):
        primary_purchase_price = 0
        finance_options = self.finance_options()
        if finance_options:
            primary_purchase_price = finance_options.primary_purchase_price or 0
            primary_purchase_price += finance_options.primary_total_remodel_cost or 0
        return primary_purchase_price

    def total_acquisition_cost(self):
        acquisition__cost = 0
        try:
            if self.check_cash_payment():
                acquisition__cost = self.acquisition_cost() or 0
                return acquisition__cost
            else:
                return acquisition__cost
        except Exception:
            return acquisition__cost

    def total_remodel_cost(self):
        remodel_cost = AbstractRemodelCostCalculation(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        ).estimated_total_remodel_cost()
        return remodel_cost

    def total_architectural_fees(self):
        try:
            property_permit_fees = self.property_permit_model.objects.get(
                property_info=self.property_id
            )
            architectural_fees = property_permit_fees.architectural_fees
        except self.property_permit_model.DoesNotExist:
            architectural_fees = 0
        return architectural_fees

    def total_miscellaneous_cost(self):
        miscellaneous_cost = AbstractOtherCostsCalculation(
            self.property_id, self.other_costs_model
        ).miscellaneous()
        miscellaneous_cost = miscellaneous_cost or 0
        return miscellaneous_cost

    def total_closing_cost(self):
        closing_cost_obj = AbstractClosingCostCalculation(
            self.property_id,
            self.analyzer,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.finance_model,
            self.property_permit_model,
            self.tax_model,
            self.est_resale_value_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        )
        front = closing_cost_obj.est_front_end_closing_cost()
        back = closing_cost_obj.est_back_end_closing_cost()
        total_front_back = round(float(front + back), 2)
        return total_front_back

    def property_management_class(self):
        return AbstractPropertyManagementCalculation(
            self.property_id,
            self.analyzer,
            self.property_mgnt_model,
            self.property_permit_model,
            self.finance_model,
            self.rental_income_model,
            self.tax_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )

    def total_property_management_fees(self):
        property_management_fees = (
            self.property_management_class().property_management_fees()
        )
        property_management_fees = (property_management_fees or 0) * (
                self.owning_months() or 0
        )
        return property_management_fees

    def total_maintenance_fees(self):
        maintenance_fees = self.property_management_class().annual_maintenance_fee()
        return maintenance_fees

    def total_vacancy_factor_loss(self):
        return AbstractRentalIncomeCalculation(
            self.property_id, self.rental_income_model
        ).total_vacancy_factor_loss_factor()

    def total_home_insurance(self):
        home_insurance = self.property_management_class().monthly_home_insurance()
        total_home_insurance = (home_insurance or 0) * (self.owning_months() or 0)
        return total_home_insurance

    def total_hoa_dues(self):
        monthly_hoa_dues = self.property_management_class().monthly_hoa_dues()
        total_hoa_dues = (monthly_hoa_dues or 0) * (self.owning_months() or 0)
        return total_hoa_dues

    def total_insurance_costs(self):
        hoa_dues = self.total_hoa_dues()
        home_insurance = self.total_home_insurance()
        return hoa_dues + home_insurance

    def total_property_permit_impact_and_fees(self):
        try:
            property_permit_fees = self.property_permit_model.objects.get(
                property_info=self.property_id
            )
            property_permit_impact_and_fees = (
                property_permit_fees.city_permit_impact_fees
            )
            if property_permit_fees.city_permit_impact_fees_dollar_or_percent == "1":
                property_permit_impact_and_fees = round(
                    float(
                        ((property_permit_impact_and_fees or 0) / 100)
                        * self.acquisition_cost()
                    ),
                    2,
                )
        except self.property_permit_model.DoesNotExist:
            property_permit_impact_and_fees = 0
        return property_permit_impact_and_fees

    def total_tax_payment(self):
        total_tax = AbstractTaxCalculations(
            self.property_id, self.tax_model, self.finance_model
        ).taxes()
        total_tax = (total_tax or 0) * (self.owning_months() or 0)
        return round(total_tax, 2)

    def _finance_option_calcs(self):
        _finance_options = AbstractFinanceCalculations(
            self.property_id,
            self.finance_model,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        return _finance_options

    def closing_cost_class(self):
        return AbstractClosingCostCalculation(
            self.property_id,
            self.analyzer,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.finance_model,
            self.property_permit_model,
            self.tax_model,
            self.est_resale_value_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        )

    def primary_conventional_mortage_loan_(self):
        return self._finance_option_calcs().conventional_mortage_loan()

    def secondary_conventional_mortage_loan_(self):
        return self._finance_option_calcs().conventional_mortage_loan()

    # deprecated method to conventional_mortage_loan
    def primary_conventional_loan_expenses(self):
        #  if hard money return all loan balance else if conventiona loan return reduced balance
        _finance_options = self._finance_option_calcs()
        owning_months = self.closing_cost_class().owning_months()

        if (
                _finance_options._primary_loan_values()
                and _finance_options.total_monthly_payment()
        ):
            values = _finance_options._primary_loan_values()
            present_value = values["present_value"]
            interest_rate = values["interest_rate"]
            mortage_term = values["mortage_term"]
            amortize = amortize_calculator(
                present_value, interest_rate, mortage_term, owning_months
            )
            total_loan_expenses = amortize["total_interest"] + amortize["balance_owed"]
            return round(total_loan_expenses)
        else:
            DEFAULT_VALUE = 0
            return DEFAULT_VALUE

    def primary_interest_loan_expenses(self):
        _finance_options = self._finance_option_calcs()
        try:
            interest_cost = _finance_options.primary_interest_only_mortgage_loan()[
                "interest_only_payment"
            ]
        except Exception:
            return DEFAULT_VALUE

        interest_cost = (interest_cost or 0) * (self.owning_months() or 0)
        interest_cost += _finance_options.conventional_mortage_loan()
        return round(interest_cost, 2)

    def primary_total_loan_expenses(self):
        try:
            if self.finance_options().primary_sub_loan_type == "0":
                # return self.primary_conventional_loan_expenses()
                return self.conventional_mortage_loan()
            elif self.finance_options().primary_sub_loan_type == "1":
                return self.primary_interest_loan_expenses()
        except AttributeError:
            return None

    def secondary_conventional_loan_expenses(self):
        #  if hard money return all loan balance else if conventiona loan return reduced balance
        _finance_options = self._finance_option_calcs()
        owning_months = self.closing_cost_class().owning_months()

        if (
                _finance_options._secondary_loan_values()
                and _finance_options.secondary_total_monthly_payment()
        ):
            values = _finance_options._secondary_loan_values()
            present_value = values["present_value"]
            interest_rate = values["interest_rate"]
            mortage_term = values["mortage_term"]
            amortize = amortize_calculator(
                present_value, interest_rate, mortage_term, owning_months
            )
            total_loan_expenses = amortize["total_interest"] + amortize["balance_owed"]
            return total_loan_expenses
        else:
            DEFAULT_VALUE = 0
            return DEFAULT_VALUE

    def secondary_interest_loan_expenses(self):
        _finance_options = self._finance_option_calcs()
        interest_cost = _finance_options.secondary_interest_only_mortgage_loan()[
            "interest_only_payment"
        ]
        interest_cost = (interest_cost or 0) * self.owning_months()
        interest_cost += _finance_options.secondary_conventional_mortage_loan()
        return interest_cost

    def secondary_total_loan_expenses(self):
        secondary_total_loan_expenses = 0
        try:
            if self.finance_options().secondary_sub_loan_type == "0":
                secondary_total_loan_expenses = (
                    self.secondary_conventional_loan_expenses()
                )
            elif self.finance_options().secondary_sub_loan_type == "1":
                secondary_total_loan_expenses = self.secondary_interest_loan_expenses()
        except Exception:
            return None
        return round(secondary_total_loan_expenses, 2)

    def total_expenses(self):
        total_list = [
            # self.total_remodel_cost() or 0,
            self.total_tax_payment() or 0,
            # self.total_closing_cost() or 0,
            self.total_miscellaneous_cost() or 0,
            self.total_maintenance_fees() or 0,
            self.total_insurance_costs() or 0,
            self.total_acquisition_cost() or 0,
            # self.primary_total_loan_expenses() or 0,
            # self.secondary_total_loan_expenses() or 0,
            self.primary_loan_after_payment() or 0,
            self.secondary_loan_after_payment() or 0,
            self.total_property_management_fees() or 0,
            self.total_property_permit_impact_and_fees() or 0,
            self.total_architectural_fees() or 0,
            self._demolishing_cost() or 0,
        ]

        total_expenses = sum(total_list)
        return round(float(total_expenses), 2)

    def commission_cost(self):
        cost = self.closing_cost_class().commissions_on_resale()
        return cost

    def total_loan_monthly_payment(self):
        total = (self.primary_total_loan_expenses() or 0) + (
                self.secondary_total_loan_expenses() or 0
        )
        return total

    def _demolishing_cost(self):
        if self.analyzer == "new_construction":
            try:
                obj = NCDemolishingCost.objects.get(property_info=self.property_id)
                if obj.demolishing_cost_option:
                    if obj.demolishing_cost_dollar_or_percent == "1":
                        purchase_price = self.acquisition_cost() or 0
                        demolishing_cost = (obj.demolishing_cost / 100) * purchase_price
                        return round(demolishing_cost, 2)
                    else:
                        return obj.demolishing_cost
                return None
            except NCDemolishingCost.DoesNotExist:
                return None

    def primary_loan_after_payment(self):
        if self.check_cash_payment():
            return 0
        owning_months = self.closing_cost_class().owning_months()
        try:
            sub_loan_type = self.finance_options().primary_sub_loan_type
        except AttributeError:
            return None
        total_payment = 0
        monthly_payment = 0
        try:
            if sub_loan_type == "0":
                monthly_payment = self._finance_option_calcs().total_monthly_payment()
            elif sub_loan_type == "1":
                interest_only_mortgage_loan = (
                    self._finance_option_calcs().primary_interest_only_mortgage_loan()
                )
                monthly_payment = interest_only_mortgage_loan.get(
                    "interest_only_payment", None
                )
        except Exception:
            return None
        total_monthly_payment = monthly_payment * owning_months
        loan_expenses = self.primary_conventional_mortage_loan_() or 0

        # total amount paid as at end of owning month/mortgage term
        if sub_loan_type == "0":
            total_payment = loan_expenses - total_monthly_payment
        elif sub_loan_type == "1":
            total_payment = loan_expenses + total_monthly_payment

        return round(total_payment, 2)

    def secondary_loan_after_payment(self):
        if self.check_cash_payment():
            return 0
        owning_months = self.closing_cost_class().owning_months()
        try:
            sub_loan_type = self.finance_options().primary_sub_loan_type
        except AttributeError:
            return None
        total_payment = 0
        monthly_payment = 0
        try:
            if sub_loan_type == "0":
                monthly_payment = (
                    self._finance_option_calcs().secondary_total_monthly_payment()
                )
            elif sub_loan_type == "1":
                interest_only_mortgage_loan = (
                    self._finance_option_calcs().secondary_interest_only_mortgage_loan()
                )
                monthly_payment = interest_only_mortgage_loan.get(
                    "interest_only_payment", None
                )
        except Exception:
            return None
        total_monthly_payment = monthly_payment or 0 * owning_months or 0
        loan_expenses = self.secondary_conventional_mortage_loan_() or 0

        # total amount paid as at end of owning month/mortgage term
        if sub_loan_type == "0":
            total_payment = loan_expenses - total_monthly_payment
        elif sub_loan_type == "1":
            total_payment = loan_expenses + total_monthly_payment
        return round(total_payment, 2)

    def other_expenses_summary(self):
        data = {
            "total_remodel_cost": self.total_remodel_cost(),
            "total_tax_payment": self.total_tax_payment(),
            "total_closing_cost": self.total_closing_cost(),
            "total_miscellaneous_cost": self.total_miscellaneous_cost(),
            "total_maintenance_fees": self.total_maintenance_fees(),
            "total_vacancy_factor_loss": self.total_vacancy_factor_loss(),
            "total_insurance_costs": self.total_insurance_costs(),
            "total_acquisition_cost": self.total_acquisition_cost(),
            "acquisition_cost": self.acquisition_cost(),
            "primary_total_loan_expenses": self.primary_loan_after_payment(),
            "secondary_total_loan_expenses": self.secondary_total_loan_expenses(),
            "total_loan_monthly_payment": self.total_loan_monthly_payment(),
            "total_property_management_fees": self.total_property_management_fees(),
            "total_property_permit_impact_and_fees": self.total_property_permit_impact_and_fees(),
            "total_architectural_fees": self.total_architectural_fees(),
            "commissions": self.commission_cost(),
            "total_expenses": self.total_expenses(),
            "total_demolishing_cost": self._demolishing_cost(),
            "primary_loan_after_payment": self.primary_loan_after_payment(),
            "secondary_loan_after_payment": self.secondary_loan_after_payment(),
        }
        return data
