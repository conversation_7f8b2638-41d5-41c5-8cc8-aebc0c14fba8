from deal_analyzer.calculations.abstract_calculations.carrying_months import (
    AbstractCarryingMonthsCalculation,
)
from deal_analyzer.calculations.abstract_calculations.closing_cost import (
    AbstractClosingCostCalculation,
)
from deal_analyzer.calculations.abstract_calculations.est_resale_value import (
    AbstractEstResaleValueCalculation,
)
from deal_analyzer.calculations.abstract_calculations.other_expenses import (
    AbstractOtherExpensesCalculations,
)
from deal_analyzer.calculations.abstract_calculations.rental_income import (
    AbstractRentalIncomeCalculation,
)
from deal_analyzer.calculations.abstract_calculations.total_liquid_capital_required import (
    AbstractTotalLiquidCaiptalCalculation,
)
from deal_analyzer.utils.basic_maths import is_what_percent_of


class AbstractInvestorProfitCalculation:
    def __init__(
        self,
        property_id,
        analyzer,
        investor_profit_model,
        rental_income_model,
        closing_cost_model,
        carrying_cost_model,
        finance_model,
        property_permit_model,
        tax_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
        property_mgnt_model,
        other_costs_model,
        est_resale_value_model,
    ):
        self.property_id = property_id
        self.analyzer = analyzer
        self.investor_profit_model = investor_profit_model
        self.rental_income_model = rental_income_model
        self.closing_cost_model = closing_cost_model
        self.carrying_cost_model = carrying_cost_model
        self.finance_model = finance_model
        self.property_permit_model = property_permit_model
        self.tax_model = tax_model
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model
        self.property_mgnt_model = property_mgnt_model
        self.other_costs_model = other_costs_model
        self.est_resale_value_model = est_resale_value_model
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.investor_profit_model.objects.filter(
                property_info=self.property_id
            )
            return obj
        except self.investor_profit_model.DoesNotExist:
            return None

    def deal_cost(self):
        cost = AbstractTotalLiquidCaiptalCalculation(
            self.property_id,
            self.analyzer,
            self.rental_income_model,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.finance_model,
            self.property_permit_model,
            self.tax_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
            self.property_mgnt_model,
            self.other_costs_model,
            self.est_resale_value_model,
        ).total_liquid_capital()
        return cost

    def est_resale_calc(self):
        return AbstractEstResaleValueCalculation(
            self.property_id,
            self.analyzer,
            self.est_resale_value_model,
            self.finance_model,
            self.carrying_cost_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        )

    def estimated_resale_value(self):
        return self.est_resale_calc().est_resale_value()

    def resale_value_option(self):
        return self.est_resale_calc().resale_value_option()

    def desired_resale_value(self):
        return self.est_resale_calc().desired_resale_value()

    def total_operational_expenses(self):
        return AbstractOtherExpensesCalculations(
            self.property_id,
            self.analyzer,
            self.rental_income_model,
            self.finance_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
            self.property_permit_model,
            self.other_costs_model,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.tax_model,
            self.property_mgnt_model,
            self.est_resale_value_model,
        ).total_expenses()

    def rental_income_calculation(self):
        return AbstractRentalIncomeCalculation(
            self.property_id, self.rental_income_model
        )

    def owning_months(self):
        owning_months = self.rental_income_calculation().owning_months()
        return owning_months

    def rental_income(self):
        owning_months = self.owning_months()
        est_rental_income_per_month = (
            self.rental_income_calculation().est_rental_income_per_month()
        )
        rental_income = (owning_months or 0) * (est_rental_income_per_month or 0)
        return rental_income

    def closing_cost_credit(self):
        return AbstractClosingCostCalculation(
            self.property_id,
            self.analyzer,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.finance_model,
            self.property_permit_model,
            self.tax_model,
            self.est_resale_value_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        ).closing_cost_credit()

    def deal_profit(self):
        estimated_resale_value = self.estimated_resale_value() or 0
        if self.resale_value_option() == "2":
            # return self.desired_resale_value()
            estimated_resale_value = self.desired_resale_value()
        if not estimated_resale_value:
            estimated_resale_value = 0
        deal_profit = (
            estimated_resale_value
            + (self.rental_income() or 0)
            + (self.closing_cost_credit() or 0)
            - (self.total_operational_expenses() or 0)
        )
        return round(deal_profit, 2)

    def carrying_months(self):
        return AbstractCarryingMonthsCalculation(
            self.property_id,
            self.analyzer,
            self.carrying_cost_model,
            self.finance_model,
            self.tax_model,
            self.property_mgnt_model,
            self.property_permit_model,
            self.rental_income_model,
            self.other_costs_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        ).carrying_months()

    def carrying_months_in_decimal(self):
        """The decimal equivalent of carrying months"""
        months = self.carrying_months()

        decimal_mon = ((months or 0) * 100 / 12) / 100
        return float(round(decimal_mon, 2))

    def investor_profit(self):
        if self.get_object() is None:
            return []

        investors = self.obj
        if not investors:
            return []

        try:
            investment_values_dollar_or_percent = int(
                investors[0].investment_values_dollar_or_percent
            )
        except Exception:
            investment_values_dollar_or_percent = 1

        deal_cost = self.deal_cost() or 0
        deal_profit = self.deal_profit() or 0

        data = []
        for investor in investors:
            investor_equity = investor.equity or 0
            investor_profit = investor.profit or 0
            if investment_values_dollar_or_percent == 0:
                equity_in_percent = is_what_percent_of(investor_equity, deal_cost)
                equity_in_value = investor_equity
                profit_in_percent = is_what_percent_of(investor_profit, deal_profit)
                profit_in_value = investor_profit
                roi_in_percent = equity_in_percent
                try:
                    # roi_in_percent = round(
                    #     ((profit_in_value - equity_in_value) / equity_in_value) * 100, 2
                    # )
                    roi_in_percent = round((profit_in_value / equity_in_value) * 100, 2)
                except Exception:
                    roi_in_percent = 0

            elif investment_values_dollar_or_percent == 1:
                equity_in_percent = investor_equity
                equity_in_value = round(float(deal_cost * (investor_equity / 100)), 2)
                profit_in_value = (investor_profit / 100) * deal_profit
                profit_in_percent = investor_profit
                try:
                    # roi_in_percent = round(
                    #     ((profit_in_value - equity_in_value) / equity_in_value) * 100, 2
                    # )
                    roi_in_percent = round((profit_in_value / equity_in_value) * 100, 2)
                except Exception:
                    roi_in_percent = 0
            try:
                profit_per_month = round(profit_in_value / self.carrying_months(), 2)
                profit_per_year = round(profit_per_month * 12, 2)
                annualized_roi_percent = round((profit_per_year / equity_in_value) * 100, 2)
                # annualized_roi_percent = float(
                #     (
                #         (profit_in_value / equity_in_value)
                #         ** (1 / self.carrying_months_in_decimal())
                #         - 1
                #     )
                #     * 100
                # )
            except Exception:
                profit_per_month = 0
                profit_per_year = 0
                annualized_roi_percent = 0
            data.append(
                {
                    "investment_values_dollar_or_percent": investment_values_dollar_or_percent,
                    "investor": investor.investor,
                    "carrying_months": self.carrying_months(),
                    "equity_in_value": round(float(equity_in_value), 2),
                    "equity_in_percent": round(float(equity_in_percent), 2),
                    "profit_in_value": round(float(profit_in_value), 2),
                    "profit_in_percent": round(float(profit_in_percent), 2),
                    "roi_in_percent": round(float(roi_in_percent), 2),
                    "roi_in_value": round(float(profit_in_value - equity_in_value), 2),
                    "profit_per_month": profit_per_month,
                    "profit_per_year": profit_per_year,
                    "annualized_roi_percent": round(float(annualized_roi_percent), 2),
                }
            )

        return data

    def investors_profit_summary(self):

        data = {"investors_profit": self.investor_profit()}
        return data
