from deal_analyzer.calculations.abstract_calculations.property_management import (
    AbstractPropertyManagementCalculation,
)


class AbstractCapRateCalculation:
    def __init__(
        self,
        property_id,
        analyzer,
        finance_model,
        property_mgnt_model,
        property_permit_model,
        rental_income_model,
        tax_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        self.property_id = property_id
        self.finance_model = finance_model
        self.analyzer = analyzer
        self.property_mgnt_model = property_mgnt_model
        self.property_permit_model = property_permit_model
        self.rental_income_model = rental_income_model
        self.tax_model = tax_model
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model

    def current_property_value(self):

        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
            purchase_price = finance_options.primary_purchase_price or 0
            purchase_price += finance_options.primary_total_remodel_cost or 0

        except Exception:
            purchase_price = 0

        return purchase_price

    def annual_net_operating_income(self):
        return AbstractPropertyManagementCalculation(
            self.property_id,
            self.analyzer,
            self.property_mgnt_model,
            self.property_permit_model,
            self.finance_model,
            self.rental_income_model,
            self.tax_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
            
        ).annual_net_operating_income()

    def cap_rate(self):

        cap_rate = round(
            (self.annual_net_operating_income() / (self.current_property_value() or 1)),
            9,
        )
        cap_rate_percent = round((cap_rate * 100), 2)
        return cap_rate_percent

    def capitalization_rate(self):
        data = {
            "current_property_value": self.current_property_value(),
            "annual_net_operating_income": self.annual_net_operating_income(),
            "cap_rate": self.cap_rate(),
        }
        return data
