from deal_analyzer.calculations.abstract_calculations.other_costs import (
    AbstractOtherCostsCalculation,
)
from deal_analyzer.calculations.abstract_calculations.other_expenses import (
    AbstractOtherExpensesCalculations,
)
from deal_analyzer.calculations.abstract_calculations.property_management import (
    AbstractPropertyManagementCalculation,
)
from deal_analyzer.calculations.abstract_calculations.rental_income import (
    AbstractRentalIncomeCalculation,
)


class AbstractOperationalExpensesCalculation:
    def __init__(
        self,
        property_id,
        analyzer,
        property_mgnt_model,
        property_permit_model,
        finance_model,
        rental_income_model,
        tax_model,
        other_costs_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        self.property_id = property_id
        self.analyzer = analyzer
        self.property_mgnt_model = property_mgnt_model
        self.property_permit_model = property_permit_model
        self.finance_model = finance_model
        self.rental_income_model = rental_income_model
        self.tax_model = tax_model
        self.other_costs_model = other_costs_model
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model

    def property_management_calculation(self):
        return AbstractPropertyManagementCalculation(
            self.property_id,
            self.analyzer,
            self.property_mgnt_model,
            self.property_permit_model,
            self.finance_model,
            self.rental_income_model,
            self.tax_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )

    def rental_income_calculation(self):
        return AbstractRentalIncomeCalculation(
            self.property_id, self.rental_income_model
        )

    def owning_months(self):
        return self.rental_income_calculation().owning_months()

    def annual_property_management_fees(self):
        annual_property_management_fees = (
            self.property_management_calculation().property_management_fees()
        )
        return annual_property_management_fees

    def annual_maintenance_fee(self):
        annual_maintenance_fee = (
            self.property_management_calculation().annual_maintenance_fee()
        )
        return annual_maintenance_fee

    def annual_property_tax(self):
        annual_property_tax = (
            self.property_management_calculation().annual_property_tax()
        )
        return annual_property_tax

    def annual_insurance_expense(self):
        annual_insurance_expense = (
            self.property_management_calculation().annual_insurance_expense()
        )
        return annual_insurance_expense

    def annual_loan_payment_of_property(self):
        annual_loan_payment_of_property = (
            self.property_management_calculation().annual_loan_payment_of_property()
        )
        return annual_loan_payment_of_property

    def annual_vacancy_rate(self):
        vacancy_rate = AbstractRentalIncomeCalculation(
            self.property_id, self.rental_income_model
        ).vacancy_rates()
        annual_vacancy_rate = (vacancy_rate or 0) * 12
        return round(annual_vacancy_rate, 2)

    def total_hoa_dues(self):
        monthly_hoa_dues = self.property_management_calculation().monthly_hoa_dues()
        total_hoa_dues = (monthly_hoa_dues or 0) * (self.owning_months() or 0)
        return total_hoa_dues

    def total_miscellaneous_cost(self):
        miscellaneous_cost = AbstractOtherCostsCalculation(
            self.property_id, self.other_costs_model
        ).miscellaneous()
        miscellaneous_cost = (miscellaneous_cost or 0)
        return miscellaneous_cost

    def total_architectural_fees(self):
        try:
            property_permit_fees = self.property_permit_model.objects.get(
                property_info=self.property_id
            )
            architectural_fees = property_permit_fees.architectural_fees
        except self.property_permit_model.DoesNotExist:
            architectural_fees = 0
        return architectural_fees

    def total_loans(self):
        return 0

    def other_operational_expenses(self):
        total_operational_expenses = (
            self.total_hoa_dues()
            + self.total_miscellaneous_cost()
            + (self.total_architectural_fees() or 0)
            + self.total_loans()
        )
        return total_operational_expenses

    def operational_expenses_summary(self):
        data = {
            "annual_property_management_fees": self.annual_property_management_fees(),
            "annual_maintenance_fee": self.annual_maintenance_fee(),
            "annual_property_tax": self.annual_property_tax(),
            "annual_insurance_expense": self.annual_insurance_expense(),
            "annual_loan_payment_of_property": self.annual_loan_payment_of_property(),
            "annual_vacancy_rate": self.annual_vacancy_rate(),
            "other_operational_expenses": self.other_operational_expenses(),
        }
        return data
