from deal_analyzer.calculations.abstract_calculations.est_resale_value import (
    AbstractEstResaleValueCalculation,
)
from deal_analyzer.calculations.abstract_calculations.finance_options import (
    AbstractFinanceCalculations,
)
from deal_analyzer.calculations.abstract_calculations.taxes import (
    AbstractTaxCalculations,
)


class AbstractClosingCostCalculation:
    def __init__(
        self,
        property_id,
        analyzer,
        closing_cost_model,
        carrying_cost_model,
        finance_model,
        property_permit_model,
        tax_model,
        est_resale_value_model,
        remodel_cost_model,
        propertyinfo_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
    ):
        self.property_id = property_id
        self.analyzer = analyzer
        self.closing_cost_model = closing_cost_model
        self.carrying_cost_model = carrying_cost_model
        self.finance_model = finance_model
        self.property_permit_model = property_permit_model
        self.tax_model = tax_model
        self.est_resale_value_model = est_resale_value_model
        self.remodel_cost_model = remodel_cost_model
        self.propertyinfo_model = propertyinfo_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model

        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.closing_cost_model.objects.get(property_info=self.property_id)
            return obj
        except self.closing_cost_model.DoesNotExist:
            return None

    def purchase_price(self):
        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
            return finance_options.primary_purchase_price or 0
        except self.finance_model.DoesNotExist:
            return 0

    def taxes(self):
        taxes_summary = AbstractTaxCalculations(
            self.property_id, self.tax_model, self.finance_model
        )
        return taxes_summary.taxes()

    def title_and_escrow_fees(self):

        try:
            ppf = self.property_permit_model.objects.get(property_info=self.property_id)
            title_and_escrow_fees = ppf.title_and_escrow_fees or 0
            title_and_escrow_fees_option = (
                ppf.title_and_escrow_fees_dollar_or_percent or 0
            )
            if title_and_escrow_fees_option == "1":
                title_and_escrow_fees = ((title_and_escrow_fees / 100) * self.purchase_price())
        except Exception:
            title_and_escrow_fees = 0
        return round(title_and_escrow_fees, 2)

    def owning_months(self):
        try:
            carrying_cost = self.carrying_cost_model.objects.get(
                property_info=self.property_id
            )
            owning_months = carrying_cost.carrying_cost_owning_months or 0
            if carrying_cost.carrying_cost_owning_months_option == "1":
                owning_months *= 12
        except Exception:
            owning_months = 0
        return owning_months

    def mortgage_loan_amount(self):
        primary_finance_option_summary = AbstractFinanceCalculations(
            self.property_id,
            self.finance_model,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        return primary_finance_option_summary.mortage_loan_closing_cost()

    def est_front_end_closing_cost(self):
        est_front_end_closing_cost = 0.0

        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
        except Exception:
            return est_front_end_closing_cost

        front_lender_points = finance_options.primary_lender_points_options or 0
        if front_lender_points == "1":
            est_front_end_closing_cost += (
                (finance_options.primary_lender_points or 0) / 100
            ) * (self.mortgage_loan_amount() or 0)

        # add wholesale fee to frontend closing cost calculation
        wholesale_fee_option = finance_options.primary_wholesale_fee_options or 0
        if wholesale_fee_option == "1":
            est_front_end_closing_cost += finance_options.primary_wholesale_fee or 0

        est_front_end_closing_cost += float(
            (self.taxes() or 0) * (self.owning_months() or 0)
        )
        # add closing cost option resell to frontend closing cost
        if self.get_object() is None:
            closing_cost_option = "0"
        else:
            closing_cost_option = self.obj.closing_cost_option_resell or 0

        if closing_cost_option == "0":
            est_front_end_closing_cost += self.title_and_escrow_fees()
        elif closing_cost_option == "2":
            est_front_end_closing_cost += round((self.title_and_escrow_fees() / 2), 2)

        # add closing cost option purchase to frontend closing cost
        if self.get_object() is None:
            closing_cost_option_purchase = "0"
        else:
            closing_cost_option_purchase = self.obj.closing_cost_option_purchase or 0

        if closing_cost_option_purchase == "0":
            est_front_end_closing_cost += self.title_and_escrow_fees()
        elif closing_cost_option_purchase == "2":
            est_front_end_closing_cost += round((self.title_and_escrow_fees() / 2), 2)
        return round(est_front_end_closing_cost, 2)

    def commissions_on_resale(self):
        """allow customers to add commission on resale value"""

        if self.get_object() is None:
            commission = 0
        else:
            commission = self.obj.commission_on_resale or 0

        est_resale_value = AbstractEstResaleValueCalculation(
            self.property_id,
            self.analyzer,
            self.est_resale_value_model,
            self.finance_model,
            self.carrying_cost_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        ).est_resale_value()
        if not est_resale_value:
            return None
        try:
            if self.obj.commission_on_resale_dollar_or_percent == "1":
                commission = est_resale_value * (commission / 100)
            else:
                commission = self.obj.commission_on_resale
        except AttributeError:
            commission = 0

        if not commission:
            commission = 0

        return round(commission, 2)

    def est_back_end_closing_cost(self):

        est_back_end_closing_cost = 0.0
        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
        except Exception:
            return est_back_end_closing_cost

        back_lender_points = finance_options.primary_lender_points_options or 0
        if back_lender_points == "2":
            est_back_end_closing_cost += (
                (finance_options.primary_lender_points or 0) / 100
            ) * (self.mortgage_loan_amount() or 0)

        # closing cost purchase option
        if self.get_object() is None:
            closing_cost_option = "0"
        else:
            closing_cost_option = self.obj.closing_cost_option_purchase or 0

        if closing_cost_option == "1":
            est_back_end_closing_cost += self.title_and_escrow_fees()
        elif closing_cost_option == "2":
            est_back_end_closing_cost += round((self.title_and_escrow_fees() / 2), 2)

        # closing cost resell option
        if self.get_object() is None:
            closing_cost_option_resell = "1"
        else:
            closing_cost_option_resell = self.obj.closing_cost_option_resell or 1

        if closing_cost_option_resell == "1":
            est_back_end_closing_cost += self.title_and_escrow_fees()
        elif closing_cost_option_resell == "2":
            est_back_end_closing_cost += round((self.title_and_escrow_fees() / 2), 2)
        est_back_end_closing_cost += self.commissions_on_resale() or 0
        return round(est_back_end_closing_cost, 2)

    def closing_cost_credit(self):
        if self.get_object() is None:
            closing_cost_credit = 0
            return closing_cost_credit
        return self.obj.closing_cost_credit or 0

    def closing_cost_summary(self):
        """
        1. if buyer pay, to title and escrow fees to frontend closing cost
        2. if seller pays, title and escrow fess added to backend closing cost
        3. if 50/50, share between frontend and backend closing cost.
        """

        data = {
            "est_front_end_closing_cost": self.est_front_end_closing_cost(),
            "est_back_end_closing_cost": self.est_back_end_closing_cost(),
            "commissions_on_resale": self.commissions_on_resale(),
            "title_and_escrow_fees": self.title_and_escrow_fees()
        }
        return data
