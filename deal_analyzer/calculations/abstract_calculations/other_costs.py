class AbstractOtherCostsCalculation:
    def __init__(self, property_id, other_costs_model):
        self.property_id = property_id
        self.other_costs_model = other_costs_model
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.other_costs_model.objects.get(property_info=self.property_id)
            return obj
        except self.other_costs_model.DoesNotExist:
            return None

    def miscellaneous(self):
        if self.get_object() is None:
            landscaping = 0
            return landscaping

        obj = self.get_object()

        miscellaneous = obj.miscellaneous
        return miscellaneous

    def other_costs_summary(self):

        if self.get_object() is None:
            return None

        data = {"miscellaneous": self.miscellaneous()}
        return data
