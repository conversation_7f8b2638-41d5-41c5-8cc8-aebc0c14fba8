from deal_analyzer.calculations.abstract_calculations.finance_options import (
    AbstractFinanceCalculations,
)
from deal_analyzer.calculations.abstract_calculations.other_costs import (
    AbstractOtherCostsCalculation,
)
from deal_analyzer.calculations.abstract_calculations.property_management import (
    AbstractPropertyManagementCalculation,
)
from deal_analyzer.calculations.abstract_calculations.taxes import (
    AbstractTaxCalculations,
)


class AbstractCarryingMonthsCalculation:
    def __init__(
            self,
            property_id,
            analyzer,
            carrying_cost_model,
            finance_model,
            tax_model,
            property_mgnt_model,
            property_permit_model,
            rental_income_model,
            other_costs_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model
    ):
        self.property_id = property_id
        self.carrying_cost_model = carrying_cost_model
        self.finance_model = finance_model
        self.tax_model = tax_model
        self.property_mgnt_model = property_mgnt_model
        self.property_permit_model = property_permit_model
        self.rental_income_model = rental_income_model
        self.other_costs_model = other_costs_model
        self.analyzer = analyzer
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.carrying_cost_model.objects.get(property_info=self.property_id)
            return obj
        except self.carrying_cost_model.DoesNotExist:
            return None

    def taxes_monthly(self):
        if self.get_object() is None:
            return 0
        taxes_monthly = AbstractTaxCalculations(
            self.property_id, self.tax_model, self.finance_model
        ).taxes()
        return taxes_monthly

    def loan_monthly_payment(self):
        finance_option = AbstractFinanceCalculations(
            self.property_id,
            self.finance_model,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        conventional_loan_monthly_payment = finance_option.total_monthly_payment()
        interest_only_loan_monthly_payment = (
            finance_option.primary_interest_only_mortgage_loan()
        )

        if conventional_loan_monthly_payment:
            loan_monthly_payment = conventional_loan_monthly_payment
        elif interest_only_loan_monthly_payment:
            loan_monthly_payment = interest_only_loan_monthly_payment[
                "interest_only_payment"
            ]
        else:
            loan_monthly_payment = 0

        #  add secondary remodel loan
        secondary_conventional_loan_monthly_payment = (
            finance_option.secondary_total_monthly_payment()
        )
        secondary_interest_only_loan_monthly_payment = (
            finance_option.secondary_interest_only_mortgage_loan()
        )

        if secondary_conventional_loan_monthly_payment:
            loan_monthly_payment += secondary_conventional_loan_monthly_payment
        elif secondary_interest_only_loan_monthly_payment:
            loan_monthly_payment += secondary_interest_only_loan_monthly_payment[
                "interest_only_payment"
            ]
        else:
            loan_monthly_payment += 0

        return loan_monthly_payment

    def property_management_object(self):
        return AbstractPropertyManagementCalculation(
            self.property_id,
            self.analyzer,
            self.property_mgnt_model,
            self.property_permit_model,
            self.finance_model,
            self.rental_income_model,
            self.tax_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model

        )

    def property_management_summary(self):
        return self.property_management_object().property_management_fees()

    def other_costs_miscellaneous(self):
        return AbstractOtherCostsCalculation(
            self.property_id, self.other_costs_model
        ).miscellaneous()

    def annual_maintenance_fee(self):
        return self.property_management_object().annual_maintenance_fee()

    def carrying_months(self):
        obj = self.get_object()
        if self.get_object() is None:
            return 0
        carrying_months = obj.carrying_cost_owning_months
        if obj.carrying_cost_owning_months_option == "1":
            carrying_months *= 12
        return carrying_months

    def total_miscellaneous_cost(self):
        miscellaneous = self.other_costs_miscellaneous() or 0
        return miscellaneous

    def total_property_management_fees(self):
        carrying_months = self.carrying_months()
        property_management_fees = self.property_management_summary()
        total_property_fees = ((property_management_fees or 0) * (carrying_months or 0))
        return total_property_fees

    def total_home_insurance(self):
        home_insurance = self.property_management_object().monthly_home_insurance()
        total_home_insurance = (home_insurance or 0) * (self.carrying_months() or 0)
        return total_home_insurance

    def total_hoa_dues(self):
        monthly_hoa_dues = self.property_management_object().monthly_hoa_dues()
        total_hoa_dues = (monthly_hoa_dues or 0) * (self.carrying_months() or 0)
        return total_hoa_dues

    def total_maintenance_fees(self):
        maintenance_fees = self.property_management_object().annual_maintenance_fee() or 0
        return maintenance_fees

    def total_carrying_cost(self):
        carrying_months = self.carrying_months()
        #  we would add all insurance and
        #  add secondary remodel loan
        if not carrying_months:
            return 0

        # all_loan_monthly_payment = self.loan_monthly_payment() or 0

        total_carrying_cost = (

                (self.taxes_monthly() or 0) * (carrying_months or 0)

                + self.total_miscellaneous_cost()

                + self.total_property_management_fees()

                + self.total_home_insurance()

                + self.total_hoa_dues()

                + self.total_maintenance_fees()
        )
        return round(float(total_carrying_cost), 2)

    def other_amounts_and_fees(self):
        other_amounts_and_fees = self.total_miscellaneous_cost() + self.total_property_management_fees()
        return round(other_amounts_and_fees, 2)

    def carrying_months_summary(self):
        if self.get_object() is None:
            return None

        data = {
            "total_carrying_cost": self.total_carrying_cost(),
            "other_amounts_and_fees": self.other_amounts_and_fees(),
            "carrying_months": self.carrying_months(),
        }
        return data
