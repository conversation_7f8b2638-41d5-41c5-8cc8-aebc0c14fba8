from deal_analyzer.calculations.abstract_calculations.remodel_cost import (
    AbstractRemodelCostCalculation,
)
from deal_analyzer.utils.basic_maths import find_percentage


class AbstractEstResaleValueCalculation:
    def __init__(
        self,
        property_id,
        analyzer,
        est_resale_value_model,
        finance_model,
        carrying_cost_model,
        remodel_cost_model,
        propertyinfo_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
    ):
        self.property_id = property_id
        self.analyzer = analyzer
        self.est_resale_value_model = est_resale_value_model
        self.finance_model = finance_model
        self.carrying_cost_model = carrying_cost_model
        self.remodel_cost_model = remodel_cost_model
        self.propertyinfo_model = propertyinfo_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = (additional_remodel_items_model,)

        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.est_resale_value_model.objects.get(
                property_info=self.property_id
            )
            return obj
        except self.est_resale_value_model.DoesNotExist:
            return None

    def resale_value_option(self):
        if self.obj:
            return self.obj.resale_value_option
        else:
            return 1

    def desired_resale_value(self):
        if self.obj:
            return self.obj.desired_resale_value
        else:
            return None

    def comps_option(self):
        if self.get_object() is None:
            return 0
        return self.obj.comps_option

    def average_square_footage(self):
        if self.get_object() is None:
            return 0
        return round(float((self.obj.average_square_footage or 0)), 2)

    def average_price_psqft(self):
        if self.get_object() is None:
            return 0
        return round(float((self.obj.average_price_psqft or 0)), 2)

    def average_lot_size(self):
        if self.get_object() is None:
            return 0
        return round(float((self.obj.average_lot_size or 0)), 2)

    def average_purchase_price(self):
        if self.get_object() is None:
            return 0
        return round(float((self.obj.average_purchase_price or 0)), 2)

    def number_of_comparables(self):
        if self.get_object() is None:
            return 0
        return self.obj.number_of_comps or 0

    def price_divided_by_square_footage(self):
        return round(
            float(self.average_purchase_price() or 0)
            / float(self.average_square_footage() or 1),
            2,
        )

    def interest_rate(self):
        interest_rate = 3.5
        return interest_rate

    def present_value_of_home(self):
        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
            primary_purchase_price = finance_options.primary_purchase_price or 0
            primary_purchase_price += finance_options.primary_total_remodel_cost or 0
            return primary_purchase_price
        except Exception:
            primary_purchase_price = 0
            return primary_purchase_price

    def time_in_years(self):
        try:
            carrying_cost = self.carrying_cost_model.objects.get(
                property_info=self.property_id
            )
        except Exception:
            return None
        time_in_years = carrying_cost.carrying_cost_owning_months
        if carrying_cost.carrying_cost_owning_months_option == "0":
            time_in_years = (time_in_years or 1) / 12

        return time_in_years

    def future_value_of_home(self):
        future_value_of_home = 0

        present_value_of_home = self.present_value_of_home()

        interest_rate = self.interest_rate()

        time_in_years = self.time_in_years()

        future_value_of_home = present_value_of_home * (
            (1 + interest_rate / 100) ** (time_in_years or 0)
        )
        return round(float(future_value_of_home), 2)

    def value_of_appreciation(self):
        value_of_appreciation = (
            self.future_value_of_home() - self.present_value_of_home()
        )
        return value_of_appreciation

    def percent_value_of_asset_appreciation(self):
        try:
            percent_value_of_asset_appreciation = (
                (self.future_value_of_home() - self.present_value_of_home())
                / self.present_value_of_home()
            ) * 100
            return round(float(percent_value_of_asset_appreciation), 2)
        except ZeroDivisionError:
            return None

    def asset_appreciation(self):
        try:
            asset_appreciation = find_percentage(
                self.percent_value_of_asset_appreciation(),
                self.average_purchase_price(),
            )
        except TypeError:
            return None

        return round(asset_appreciation, 2)

    def estimated_total_remodel_cost(self):

        return AbstractRemodelCostCalculation(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        ).estimated_total_remodel_cost()

    def est_resale_value_from_comps(self):
        try:
            estimated_resale_value = float(self.average_price_psqft()) * float(
                self.total_sqft_after_remodel()
            )
            return round(float(estimated_resale_value), 2)
        except ZeroDivisionError:
            return None

    def est_resale_value(self):
        if self.resale_value_option() == "2":
            return self.desired_resale_value()
        elif self.average_purchase_price():
            return self.est_resale_value_from_comps()
        else:
            return self.future_value_of_home()

    def total_sqft_after_remodel(self):
        pre_existing_livable_square_footage = 0
        try:
            property_info = self.propertyinfo_model.objects.get(id=self.property_id)
            pre_existing_livable_square_footage += property_info.additional_sqrft or 0
        except Exception:
            pass

        try:
            property_info = self.propertyinfo_model.objects.get(id=self.property_id)
            pre_existing_livable_square_footage += int(
                property_info.pre_existing_livable_sqft or 0
            )
        except Exception:
            pass
        return pre_existing_livable_square_footage

    def est_resale_value_summary(self):

        data = {
            "total_sqft_after_remodel": self.total_sqft_after_remodel(),
            "future_value_of_home": self.future_value_of_home(),
            "asset_appreciation": self.percent_value_of_asset_appreciation(),
            "value_of_appreciation": self.percent_value_of_asset_appreciation(),
            "estimated_resale_value": self.est_resale_value(),
            "estimated_total_remodel_cost": self.estimated_total_remodel_cost(),
            "average_square_footage": self.average_square_footage(),
            "average_purchase_price": self.average_purchase_price(),
            "average_price_per_sqft": self.average_price_psqft(),
            "average_lot_size": self.average_lot_size(),
            "number_of_comparables": self.number_of_comparables(),
            "comps_option": self.comps_option(),
            "desired_resale_value": self.desired_resale_value(),
            "resale_value_option": self.resale_value_option(),
        }
        return data
