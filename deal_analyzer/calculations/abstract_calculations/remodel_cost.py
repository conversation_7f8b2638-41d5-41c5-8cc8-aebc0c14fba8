import logging

from deal_analyzer.calculations.abstract_calculations.abstract_remodel_calcs.all_remodel_cost_calcs import (
    BuyAndRent,
    NewConstruction,
    RemodelAddAdditionRemodelCost,
    RemodelCostForAll,
    RemodelDownToStudsAndAddAdditionCost,
    RemodelDownToStudsRemodelCost,
    StraightAestheticsRemodelCost,
)

logger = logging.getLogger("aws_logger")


class AbstractRemodelCostCalculation(RemodelCostForAll):
    def get_object(self):
        try:
            obj = self.remodel_cost_model.objects.get(property_info=self.property_id)
            return obj
        except self.remodel_cost_model.DoesNotExist:
            return None

    def check_analyzer_type(self, model):
        default = None
        return getattr(self, str(model), lambda: default)()

    def straight_aesthetics(self):
        data = StraightAestheticsRemodelCost(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        return data

    def remodel_add_addition(self):
        data = RemodelAddAdditionRemodelCost(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        return data

    def remodel_down_studs(self):
        data = RemodelDownToStudsRemodelCost(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        return data

    def remodel_down_studs_addition(self):
        data = RemodelDownToStudsAndAddAdditionCost(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        return data

    def new_construction(self):
        data = NewConstruction(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        return data

    def buy_and_rent(self):
        data = BuyAndRent(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        return data

    def estimated_total_remodel_cost(self):
        analyzer_data = self.check_analyzer_type(self.analyzer)
        return analyzer_data.estimated_total_remodel_cost() if analyzer_data else None

    def remodel_summary(self):
        analyzer_data = self.check_analyzer_type(self.analyzer)
        return analyzer_data.remodel_summary() if analyzer_data else None
