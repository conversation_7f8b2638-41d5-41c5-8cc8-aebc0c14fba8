from deal_analyzer.calculations.abstract_calculations.closing_cost import (
    AbstractClosingCostCalculation,
)
from deal_analyzer.calculations.abstract_calculations.est_resale_value import (
    AbstractEstResaleValueCalculation,
)
from deal_analyzer.calculations.abstract_calculations.investors_profit import (
    AbstractInvestorProfitCalculation,
)
from deal_analyzer.calculations.abstract_calculations.other_expenses import (
    AbstractOtherExpensesCalculations,
)
from deal_analyzer.calculations.abstract_calculations.rental_income import (
    AbstractRentalIncomeCalculation,
)


class AbstractNetProfitCalculation:
    def __init__(
        self,
        property_id,
        analyzer,
        est_resale_value_model,
        finance_model,
        carrying_cost_model,
        remodel_cost_model,
        propertyinfo_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        other_costs_model,
        investor_profit_model,
        rental_income_model,
        closing_cost_model,
        property_permit_model,
        tax_model,
        property_mgnt_model,
    ):
        self.property_id = property_id
        self.analyzer = analyzer
        self.est_resale_value_model = est_resale_value_model
        self.finance_model = finance_model
        self.carrying_cost_model = carrying_cost_model
        self.remodel_cost_model = remodel_cost_model
        self.propertyinfo_model = propertyinfo_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.other_costs_model = other_costs_model
        self.investor_profit_model = investor_profit_model
        self.rental_income_model = rental_income_model
        self.closing_cost_model = closing_cost_model
        self.property_permit_model = property_permit_model
        self.tax_model = tax_model
        self.property_mgnt_model = property_mgnt_model

    def resale_value(self):
        resale_value = AbstractEstResaleValueCalculation(
            self.property_id,
            self.analyzer,
            self.est_resale_value_model,
            self.finance_model,
            self.carrying_cost_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        ).est_resale_value()
        return resale_value

    def total_expenses(self):
        total_expenses = AbstractOtherExpensesCalculations(
            self.property_id,
            self.analyzer,
            self.rental_income_model,
            self.finance_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
            self.property_permit_model,
            self.other_costs_model,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.tax_model,
            self.property_mgnt_model,
            self.est_resale_value_model,
        ).total_expenses()
        return total_expenses

    def est_net_profit(self):
        est_net_profit = AbstractInvestorProfitCalculation(
            self.property_id,
            self.analyzer,
            self.investor_profit_model,
            self.rental_income_model,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.finance_model,
            self.property_permit_model,
            self.tax_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
            self.property_mgnt_model,
            self.other_costs_model,
            self.est_resale_value_model,
        ).deal_profit()
        return est_net_profit

    def rental_income(self):
        return AbstractRentalIncomeCalculation(
            self.property_id, self.rental_income_model
        ).rental_income()

    def closing_cost_credit(self):
        return AbstractClosingCostCalculation(
            self.property_id,
            self.analyzer,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.finance_model,
            self.property_permit_model,
            self.tax_model,
            self.est_resale_value_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        ).closing_cost_credit()

    def net_profit(self):
        net_profit = {
            "resale_value": self.resale_value(),
            "total_expenses": self.total_expenses(),
            "est_net_profit": self.est_net_profit(),
            "total_rental_income": self.rental_income(),
            "closing_cost_credit": self.closing_cost_credit(),
        }
        return net_profit
