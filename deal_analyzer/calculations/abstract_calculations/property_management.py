from deal_analyzer.calculations.abstract_calculations.finance_options import (
    AbstractFinanceCalculations,
)
from deal_analyzer.calculations.abstract_calculations.rental_income import (
    AbstractRentalIncomeCalculation,
)
from deal_analyzer.calculations.abstract_calculations.taxes import (
    AbstractTaxCalculations,
)


class AbstractPropertyManagementCalculation:
    def __init__(
        self,
        property_id,
        analyzer,
        property_mgnt_model,
        property_permit_model,
        finance_model,
        rental_income_model,
        tax_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        self.property_id = property_id
        self.property_mgnt_model = property_mgnt_model
        self.property_permit_model = property_permit_model
        self.finance_model = finance_model
        self.rental_income_model = rental_income_model
        self.tax_model = tax_model
        self.analyzer = analyzer
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.property_mgnt_model.objects.get(property_info=self.property_id)
            return obj
        except self.property_mgnt_model.DoesNotExist:
            return None

    def purchase_price(self):

        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
            purchase_price = finance_options.primary_purchase_price or 0
            purchase_price += finance_options.primary_total_remodel_cost or 0

        except Exception:
            purchase_price = 0

        return purchase_price

    def annual_property_tax(self):
        if self.get_object() is None:
            return None

        annual_property_tax = 0
        taxes_monthly = (
            AbstractTaxCalculations(
                self.property_id, self.tax_model, self.finance_model
            ).taxes()
            or 0
        )
        annual_property_tax = taxes_monthly * 12

        return round(float(annual_property_tax), 2)

    def annual_loan_payment_of_property(self):

        annual_loan_payment = 0
        finance_option = AbstractFinanceCalculations(
            self.property_id,
            self.finance_model,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )
        monthly_payment = finance_option.total_monthly_payment()
        interest_only_monthly_payment = (
            finance_option.primary_interest_only_mortgage_loan()
        )

        if monthly_payment:
            annual_loan_payment = monthly_payment * 12
        elif interest_only_monthly_payment:
            annual_loan_payment = (
                interest_only_monthly_payment["interest_only_payment"] * 12
            )
        else:
            annual_loan_payment = 0
        return round(float(annual_loan_payment), 2)

    def monthly_home_insurance(self):
        try:
            property_permit_fees = self.property_permit_model.objects.get(
                property_info=self.property_id
            )
            monthly_home_insurance = property_permit_fees.monthly_home_insurance
        except self.property_permit_model.DoesNotExist:
            monthly_home_insurance = 0
        return monthly_home_insurance

    def monthly_hoa_dues(self):
        try:
            property_permit_fees = self.property_permit_model.objects.get(
                property_info=self.property_id
            )
            monthly_hoa_dues = property_permit_fees.monthly_hoa_dues
        except self.property_permit_model.DoesNotExist:
            monthly_hoa_dues = 0
        return monthly_hoa_dues

    def annual_insurance_expense(self):

        annual_insurance_expense = float((self.monthly_home_insurance() or 0)) * 12
        return annual_insurance_expense

    def est_rental_income(self):

        try:
            obj = self.rental_income_model.objects.get(property_info=self.property_id)
            return obj.est_rental_income_per_month
        except self.rental_income_model.DoesNotExist:
            return int(0)

    def annual_maintenance_fee(self):
        if self.get_object() is None:
            return 0

        annual_maintenance = self.get_object().annual_maintenance
        annual_maintenance_option = (
            self.get_object().annual_maintenance_dollar_or_percent
        )
        if annual_maintenance_option == "1":
            annual_maintenance = round(
                float(
                    ((annual_maintenance or 0) / 100) * (self.est_rental_income() or 0)
                ),
                2,
            )
        return annual_maintenance

    def property_management_fees(self):
        if self.get_object() is None:
            return None

        property_management_fees = self.get_object().property_management_fees
        property_management_fees_option = (
            self.get_object().property_management_fees_dollar_or_percent
        )
        if property_management_fees_option == "1":
            try:
                property_management_fees = round(
                    float((property_management_fees / 100) * self.est_rental_income()),
                    2,
                )
            except Exception:
                return 0

        return property_management_fees

    def annual_vacancy_factor_loss(self):
        return AbstractRentalIncomeCalculation(
            self.property_id, self.rental_income_model
        ).total_vacancy_factor_loss_factor()

    def annual_net_operating_income(self):

        annual_rental_income = (self.est_rental_income() or 0) * 12
        annuual_property_management_fees = (self.property_management_fees() or 0) * 12
        annual_maintenance_fee = self.annual_maintenance_fee() or 0
        annual_insurance_expense = self.annual_insurance_expense() or 0
        annual_property_tax = self.annual_property_tax() or 0
        annual_net_operating_income = round(
            float(
                (
                    annual_rental_income
                    - (
                        annuual_property_management_fees
                        + annual_maintenance_fee
                        + annual_property_tax
                        + annual_insurance_expense
                    )
                )
            ),
            2,
        )

        return annual_net_operating_income

    def property_management_summary(self):

        data = {
            "annual_net_operating_income": self.annual_net_operating_income(),
            "annual_maintenance_fee": self.annual_maintenance_fee(),
            "annual_property_tax": self.annual_property_tax(),
            "annual_insurance_expense": self.annual_insurance_expense(),
            "annual_loan_payment_of_property": self.annual_loan_payment_of_property(),
        }

        return data
