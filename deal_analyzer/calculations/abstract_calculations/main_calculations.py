from django.contrib.auth import get_user_model
from deal_analyzer.calculations.abstract_calculations.cap_rate import (
    AbstractCapRateCalculation,
)
from deal_analyzer.calculations.abstract_calculations.carrying_months import (
    AbstractCarryingMonthsCalculation,
)
from deal_analyzer.calculations.abstract_calculations.closing_cost import (
    AbstractClosingCostCalculation,
)
from deal_analyzer.calculations.abstract_calculations.est_resale_value import (
    AbstractEstResaleValueCalculation,
)
from deal_analyzer.calculations.abstract_calculations.finance_options import (
    AbstractFinanceCalculations,
)
from deal_analyzer.calculations.abstract_calculations.investors_profit import (
    AbstractInvestorProfitCalculation,
)
from deal_analyzer.calculations.abstract_calculations.net_profit import (
    AbstractNetProfitCalculation,
)
from deal_analyzer.calculations.abstract_calculations.operational_expenses import (
    AbstractOperationalExpensesCalculation,
)
from deal_analyzer.calculations.abstract_calculations.other_costs import (
    AbstractOtherCostsCalculation,
)
from deal_analyzer.calculations.abstract_calculations.other_expenses import (
    AbstractOtherExpensesCalculations,
)
from deal_analyzer.calculations.abstract_calculations.property_management import (
    AbstractPropertyManagementCalculation,
)
from deal_analyzer.calculations.abstract_calculations.remodel_cost import (
    AbstractRemodelCostCalculation,
)
from deal_analyzer.calculations.abstract_calculations.rental_income import (
    AbstractRentalIncomeCalculation,
)
from deal_analyzer.calculations.abstract_calculations.summary_text import AbstractSummaryTextCalculation
from deal_analyzer.calculations.abstract_calculations.taxes import (
    AbstractTaxCalculations,
)
from deal_analyzer.calculations.abstract_calculations.total_liquid_capital_required import (
    AbstractTotalLiquidCaiptalCalculation,
)
from register.models import Agent, License


class AbstractCalculations:
    def __init__(self, property_id, user, analyzer, *args, **kwargs):
        self.property_id = property_id
        self.user = user
        self.analyzer = analyzer

    def get_user_object(self):
        _user_obj = get_user_model().objects.get(id=self.user.id)
        brokerage_name = ""
        _customer_id = None
        profile_image = ""
        if _user_obj.role == "agent" or _user_obj.role == "realty_agent":
            try:
                agent = Agent.objects.get(user=_user_obj)
                _customer_id = agent.customer_ptr_id
                brokerage_name = agent.brokerage_name
                profile_image = agent.profile_image
                profile_image = profile_image.url
            except (Agent.DoesNotExist, ValueError):
                profile_image = ""
                _customer_id = None

        try:
            licence_obj = License.objects.filter(customer_id=_customer_id)

            licence = licence_obj[0].number
        except Exception:
            licence = ""
        data = {
            "id": _user_obj.id,
            "first_name": _user_obj.first_name,
            "last_name": _user_obj.last_name,
            "email": _user_obj.email,
            "role": _user_obj.role,
            "date_joined": _user_obj.date_joined,
            "profile_image": profile_image,
            "company": brokerage_name,
            "license": licence,
        }
        return data

    def primary_finance_options(
        self,
        finance_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        primary_finance_option_summary = AbstractFinanceCalculations(
            self.property_id,
            finance_model,
            self.analyzer,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
        )
        return primary_finance_option_summary.primary_summary()

    def secondary_finance_options(
        self,
        finance_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        secondary_finance_option_summary = AbstractFinanceCalculations(
            self.property_id,
            finance_model,
            self.analyzer,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
        )
        return secondary_finance_option_summary.secondary_summary()

    def taxes(self, tax_model, finance_options):
        taxes_summary = AbstractTaxCalculations(
            self.property_id, tax_model, finance_options
        )
        return taxes_summary.taxes()

    def city_and_county_tax(self, tax_model, finance_options):
        county_tax = AbstractTaxCalculations(
            self.property_id, tax_model, finance_options
        )
        return county_tax.city_and_county_tax()

    def remodel_cost_summary(
        self,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        return AbstractRemodelCostCalculation(
            self.property_id,
            self.analyzer,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
        ).remodel_summary()

    def closing_cost_summary(
        self,
        closing_cost_model,
        carrying_cost_model,
        finance_model,
        property_permit_model,
        tax_model,
        est_resale_value_model,
        remodel_cost_model,
        propertyinfo_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
    ):
        return AbstractClosingCostCalculation(
            self.property_id,
            self.analyzer,
            closing_cost_model,
            carrying_cost_model,
            finance_model,
            property_permit_model,
            tax_model,
            est_resale_value_model,
            remodel_cost_model,
            propertyinfo_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
        ).closing_cost_summary()

    def rental_income_summary(self, rental_income_model):
        return AbstractRentalIncomeCalculation(
            self.property_id, rental_income_model
        ).rental_income_summary()

    def property_management_summary(
        self,
        property_mgnt_model,
        property_permit_model,
        finance_model,
        rental_income_model,
        tax_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        return AbstractPropertyManagementCalculation(
            self.property_id,
            self.analyzer,
            property_mgnt_model,
            property_permit_model,
            finance_model,
            rental_income_model,
            tax_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
        ).property_management_summary()

    def other_costs_summary(self, other_costs_model):
        return AbstractOtherCostsCalculation(
            self.property_id, other_costs_model
        ).other_costs_summary()

    def carrying_months_summary(
        self,
        carrying_cost_model,
        finance_model,
        tax_model,
        property_mgnt_model,
        property_permit_model,
        rental_income_model,
        other_costs_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        return AbstractCarryingMonthsCalculation(
            self.property_id,
            self.analyzer,
            carrying_cost_model,
            finance_model,
            tax_model,
            property_mgnt_model,
            property_permit_model,
            rental_income_model,
            other_costs_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
        ).carrying_months_summary()

    def est_resale_value_summary(
        self,
        est_resale_value_model,
        finance_model,
        carrying_cost_model,
        remodel_cost_model,
        propertyinfo_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
    ):
        return AbstractEstResaleValueCalculation(
            self.property_id,
            self.analyzer,
            est_resale_value_model,
            finance_model,
            carrying_cost_model,
            remodel_cost_model,
            propertyinfo_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
        ).est_resale_value_summary()

    def capitalization_rate(
        self,
        finance_model,
        property_mgnt_model,
        property_permit_model,
        rental_income_model,
        tax_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        return AbstractCapRateCalculation(
            self.property_id,
            self.analyzer,
            finance_model,
            property_mgnt_model,
            property_permit_model,
            rental_income_model,
            tax_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
        ).capitalization_rate()

    def other_expenses(
        self,
        rental_income_model,
        finance_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
        property_permit_model,
        other_costs_model,
        closing_cost_model,
        carrying_cost_model,
        tax_model,
        property_mgnt_model,
        est_resale_value_model,
    ):
        return AbstractOtherExpensesCalculations(
            self.property_id,
            self.analyzer,
            rental_income_model,
            finance_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
            property_permit_model,
            other_costs_model,
            closing_cost_model,
            carrying_cost_model,
            tax_model,
            property_mgnt_model,
            est_resale_value_model,
        ).other_expenses_summary()

    def total_liquid_capital_required_summary(
        self,
        rental_income_model,
        closing_cost_model,
        carrying_cost_model,
        finance_model,
        property_permit_model,
        tax_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
        property_mgnt_model,
        other_costs_model,
        est_resale_value_model,
    ):
        return AbstractTotalLiquidCaiptalCalculation(
            self.property_id,
            self.analyzer,
            rental_income_model,
            closing_cost_model,
            carrying_cost_model,
            finance_model,
            property_permit_model,
            tax_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
            property_mgnt_model,
            other_costs_model,
            est_resale_value_model,
        ).total_liquid_capital_required_summary()

    def investors_profit_summary(
        self,
        investor_profit_model,
        rental_income_model,
        closing_cost_model,
        carrying_cost_model,
        finance_model,
        property_permit_model,
        tax_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
        property_mgnt_model,
        other_costs_model,
        est_resale_value_model,
    ):
        return AbstractInvestorProfitCalculation(
            self.property_id,
            self.analyzer,
            investor_profit_model,
            rental_income_model,
            closing_cost_model,
            carrying_cost_model,
            finance_model,
            property_permit_model,
            tax_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
            property_mgnt_model,
            other_costs_model,
            est_resale_value_model,
        ).investors_profit_summary()

    def operational_expenses_summary(
        self,
        property_mgnt_model,
        property_permit_model,
        finance_model,
        rental_income_model,
        tax_model,
        other_costs_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        return AbstractOperationalExpensesCalculation(
            self.property_id,
            self.analyzer,
            property_mgnt_model,
            property_permit_model,
            finance_model,
            rental_income_model,
            tax_model,
            other_costs_model,
            remodel_cost_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            propertyinfo_model,
        ).operational_expenses_summary()

    def net_profit(
        self,
        est_resale_value_model,
        finance_model,
        carrying_cost_model,
        remodel_cost_model,
        propertyinfo_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        other_costs_model,
        investor_profit_model,
        rental_income_model,
        closing_cost_model,
        property_permit_model,
        tax_model,
        property_mgnt_model,
    ):
        return AbstractNetProfitCalculation(
            self.property_id,
            self.analyzer,
            est_resale_value_model,
            finance_model,
            carrying_cost_model,
            remodel_cost_model,
            propertyinfo_model,
            aesthetic_items_model,
            non_aesthetic_items_model,
            additional_remodel_items_model,
            other_costs_model,
            investor_profit_model,
            rental_income_model,
            closing_cost_model,
            property_permit_model,
            tax_model,
            property_mgnt_model,
        ).net_profit()

    def summary_text(self, summary_model):
        return AbstractSummaryTextCalculation(
            self.property_id, summary_model
        ).summary_text()
