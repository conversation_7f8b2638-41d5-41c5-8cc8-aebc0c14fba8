from deal_analyzer.calculations.abstract_calculations.remodel_cost import (
    AbstractRemodelCostCalculation,
)
# from deal_analyzer.utils.basic_maths import find_percentage


def calculate_mortgage_loan(pv, i, n):

    try:
        numerator = pv * i * ((1 + i) ** n)
    except Exception:
        numerator = 0

    try:
        denominator = ((1 + i) ** n) - 1
    except Exception:
        denominator = 0
    try:
        total_monthly_payment = numerator / denominator
        return total_monthly_payment
    except Exception:
        total_monthly_payment = 0
        return total_monthly_payment


class AbstractFinanceCalculations:
    def __init__(
        self,
        property_id,
        finance_model,
        analyzer,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
    ):
        self.property_id = property_id
        self.finance_model = finance_model
        self.analyzer = analyzer
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model

        self.obj = self.get_object() if self.get_object() else None

    def remodel_cost(self):
        return AbstractRemodelCostCalculation(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model
        )

    def primary_remodel_loan(self):
        try:
            deal_option = self.obj.deal_finance_option
            if deal_option == "3":
                return True
            else:
                return False
        except Exception:
            return False

    def check_cash_payment(self):
        try:
            deal_option = self.obj.deal_finance_option
            if deal_option == "2":
                return True
            else:
                return False
        except Exception:
            return False

    def pay_cash_only(self):
        try:
            deal_option = self.obj.deal_finance_option
            if deal_option == "4":
                return True
            else:
                return False
        except Exception:
            return False

    def get_object(self):
        try:
            obj = self.finance_model.objects.get(property_info=self.property_id)
            return obj
        except self.finance_model.DoesNotExist:
            return None

    def purchase_price(self):
        primary_purchase_price = 0
        primary_purchase_price += self.obj.primary_purchase_price or 0
        primary_purchase_price += self.obj.primary_total_remodel_cost or 0
        if self.check_cash_payment():
            primary_purchase_price += self.remodel_cost().estimated_total_remodel_cost()

        if self.primary_remodel_loan():
            primary_purchase_price = self.remodel_cost().estimated_total_remodel_cost()
        return 0 if primary_purchase_price is None else primary_purchase_price

    def cost_of_ownership(self):
        """
        Conventional Loan [Cost of Ownership]:
            Purchase Price + Lender Points[If Select Loan Amount] + Wholesale Fee[If Select Loan Amount]
        """
        cost_of_ownership = 0

        cost_of_ownership += self.purchase_price()

        if self.obj.primary_wholesale_fee_options == "0":
            cost_of_ownership += self.obj.primary_wholesale_fee or 0

        return cost_of_ownership

    def down_payment(self):
        primary_down_payment = 0

        # primary_purchase_price = self.obj.primary_purchase_price or 0
        # primary_purchase_price += self.obj.primary_total_remodel_cost or 0
        # if self.check_cash_payment():
        #     primary_purchase_price += self.remodel_cost().estimated_total_remodel_cost()

        primary_down_payment = float(self.obj.primary_down_payment or 0)
        if self.obj.primary_dollar_or_percent == "1":
            primary_down_payment = round(
                float((primary_down_payment / 100) * self.purchase_price()), 2
            )
        return primary_down_payment

    def conventional_mortage_loan(self):
        if self.get_object() is None:
            return None
        if self.pay_cash_only():
            return None

        down_payment = self.down_payment()

        """
        get either purchase price or get the total remodel cost for the finance options
        """

        mortage_loan_amount = self.cost_of_ownership() - down_payment

        if self.obj.primary_lender_points_options == "0":
            mortage_loan_amount += (
                (self.obj.primary_lender_points or 0) / 100
            ) * mortage_loan_amount

        if self.obj.primary_include_pmi:
            mortage_loan_amount += 0.01 * mortage_loan_amount

        return round(mortage_loan_amount, 2)

    def mortage_loan_closing_cost(self):
        if self.get_object() is None:
            return None

        down_payment = self.down_payment()

        """
        get either purchase price or get the total remodel cost for the finance options
        """

        # primary_purchase_price = obj.primary_purchase_price or 0
        # primary_purchase_price += obj.primary_total_remodel_cost or 0
        # primary_purchase_price += self.remodel_cost().estimated_total_remodel_cost()
        # if not primary_purchase_price:
        #     return None

        mortage_loan_amount = self.cost_of_ownership() - down_payment
        # twenty_percent_of_purchase_price = round(
        #     float(find_percentage(20, primary_purchase_price)), 5
        # )

        if self.obj.primary_include_pmi:
            mortage_loan_amount += 0.01 * mortage_loan_amount

        return round(mortage_loan_amount, 2)

    def _primary_loan_values(self):
        if not self.obj.primary_term_of_loan:
            return None
        if not self.obj.primary_interest_rate:
            return None
        if self.pay_cash_only():
            return None
        #  mortage_term should be in years
        mortage_term = int(self.obj.primary_term_of_loan or 0)

        #  interest_rate should be between 0 and 1
        interest_rate = float((self.obj.primary_interest_rate or 0) / 100)

        mortage_term *= 12
        if self.conventional_mortage_loan():
            mortage_loan_amount = self.conventional_mortage_loan()
            # Formula to calculate monthly payments

            present_value = mortage_loan_amount
            interest_rate = round((interest_rate / 12), 9)

            return {
                "present_value": present_value,
                "interest_rate": interest_rate,
                "mortage_term": mortage_term,
            }

        return None

    def total_monthly_payment(self):
        if self.get_object() is None:
            return None

        if self._primary_loan_values():
            values = self._primary_loan_values()
            present_value = values["present_value"]
            interest_rate = values["interest_rate"]
            mortage_term = values["mortage_term"]
            total_monthly_payment = calculate_mortgage_loan(
                present_value, interest_rate, mortage_term
            )

            return round(float(total_monthly_payment), 2)
        else:
            return None

    def primary_interest_only_mortgage_loan(self):
        if self.get_object() is None:
            return None
        if self.pay_cash_only():
            return None
        if not self.obj.primary_mortgage_term_of_loan:
            return None
        if not self.obj.primary_annual_interest_rate:
            return None
        if not self.obj.primary_interest_only_term:
            return None

        if self.conventional_mortage_loan():
            mortage_loan_amount = self.conventional_mortage_loan()

            primary_mortgage_term_of_loan = round(
                float(self.obj.primary_mortgage_term_of_loan), 2
            )

            if self.obj.primary_mortgage_term_of_loan_month_or_year == "1":
                primary_mortgage_term_of_loan = primary_mortgage_term_of_loan * 12

            primary_interest_only_term = round(
                float(self.obj.primary_interest_only_term or 1), 2
            )

            if self.obj.primary_interest_only_term_month_or_year == "1":
                primary_interest_only_term = primary_interest_only_term * 12

            # PMT = total payment each period
            # PV = present value of loan (loan amount)
            # i = period interest rate expressed as a decimal
            # n = number of loan payments
            primary_annual_interest_rate = round(
                float(self.obj.primary_annual_interest_rate or 1) / 100, 5
            )
            interest_only_payment = round(
                mortage_loan_amount * (primary_annual_interest_rate / 12), 2
            )

            principal_plus_interest_payment_term = (
                primary_mortgage_term_of_loan - primary_interest_only_term
            )

            principal_plus_interest_payment = round(
                calculate_mortgage_loan(
                    mortage_loan_amount,
                    primary_annual_interest_rate / 12,
                    principal_plus_interest_payment_term,
                ),
                2,
            )

            data = {
                "mortage_loan_amount": self.conventional_mortage_loan(),
                "interest_only_payment": interest_only_payment,
                "interest_only_payment_term": primary_interest_only_term,
                "principal_plus_interest_payment": principal_plus_interest_payment,
                "principal_plus_interest_payment_term": principal_plus_interest_payment_term,
            }
            return data
        else:
            return None

    def primary_summary(self):
        """
        Returns the primary mortage loan amount

        """

        data = {
            "conventional_mortgage_loan": self.conventional_mortage_loan(),
            "total_monthly_payment": self.total_monthly_payment(),
            "interest_only_mortgage_loan": self.primary_interest_only_mortgage_loan(),
        }
        return data

    """
    Secondary remodel loan finance options
    """

    def secondary_total_remodel_cost(self):
        if not self.obj:
            return None

        secondary_total_remodel_cost = self.obj.secondary_total_remodel_cost or None
        if not secondary_total_remodel_cost:
            return self.remodel_cost().estimated_total_remodel_cost()
        return secondary_total_remodel_cost

    def secondary_cost_of_ownership(self):
        """
        Conventional Loan [Cost of Ownership]:
            Purchase Price + Lender Points[If Select Loan Amount] + Wholesale Fee[If Select Loan Amount]
        """
        secondary_cost_of_ownership = 0

        secondary_total_remodel_cost = self.secondary_total_remodel_cost()

        if secondary_total_remodel_cost:
            secondary_cost_of_ownership += secondary_total_remodel_cost

        return secondary_cost_of_ownership

    def secondary_down_payment(self):
        secondary_purchase_price = self.secondary_total_remodel_cost()

        secondary_down_payment = 0

        secondary_down_payment = float(self.obj.secondary_down_payment or 0)
        if self.obj.secondary_dollar_or_percent == "1":
            secondary_down_payment = round(
                float((secondary_down_payment / 100) * secondary_purchase_price), 2
            )

        return secondary_down_payment

    def secondary_conventional_mortage_loan(self):
        if self.get_object() is None:
            return None

        # obj = self.get_object()
        if self.pay_cash_only():
            return None

        secondary_down_payment = self.secondary_down_payment()

        """
        get either purchase price or get the total remodel cost for the finance options
        """

        # secondary_purchase_price = self.secondary_total_remodel_cost()

        mortage_loan_amount = (
            self.secondary_cost_of_ownership() - secondary_down_payment
        )
        # twenty_percent_of_purchase_price = round(
        #     float(find_percentage(20, (secondary_purchase_price or 0))), 2
        # )

        if self.obj.secondary_lender_points_options == "0":
            mortage_loan_amount += (
                (self.obj.secondary_lender_points or 0) / 100
            ) * mortage_loan_amount

        if self.obj.secondary_include_pmi:
            mortage_loan_amount += 0.01 * mortage_loan_amount

        return round(mortage_loan_amount, 2)

    def secondary_total_monthly_payment(self):
        if self.get_object() is None:
            return None

        if not self.obj.secondary_term_of_loan:
            return None
        if not self.obj.secondary_interest_rate:
            return None

        #  mortage_term should be in years
        mortage_term = int(self.obj.secondary_term_of_loan or 0)

        #  interest_rate should be between 0 and 1
        interest_rate = float((self.obj.secondary_interest_rate or 0) / 100)

        mortage_term *= 12

        if self.secondary_conventional_mortage_loan():
            mortage_loan_amount = self.secondary_conventional_mortage_loan()
            # Formula to calculate monthly payments
            pv = mortage_loan_amount
            i = round((interest_rate / 12), 9)
            n = mortage_term

            numerator = pv * i * ((1 + i) ** n)
            denominator = ((1 + i) ** n) - 1
            total_monthly_payment = numerator / denominator

            return round(float(total_monthly_payment), 2)
        else:
            return None

    def _secondary_loan_values(self):
        if not self.obj.secondary_term_of_loan:
            return None
        if not self.obj.secondary_interest_rate:
            return None

        #  mortage_term should be in years
        mortage_term = int(self.obj.secondary_term_of_loan or 0)

        #  interest_rate should be between 0 and 1
        interest_rate = float((self.obj.secondary_interest_rate or 0) / 100)

        mortage_term *= 12
        if self.secondary_conventional_mortage_loan():
            mortage_loan_amount = self.secondary_conventional_mortage_loan()
            # Formula to calculate monthly payments

            present_value = mortage_loan_amount
            interest_rate = round((interest_rate / 12), 9)

            return {
                "present_value": present_value,
                "interest_rate": interest_rate,
                "mortage_term": mortage_term,
            }

        return None

    def secondary_interest_only_mortgage_loan(self):
        if self.get_object() is None:
            return None

        if not self.obj.secondary_mortgage_term_of_loan:
            return None
        if not self.obj.secondary_annual_interest_rate:
            return None
        if not self.obj.secondary_interest_only_term:
            return None

        if self.secondary_conventional_mortage_loan():
            mortage_loan_amount = self.secondary_conventional_mortage_loan()

            secondary_mortgage_term_of_loan = round(
                float(self.obj.secondary_mortgage_term_of_loan or 1), 5
            )
            if self.obj.secondary_mortgage_term_of_loan_month_or_year == "1":
                secondary_mortgage_term_of_loan = secondary_mortgage_term_of_loan * 12

            secondary_interest_only_term = round(
                float(self.obj.secondary_interest_only_term or 1), 5
            )

            if self.obj.secondary_interest_only_term_month_or_year == "1":
                secondary_interest_only_term = secondary_interest_only_term * 12

            # PMT = total payment each period
            # PV = present value of loan (loan amount)
            # i = period interest rate expressed as a decimal
            # n = number of loan payments
            secondary_annual_interest_rate = round(
                float(self.obj.secondary_annual_interest_rate or 1) / 100, 5
            )
            interest_only_payment = round(
                mortage_loan_amount * (secondary_annual_interest_rate / 12), 2
            )

            principal_plus_interest_payment_term = (
                secondary_mortgage_term_of_loan - secondary_interest_only_term
            )

            principal_plus_interest_payment = round(
                calculate_mortgage_loan(
                    mortage_loan_amount,
                    secondary_annual_interest_rate / 12,
                    principal_plus_interest_payment_term,
                ),
                2,
            )

            data = {
                "mortage_loan_amount": self.secondary_conventional_mortage_loan(),
                "interest_only_payment": interest_only_payment,
                "interest_only_payment_term": secondary_interest_only_term,
                "principal_plus_interest_payment": principal_plus_interest_payment,
                "principal_plus_interest_payment_term": principal_plus_interest_payment_term,
            }
            return data

    def secondary_summary(self):
        """
        Returns the primary mortage loan amount
        """

        data = {
            "secondary_total_remodel_cost": self.secondary_total_remodel_cost(),
            "conventional_mortgage_loan": self.secondary_conventional_mortage_loan(),
            "total_monthly_payment": self.secondary_total_monthly_payment(),
            "interest_only_mortgage_loan": self.secondary_interest_only_mortgage_loan(),
            "pay_cash_only": self.pay_cash_only()
        }
        return data
