class AbstractSummaryTextCalculation:
    def __init__(self, property_id, summary_model):
        self.property_id = property_id
        self.summary_model = summary_model
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.summary_model.objects.get(property_info=self.property_id)
            return obj
        except self.summary_model.DoesNotExist:
            return None

    def texts(self):
        if self.get_object() is None:
            return ''

        obj = self.get_object()

        text = obj.text
        return text

    def summary_text(self):

        if self.get_object() is None:
            return None

        data = {"text": self.texts()}
        return data
