from deal_analyzer.calculations.abstract_calculations.carrying_months import (
    AbstractCarryingMonthsCalculation,
)
from deal_analyzer.calculations.abstract_calculations.closing_cost import (
    AbstractClosingCostCalculation,
)
from deal_analyzer.calculations.abstract_calculations.est_resale_value import (
    AbstractEstResaleValueCalculation,
)
from deal_analyzer.calculations.abstract_calculations.finance_options import (
    AbstractFinanceCalculations,
)
from deal_analyzer.calculations.abstract_calculations.other_costs import (
    AbstractOtherCostsCalculation,
)
from deal_analyzer.calculations.abstract_calculations.other_expenses import (
    AbstractOtherExpensesCalculations,
)
from deal_analyzer.calculations.abstract_calculations.property_management import (
    AbstractPropertyManagementCalculation,
)
from deal_analyzer.calculations.abstract_calculations.remodel_cost import (
    AbstractRemodelCostCalculation,
)
from deal_analyzer.calculations.abstract_calculations.rental_income import (
    AbstractRentalIncomeCalculation,
)
from deal_analyzer.calculations.abstract_calculations.taxes import (
    AbstractTaxCalculations,
)

DEFAULT_VALUE = 0


class AbstractTotalLiquidCaiptalCalculation:
    def __init__(
        self,
        property_id,
        analyzer,
        rental_income_model,
        closing_cost_model,
        carrying_cost_model,
        finance_model,
        property_permit_model,
        tax_model,
        remodel_cost_model,
        aesthetic_items_model,
        non_aesthetic_items_model,
        additional_remodel_items_model,
        propertyinfo_model,
        property_mgnt_model,
        other_costs_model,
        est_resale_value_model,
    ):
        self.property_id = property_id
        self.analyzer = analyzer
        self.rental_income_model = rental_income_model
        self.closing_cost_model = closing_cost_model
        self.carrying_cost_model = carrying_cost_model
        self.finance_model = finance_model
        self.property_permit_model = property_permit_model
        self.tax_model = tax_model
        self.remodel_cost_model = remodel_cost_model
        self.aesthetic_items_model = aesthetic_items_model
        self.non_aesthetic_items_model = non_aesthetic_items_model
        self.additional_remodel_items_model = additional_remodel_items_model
        self.propertyinfo_model = propertyinfo_model
        self.property_mgnt_model = property_mgnt_model
        self.other_costs_model = other_costs_model
        self.est_resale_value_model = est_resale_value_model

    def rental_income_calculation(self):
        return AbstractRentalIncomeCalculation(
            self.property_id, self.rental_income_model
        )

    def owning_months(self):
        return self.rental_income_calculation().owning_months()

    def closing_cost_calculation(self):
        return AbstractClosingCostCalculation(
            self.property_id,
            self.analyzer,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.finance_model,
            self.property_permit_model,
            self.tax_model,
            self.est_resale_value_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        )

    def frontend_closing_cost(self):
        frontend_closingcost = (
            self.closing_cost_calculation().est_front_end_closing_cost()
        )
        return frontend_closingcost

    def finance_options(self):
        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
        except self.finance_model.DoesNotExist:
            return None
        return finance_options

    def check_cash_payment(self):
        try:
            deal_option = self.finance_options().deal_finance_option
            if deal_option == "1" or deal_option == "3" or deal_option == "4":
                return True
            else:
                return False
        except Exception:
            return False

    def lender_points(self):
        lender_points = 0
        finance_options = self.finance_options()
        try:
            lender_points = finance_options.primary_lender_points or 0
        except Exception:
            return lender_points

        return int(lender_points)

    def acquisition_cost(self):
        primary_purchase_price = 0
        finance_options = self.finance_options()
        try:
            primary_purchase_price = finance_options.primary_purchase_price or 0
            primary_purchase_price += finance_options.primary_total_remodel_cost or 0
        except Exception:
            return None
        return int(primary_purchase_price)

    def total_acquisition_cost(self):
        if self.check_cash_payment():
            return self.acquisition_cost()
        else:
            return 0

    def finance_option_calculation_class(self):
        return AbstractFinanceCalculations(
            self.property_id,
            self.finance_model,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )

    def primary_home_loan_monthly_payment(self):
        if self.check_cash_payment():
            return 0
        else:
            cost = self.finance_option_calculation_class().total_monthly_payment() or 0
            cost *= 12
            return round(cost, 2)

    def secondary_remodel_loan(self):
        try:
            cost = self.finance_option_calculation_class().secondary_interest_only_mortgage_loan()[
                "interest_only_payment"
            ]
            cost *= 12
            return round(cost, 2)
        except Exception:
            return DEFAULT_VALUE

    def downpayment_cost(self):
        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
            down_payment_option = finance_options.primary_dollar_or_percent
            down_payment = finance_options.primary_down_payment
            if down_payment_option == "1":
                down_payment = (int(down_payment) / 100) * (
                    finance_options.primary_purchase_price or 0
                )
            return int(down_payment)
        except Exception:
            return None

    def secondary_downpayment_cost(self):
        try:
            finance_options = self.finance_model.objects.get(
                property_info=self.property_id
            )
            down_payment_option = finance_options.secondary_dollar_or_percent
            down_payment = finance_options.secondary_down_payment
            if down_payment_option == "1":
                down_payment = (int(down_payment) / 100) * (
                    finance_options.secondary_purchase_price
                    or finance_options.secondary_total_remodel_cost
                    or 0
                )
            return int(down_payment)
        except Exception:
            return None

    def asset_appreciation(self):
        return AbstractEstResaleValueCalculation(
            self.property_id,
            self.analyzer,
            self.est_resale_value_model,
            self.finance_model,
            self.carrying_cost_model,
            self.remodel_cost_model,
            self.propertyinfo_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
        ).percent_value_of_asset_appreciation()

    def remodel_cost(self):

        return AbstractRemodelCostCalculation(
            self.property_id,
            self.analyzer,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        ).estimated_total_remodel_cost()

    def total_remodel_cost(self):
        if self.check_cash_payment():
            return self.remodel_cost()
        return DEFAULT_VALUE

    def carrying_months_calculation(self):
        return AbstractCarryingMonthsCalculation(
            self.property_id,
            self.analyzer,
            self.carrying_cost_model,
            self.finance_model,
            self.tax_model,
            self.property_mgnt_model,
            self.property_permit_model,
            self.rental_income_model,
            self.other_costs_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )

    def total_carrying_cost(self):
        return self.carrying_months_calculation().total_carrying_cost()

    def carrying_months(self):
        return self.carrying_months_calculation().carrying_months()

    def property_management_calculation(self):
        return AbstractPropertyManagementCalculation(
            self.property_id,
            self.analyzer,
            self.property_mgnt_model,
            self.property_permit_model,
            self.finance_model,
            self.rental_income_model,
            self.tax_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
        )

    def total_property_management_fees(self):
        # if self.check_cash_payment():
        property_management_fees = (
            self.property_management_calculation().property_management_fees()
        )
        property_management_fees = (property_management_fees or 0) * (
            self.owning_months() or 0
        )
        return round(property_management_fees, 2)

    def other_expenses_calculation(self):
        return AbstractOtherExpensesCalculations(
            self.property_id,
            self.analyzer,
            self.rental_income_model,
            self.finance_model,
            self.remodel_cost_model,
            self.aesthetic_items_model,
            self.non_aesthetic_items_model,
            self.additional_remodel_items_model,
            self.propertyinfo_model,
            self.property_permit_model,
            self.other_costs_model,
            self.closing_cost_model,
            self.carrying_cost_model,
            self.tax_model,
            self.property_mgnt_model,
            self.est_resale_value_model,
        )

    def total_city_and_permit_fees(self):
        return self.other_expenses_calculation().total_property_permit_impact_and_fees()

    def total_architectural_fees(self):
        return self.other_expenses_calculation().total_architectural_fees()

    def total_tax_payment(self):

        total_tax = AbstractTaxCalculations(
            self.property_id, self.tax_model, self.finance_model
        ).taxes()
        total_tax = (total_tax or 0) * (self.carrying_months() or 0)
        return round(total_tax, 2)

    def total_title_and_escrow_fees(self):
        return round(self.closing_cost_calculation().title_and_escrow_fees(), 2)

    def total_home_insurance(self):
        home_insurance = self.property_management_calculation().monthly_home_insurance()
        total_home_insurance = (home_insurance or 0) * (self.carrying_months() or 0)
        return round(total_home_insurance, 2)

    def total_hoa_dues(self):
        monthly_hoa_dues = self.property_management_calculation().monthly_hoa_dues()
        total_hoa_dues = (monthly_hoa_dues or 0) * (self.carrying_months() or 0)
        return round(total_hoa_dues, 2)

    def total_miscellaneous_cost(self):
        miscellaneous = AbstractOtherCostsCalculation(
            self.property_id, self.other_costs_model
        ).miscellaneous()
        total_miscellaneous = miscellaneous or 0
        return total_miscellaneous

    def total_liquid_capital(self):
        """
        purchase price = 3rd and 4th(last one) option is checked before the pay cash only for property
        remodel cost = 2 and 4th options
        total monthly payment is from the finance options and then multiple
                 it by the number of months you are holding the property
        Liquid Capital Required = Est. Front-end Closing Cost (Cash Payment) +
            Acquisition or Purchase Price (If cash for Property) +
        Down-payment + Remodel Cost (If its cash payment) OR
            [Remodel Loan Total Monthly Loan Payment (Cash Payment) X carrying Month]  +
        [Home Purchase Total Monthly Loan Payment (Cash Payment) X carrying Month] +
            Total Property Management Fees (Cash Payment) x Carry months +
        City & Impact  Fees (Cash Payment) + Architectural Fees (Cash Payment) +
            Total Tax Payment x Carrying Month + Tittle & Escrow Fee +
        Monthly Home Insurance X Carrying Months + Monthly HOA Dues X Carrying Months +
            Monthly Miscellaneous X Carrying Month
        """
        total_liquid_capital = (
            (self.frontend_closing_cost() or 0)
            + (self.total_acquisition_cost() or 0)
            + (self.downpayment_cost() or 0)
            + (self.secondary_remodel_loan() or 0)
            + (self.primary_home_loan_monthly_payment() or 0)
            + (self.total_property_management_fees() or 0)
            + (self.total_city_and_permit_fees() or 0)
            + (self.total_architectural_fees() or 0)
            # + (self.total_tax_payment() or 0)
            + (self.total_home_insurance() or 0)
            + (self.total_hoa_dues() or 0)
            + (self.total_miscellaneous_cost() or 0)
        )
        return round(total_liquid_capital, 2)

    def other_liquid_capital_expenses(self):
        other_lce = (
            self.primary_home_loan_monthly_payment()
            + self.secondary_remodel_loan()
            + self.total_property_management_fees()
            + self.total_home_insurance()
            + self.total_hoa_dues()
            + self.total_miscellaneous_cost()
        )
        return round(other_lce, 2)

    def total_liquid_capital_required_summary(self):
        data = {
            "est_frontend_closing_cost": self.frontend_closing_cost(),
            "acquisition_cost": self.acquisition_cost(),
            "downpayment_for_home_loan": self.downpayment_cost(),
            "downpayment_for_remodel_loan": self.secondary_downpayment_cost(),
            "secondary_remodel_loan": self.secondary_remodel_loan(),
            "primary_home_loan_monthly_payment": self.primary_home_loan_monthly_payment(),
            "total_property_management_fees": self.total_property_management_fees(),
            "total_city_and_impact_fees": self.total_city_and_permit_fees(),
            "total_architectural_fees": self.total_architectural_fees(),
            "total_tax_payment": self.total_tax_payment(),
            "total_home_insurance": self.total_home_insurance(),
            "total_hoa_dues": self.total_hoa_dues(),
            "total_miscellaneous_cost": self.total_miscellaneous_cost(),
            "asset_appreciation": self.asset_appreciation(),
            "total_remodel_cost": self.remodel_cost(),
            "total_monthly_carrying_cost": self.total_carrying_cost(),
            "total_liquid_capital": self.total_liquid_capital(),
            "lender_point": self.lender_points(),
            "other_liquid_capital_expenses": self.other_liquid_capital_expenses(),
        }
        return data
