class AbstractRentalIncomeCalculation:
    def __init__(self, property_id, rental_income_model):
        self.property_id = property_id
        self.rental_income_model = rental_income_model
        self.obj = self.get_object() if self.get_object() else None

    def get_object(self):
        try:
            obj = self.rental_income_model.objects.get(property_info=self.property_id)
            return obj
        except self.rental_income_model.DoesNotExist:
            return None

    def total_vacancy_factor_loss_factor(self):
        if self.get_object() is None:
            return None

        obj = self.get_object()
        total_vacancy_factor_loss = 0
        total_vacancy_factor_loss = (obj.vacancy_months or 0) * (
            obj.est_rental_income_per_month or 0
        )
        return total_vacancy_factor_loss

    def vacancy_rates(self):
        if self.get_object() is None:
            return None

        obj = self.get_object()

        annual_rental_income = (obj.est_rental_income_per_month or 1) * 12
        vacancy_rates = (
            self.total_vacancy_factor_loss_factor() * 100
        ) / annual_rental_income

        return round(vacancy_rates, 2)

    def owning_months(self):

        obj = self.get_object()
        try:
            owning_months = obj.owning_months
            if obj.owning_months_month_or_year == "1":
                owning_months = owning_months * 12
            return owning_months
        except Exception:
            owning_months = 0.0
            return owning_months

    def est_rental_income_per_month(self):
        obj = self.get_object()

        try:
            est_rental_income_per_month = obj.est_rental_income_per_month
            return est_rental_income_per_month

        except Exception:
            est_rental_income_per_month = 0.0
            return est_rental_income_per_month

    def rental_info(self):

        if self.get_object() is None:
            return None

        obj = self.get_object()
        owning_months = self.owning_months()

        info = {
            "owning_months": owning_months,
            "est_rental_income_per_month": obj.est_rental_income_per_month,
        }

        return info

    def rental_income(self):
        owning_months = self.owning_months()

        est_rental_income_per_month = self.est_rental_income_per_month()
        rental_income = (owning_months or 0) * (est_rental_income_per_month or 0)
        return rental_income

    def rental_income_summary(self):
        data = {
            "total_vacancy_factor_loss_factor": self.total_vacancy_factor_loss_factor(),
            "vacancy_rates": self.vacancy_rates(),
            "rental_income": self.rental_income(),
            "monthly_rental_income": self.est_rental_income_per_month(),
        }

        return data
