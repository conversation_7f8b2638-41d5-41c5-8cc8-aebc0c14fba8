from django.db.models import Q
from rest_framework.permissions import BasePermission
from deal_analyzer.models.subscription import OneTimeSubscription
from register.models import StripeSubscription
from django.utils import timezone

now = timezone.now()


class SubscriptionPermission(BasePermission):
    message = "You do not have permission to perform any deal analysis."

    # bpo-farm, combo-package, deal-analyzer packages that gives you access to deal-analyzer

    def has_permission(self, request, *args, **kwargs):
        if request.method == "POST":
            queryset = StripeSubscription.objects.filter(
                Q(
                    user__id=request.user.id,
                    package__iregex=r"(deal-analyzer|deal analyzer)",
                    current_period_end__gte=now,
                )
                | Q(
                    user__id=request.user.id,
                    package="combo-package",
                    current_period_end__gte=now,
                )
                | Q(
                    user__id=request.user.id,
                    package="bpo-farm",
                    current_period_end__gte=now,
                )
                | Q(
                    user__id=request.user.id,
                    package="bpo-realty-agent",
                    current_period_end__gte=now,
                )
                | Q(
                    user__id=request.user.id,
                    package="premier-agent-website",
                    current_period_end__gte=now,
                )
            )
            if not queryset:
                queryset = OneTimeSubscription.objects.filter(customer__user=request.user).values("status")
                for instance in queryset:
                    if instance["status"] == "Pending":
                        return True
                    else:
                        return False
            if queryset:
                return True
            else:
                return False
        else:
            return True
