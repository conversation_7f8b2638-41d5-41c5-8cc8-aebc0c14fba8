def refactor_investors_profit(data, dollar_or_percent=0):
    refactored_data = []

    for entry in data:
        refactored_entry = {
            "investment_values_dollar_or_percent": dollar_or_percent,
            "investor": entry["investor"],
            "equity_in_value": entry["equity"],
            "profit_in_value": entry["profit"],
            "roi_in_value": entry["roi"],
            "annual_roi": entry["roi_a"],
        }
        refactored_data.append(refactored_entry)

    return refactored_data


def refactor_data(input_data):
    format_data = {}
    investors_profit = []
    dollar_percent_checker = 0

    for data_dict in input_data:
        for investor_data in data_dict["investors_profit"]:
            investor_entry = {
                "investor": investor_data["investor"],
                "roi": str(investor_data["roi_in_value"]),
                "roi_a": str(investor_data["annual_roi"]),
                "equity": int(investor_data["equity_in_value"]),
                "profit": int(investor_data["profit_in_value"]),
            }
            investors_profit.append(investor_entry)

            if investor_data["investment_values_dollar_or_percent"] == "1":
                dollar_percent_checker = 1

        format_data = {
            "investors_profit": investors_profit,
            "totalLiquidRequired": data_dict["total_liquid_capital_required"],
            "totalExpenseAmount": data_dict["total_expenses"],
            "estNetProfit": data_dict["estimated_net_profit"],
            "dollar_percent_checker": dollar_percent_checker,
        }

    return format_data

