from datetime import datetime
from django.db import models
from deal_analyzer.models.abstract_models import (
    AbstractAdditionRemodelItems,
    AbstractAestheticItems,
    AbstractCarryingMonths,
    AbstractClosingCost,
    AbstractCustomComps,
    AbstractEstimatedResaleValue,
    AbstractInvestorProfit,
    AbstractNonAestheticItems,
    AbstractOtherCosts,
    AbstractPropertyInfo,
    AbstractPropertyManagement,
    AbstractPropertyPermitFees,
    AbstractRemodelCost,
    AbstractRentalIncome,
    AbstractSummaryText,
    AbstractTaxes,
    AstractFinanceOptions,
)


class RDSPropertyInfo(AbstractPropertyInfo):
    pass


class _RDSRelation(models.Model):
    property_info = models.ForeignKey(RDSPropertyInfo, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.property_info.created = datetime.now()
        self.property_info.save()
        super().save(*args, **kwargs)


class RDSFinanceOptions(AstractFinanceOptions, _RDSRelation):
    pass


class RDSPropertyPermitFees(AbstractPropertyPermitFees, _RDSRelation):
    pass


class RDSTaxes(AbstractTaxes, _RDSRelation):
    pass


class RDSAestheticItems(AbstractAestheticItems, _RDSRelation):
    pass


class RDSNonAestheticItems(AbstractNonAestheticItems, _RDSRelation):
    pass


class RDSAdditionRemodelItems(AbstractAdditionRemodelItems, _RDSRelation):
    pass


class RDSRemodelCost(AbstractRemodelCost, _RDSRelation):
    est_new_construction_cost_psqft = models.FloatField(null=True)


class RDSClosingCost(AbstractClosingCost, _RDSRelation):
    pass


class RDSRentalIncome(AbstractRentalIncome, _RDSRelation):
    pass


class RDSPropertyManagement(AbstractPropertyManagement, _RDSRelation):
    pass


class RDSOtherCosts(AbstractOtherCosts, _RDSRelation):
    pass


class RDSCarryingMonths(AbstractCarryingMonths, _RDSRelation):
    pass


class RDSEstResaleValue(AbstractEstimatedResaleValue, _RDSRelation):
    pass


class RDSInvestorProfit(AbstractInvestorProfit, _RDSRelation):
    pass


class RDSSummaryText(AbstractSummaryText, _RDSRelation):
    pass


class RDSCustomComps(AbstractCustomComps, _RDSRelation):
    pass
