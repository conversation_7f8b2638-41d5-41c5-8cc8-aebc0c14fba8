from datetime import datetime
from django.db import models
from deal_analyzer.models import global_choices
from deal_analyzer.models.abstract_models import (
    AbstractAdditionRemodelItems,
    AbstractAestheticItems,
    AbstractCarryingMonths,
    AbstractClosingCost,
    AbstractCustomComps,
    AbstractEstimatedResaleValue,
    AbstractInvestorProfit,
    AbstractNonAestheticItems,
    AbstractOtherCosts,
    AbstractPropertyInfo,
    AbstractPropertyManagement,
    AbstractPropertyPermitFees,
    AbstractRentalIncome,
    AbstractSummaryText,
    AbstractTaxes,
    AstractFinanceOptions,
)


class NCPropertyInfo(AbstractPropertyInfo):
    new_construction_sqft = models.CharField(max_length=255, null=True)


class _NCRelation(models.Model):
    property_info = models.ForeignKey(NCPropertyInfo, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.property_info.created = datetime.now()
        self.property_info.save()
        super().save(*args, **kwargs)


class NCFinanceOptions(AstractFinanceOptions, _NCRelation):
    deal_finance_option = models.CharField(
        max_length=100,
        choices=global_choices.DEAL_FINANCE_OPTION_NEW_CONSTRUCTIOn,
        null=True,
        default=0,
    )
    new_construction_cost = models.FloatField(null=True)


class NCPropertyPermitFees(AbstractPropertyPermitFees, _NCRelation):
    pass


class NCTaxes(AbstractTaxes, _NCRelation):
    pass


class NCDemolishingCost(_NCRelation, models.Model):
    demolishing_cost_option = models.BooleanField(default=False, null=True)
    demolishing_cost_dollar_or_percent = models.CharField(
        max_length=50, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    demolishing_cost = models.FloatField(null=True)


class NCDevelopmentCost(_NCRelation, models.Model):
    new_construction_cost_psqft = models.FloatField(null=True)
    remodel_cost_option = models.CharField(
        max_length=50, choices=global_choices.REMODEL_COST_OPTION, null=True, default=0
    )


class NCAestheticItems(AbstractAestheticItems, _NCRelation):
    pass


class NCNonAestheticItems(AbstractNonAestheticItems, _NCRelation):
    pass


class NCAdditionRemodelItems(AbstractAdditionRemodelItems, _NCRelation):
    pass


class NCClosingCost(AbstractClosingCost, _NCRelation):
    pass


class NCRentalIncome(AbstractRentalIncome, _NCRelation):
    pass


class NCPropertyManagement(AbstractPropertyManagement, _NCRelation):
    pass


class NCOtherCosts(AbstractOtherCosts, _NCRelation):
    pass


class NCCarryingMonths(AbstractCarryingMonths, _NCRelation):
    pass


class NCEstResaleValue(AbstractEstimatedResaleValue, _NCRelation):
    pass


class NCInvestorProfit(AbstractInvestorProfit, _NCRelation):
    pass


class NCSummaryText(AbstractSummaryText, _NCRelation):
    pass


class NCCustomComps(AbstractCustomComps, _NCRelation):
    pass
