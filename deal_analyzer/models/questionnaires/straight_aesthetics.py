from django.db import models
from deal_analyzer.models.questionnaires.abstract_questions import AbstractQuestions


class StraightAestheticsQuestions(AbstractQuestions):
    pass


class Investors(models.Model):
    investor_value = models.FloatField(null=True)
    straight_aesthetics = models.ForeignKey(
        StraightAestheticsQuestions,
        related_name="investors",
        null=True,
        on_delete=models.CASCADE,
    )
