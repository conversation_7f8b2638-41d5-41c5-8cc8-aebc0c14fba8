from django.db import models
from deal_analyzer.models.questionnaires.abstract_questions import AbstractQuestions


class NewConstructionQuestions(AbstractQuestions):
    square_footage = models.FloatField(null=True)
    demolishing_cost_option = models.BooleanField(default=False, null=True)
    demolishing_cost = models.FloatField(null=True)
    est_development_cost = models.FloatField(null=True)


class NCInvestors(models.Model):
    investor_value = models.FloatField(null=True)
    new_construction = models.ForeignKey(
        NewConstructionQuestions,
        related_name="investors",
        null=True,
        on_delete=models.CASCADE,
    )
