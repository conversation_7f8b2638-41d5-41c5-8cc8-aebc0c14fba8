from django.db import models
from deal_analyzer.models.questionnaires.abstract_questions import AbstractQuestions


class RemodelDownToStudsAndAdditionQuestions(AbstractQuestions):
    square_footage = models.FloatField(null=True)


class RDSAInvestors(models.Model):
    investor_value = models.FloatField(null=True)
    remodel_down_to_studs_and_addition = models.ForeignKey(
        RemodelDownToStudsAndAdditionQuestions,
        related_name="investors",
        null=True,
        on_delete=models.CASCADE,
    )
