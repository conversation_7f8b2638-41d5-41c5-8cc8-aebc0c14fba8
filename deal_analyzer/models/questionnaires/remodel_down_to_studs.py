from django.db import models
from deal_analyzer.models.questionnaires.abstract_questions import AbstractQuestions


class RemodelDownToStudsQuestions(AbstractQuestions):
    pass


class RDSInvestors(models.Model):
    investor_value = models.FloatField(null=True)
    remodel_down_to_studs = models.ForeignKey(
        RemodelDownToStudsQuestions,
        related_name="investors",
        null=True,
        on_delete=models.CASCADE,
    )
