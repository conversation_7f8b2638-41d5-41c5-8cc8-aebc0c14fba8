from django.db import models
from deal_analyzer.models.questionnaires.abstract_questions import AbstractQuestions


class BuyAndRentQuestions(AbstractQuestions):
    square_footage = models.FloatField(null=True)


class BRInvestors(models.Model):
    investor_value = models.FloatField(null=True)
    buy_and_rent = models.ForeignKey(
        BuyAndRentQuestions,
        related_name="investors",
        null=True,
        on_delete=models.CASCADE,
    )