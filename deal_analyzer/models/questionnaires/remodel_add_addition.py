from django.db import models
from deal_analyzer.models.questionnaires.abstract_questions import AbstractQuestions


class RemodelAddAdditionQuestions(AbstractQuestions):
    square_footage = models.FloatField(null=True)


class RAAInvestors(models.Model):
    investor_value = models.FloatField(null=True)
    remodel_add_addition = models.ForeignKey(
        RemodelAddAdditionQuestions,
        related_name="investors",
        null=True,
        on_delete=models.CASCADE,
    )
