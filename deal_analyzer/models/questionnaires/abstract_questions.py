from django.db import models
from deal_analyzer.utils import global_choices
from django.conf import settings



class AbstractQuestions(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
    )
    property_address = models.CharField(max_length=255, null=True, blank=True)
    deal_finance_option = models.CharField(
        max_length=100, choices=global_choices.DEAL_FINANCE_OPTION, null=True, default=0
    )
    primary_purchase_price = models.FloatField(null=True)
    primary_remodel_cost = models.FloatField(null=True)
    primary_wholesale_fee_option = models.BooleanField(null=True, default=False)
    primary_wholesale_fee = models.FloatField(null=True)
    primary_wholesale_fee_secondary_option = models.BooleanField(
        null=True, default=False
    )
    primary_lender_points_option = models.BooleanField(null=True, default=False)
    primary_lender_points = models.FloatField(null=True)
    primary_lender_points_secondary_option = models.BooleanField(
        null=True, default=False
    )
    primary_loan_type = models.CharField(
        max_length=20, choices=global_choices.LOAN_TYPE, null=True
    )
    primary_sub_loan_type = models.CharField(
        max_length=255, choices=global_choices.SUB_LOAN_TYPE, null=True
    )
    primary_loan_amount = models.FloatField(null=True)
    primary_down_payment = models.FloatField(null=True)
    primary_down_payment_dollar_or_percent = models.CharField(
        max_length=255, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    primary_down_payment_secondary_option = models.BooleanField(
        null=True, default=False
    )
    primary_interest_rate = models.FloatField(null=True)

    primary_term_of_loan = models.FloatField(null=True)
    primary_term_of_loan_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    primary_term_of_loan_secondary_option = models.BooleanField(
        null=True, default=False
    )
    primary_annual_interest_rate = models.FloatField(null=True)
    primary_annual_interest_rate_secondary_option = models.BooleanField(
        null=True, default=False
    )
    primary_interest_only_term = models.FloatField(null=True)
    primary_interest_only_term_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    primary_interest_only_term_secondary_option = models.BooleanField(
        null=True, default=False
    )
    cash_amount = models.FloatField(null=True)

    # secondary remodel loan option

    secondary_wholesale_fee_option = models.BooleanField(null=True, default=False)
    secondary_wholesale_fee = models.FloatField(null=True)
    secondary_lender_points_option = models.BooleanField(null=True, default=False)
    secondary_lender_points = models.FloatField(null=True)
    secondary_loan_type = models.CharField(
        max_length=20, choices=global_choices.LOAN_TYPE, null=True
    )
    secondary_sub_loan_type = models.CharField(
        max_length=255, choices=global_choices.SUB_LOAN_TYPE, null=True
    )
    secondary_loan_amount = models.FloatField(null=True)
    secondary_down_payment = models.FloatField(null=True)
    secondary_down_payment_dollar_or_percent = models.CharField(
        max_length=255, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    secondary_interest_rate = models.FloatField(null=True)
    secondary_term_of_loan = models.FloatField(null=True)
    secondary_term_of_loan_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    secondary_annual_interest_rate = models.FloatField(null=True)
    secondary_interest_only_term = models.FloatField(null=True)
    secondary_interest_only_term_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    owning_months = models.FloatField(null=True)
    owning_months_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    rental_income_option = models.BooleanField(null=True, default=False)
    monthly_rental_income = models.FloatField(null=True)
    property_manager_option = models.BooleanField(null=True, default=False)
    investors_option = models.BooleanField(null=True, default=False)
    information = models.TextField(null=True)
    contact_name = models.CharField(max_length=255, null=True)
    contact_phone = models.CharField(max_length=255, null=True)
    contact_email = models.CharField(max_length=255, null=True)
    
    class Meta:
        abstract = True