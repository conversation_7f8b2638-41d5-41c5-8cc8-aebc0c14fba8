from datetime import datetime
from django.db import models
from deal_analyzer.models.abstract_models import (
    AbstractAdditionRemodelItems,
    AbstractAestheticItems,
    AbstractCarryingMonths,
    AbstractClosingCost,
    AbstractCustomComps,
    AbstractEstimatedResaleValue,
    AbstractInvestorProfit,
    AbstractNonAestheticItems,
    AbstractOtherCosts,
    AbstractPropertyInfo,
    AbstractPropertyManagement,
    AbstractPropertyPermitFees,
    AbstractRemodelCost,
    AbstractRentalIncome,
    AbstractSummaryText,
    AbstractTaxes,
    AstractFinanceOptions,
)


class BRPropertyInfo(AbstractPropertyInfo):
    pass


class _BRRelation(models.Model):
    property_info = models.ForeignKey(BRPropertyInfo, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.property_info.created = datetime.now()
        self.property_info.save()
        super().save(*args, **kwargs)


class BRFinanceOptions(AstractFinanceOptions, _BRRelation):
    pass


class BRPropertyPermitFees(AbstractPropertyPermitFees, _BRRelation):
    pass


class BRTaxes(AbstractTaxes, _BRRelation):
    pass


class BRAestheticItems(AbstractAestheticItems, _BRRelation):
    pass


class BRNonAestheticItems(AbstractNonAestheticItems, _BRRelation):
    pass


class BRAdditionRemodelItems(AbstractAdditionRemodelItems, _BRRelation):
    pass


class BRRemodelCost(AbstractRemodelCost, _BRRelation):
    est_new_construction_cost_psqft = models.FloatField(null=True)


class BRClosingCost(AbstractClosingCost, _BRRelation):
    pass


class BRRentalIncome(AbstractRentalIncome, _BRRelation):
    pass


class BRPropertyManagement(AbstractPropertyManagement, _BRRelation):
    pass


class BROtherCosts(AbstractOtherCosts, _BRRelation):
    pass


class BRCarryingMonths(AbstractCarryingMonths, _BRRelation):
    pass


class BREstResaleValue(AbstractEstimatedResaleValue, _BRRelation):
    pass


class BRInvestorProfit(AbstractInvestorProfit, _BRRelation):
    pass


class BRSummaryText(AbstractSummaryText, _BRRelation):
    pass


class BRCustomComps(AbstractCustomComps, _BRRelation):
    pass
