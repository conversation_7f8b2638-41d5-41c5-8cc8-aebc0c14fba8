from datetime import datetime
from django.db import models
from deal_analyzer.models.abstract_models import (
    AbstractAdditionRemodelItems,
    AbstractAestheticItems,
    AbstractCarryingMonths,
    AbstractClosingCost,
    AbstractCustomComps,
    AbstractEstimatedResaleValue,
    AbstractInvestorProfit,
    AbstractNonAestheticItems,
    AbstractOtherCosts,
    AbstractPropertyInfo,
    AbstractPropertyManagement,
    AbstractPropertyPermitFees,
    AbstractRemodelCost,
    AbstractRentalIncome,
    AbstractSummaryText,
    AbstractTaxes,
    AstractFinanceOptions,
)


class RDSAPropertyInfo(AbstractPropertyInfo):
    pass


class _RDSARelation(models.Model):
    property_info = models.ForeignKey(RDSAPropertyInfo, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.property_info.created = datetime.now()
        self.property_info.save()
        super().save(*args, **kwargs)


class RDSAFinanceOptions(AstractFinanceOptions, _RDSARelation):
    pass


class RDSAPropertyPermitFees(AbstractPropertyPermitFees, _RDSARelation):
    pass


class RDSATaxes(AbstractTaxes, _RDSARelation):
    pass


class RDSAAestheticItems(AbstractAestheticItems, _RDSARelation):
    pass


class RDSANonAestheticItems(AbstractNonAestheticItems, _RDSARelation):
    pass


class RDSAAdditionRemodelItems(AbstractAdditionRemodelItems, _RDSARelation):
    pass


class RDSARemodelCost(AbstractRemodelCost, _RDSARelation):
    est_new_construction_cost_psqft = models.FloatField(null=True)


class RDSAClosingCost(AbstractClosingCost, _RDSARelation):
    pass


class RDSARentalIncome(AbstractRentalIncome, _RDSARelation):
    pass


class RDSAPropertyManagement(AbstractPropertyManagement, _RDSARelation):
    pass


class RDSAOtherCosts(AbstractOtherCosts, _RDSARelation):
    pass


class RDSACarryingMonths(AbstractCarryingMonths, _RDSARelation):
    pass


class RDSAEstResaleValue(AbstractEstimatedResaleValue, _RDSARelation):
    pass


class RDSAInvestorProfit(AbstractInvestorProfit, _RDSARelation):
    pass


class RDSASummaryText(AbstractSummaryText, _RDSARelation):
    pass


class RDSACustomComps(AbstractCustomComps, _RDSARelation):
    pass
