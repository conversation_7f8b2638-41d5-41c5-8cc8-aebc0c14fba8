from django.db import models
from django.conf import settings
from deal_analyzer.utils import global_choices
from datetime import datetime
import uuid


class AbstractPropertyInfo(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
    )
    property_address = models.CharField(
        max_length=255, null=True, blank=True, default=""
    )
    listing_date = models.Char<PERSON>ield(max_length=255, null=True, default=datetime.now)
    listing_source = models.CharField(
        max_length=20, choices=global_choices.LISTING_SOURCE_TYPE, null=True, default=0
    )
    bedrooms = models.IntegerField(null=True)
    bathrooms = models.IntegerField(null=True)
    lot_size = models.IntegerField(null=True)
    pre_existing_livable_sqft = models.CharField(max_length=255, null=True)
    garage = models.Char<PERSON>ield(max_length=255, null=True)
    additional_sqrft = models.IntegerField(null=True)
    year_of_construction = models.Char<PERSON><PERSON>(
        max_length=255, null=True, default=datetime.now
    )
    property_type = models.Char<PERSON>ield(
        max_length=255, choices=global_choices.PROPERTY_TYPE, null=True, default=0
    )
    completed_round = models.<PERSON>oleanField(default=False)
    created = models.Char<PERSON>ield(
        max_length=255, null=True, default=datetime.now, blank=True
    )
    updated = models.CharField(
        max_length=255, null=True, default=datetime.now, blank=True
    )
    carpot_or_garage = models.CharField(
        max_length=255, null=True, default=datetime.now, blank=True
    )
    other_listing_source = models.CharField(max_length=255, null=True, blank=True, default="")
    other_property_type = models.CharField(max_length=255, null=True, blank=True, default="")
    adu = models.CharField(max_length=255, null=True, blank=True, default="")
    additional_units = models.JSONField(null=True)
    basement_psqf = models.CharField(max_length=255, null=True, blank=True, default="")
    additional_data = models.JSONField(null=True, default=dict)

    class Meta:
        abstract = True

    def update(self, *args, **kwargs):
        kwargs.update({"updated": datetime.now})
        super().update(*args, **kwargs)

        return self


class AstractFinanceOptions(models.Model):
    deal_finance_option = models.CharField(
        max_length=100, choices=global_choices.DEAL_FINANCE_OPTION, null=True, default=0
    )
    primary_loan_type = models.CharField(
        max_length=20, choices=global_choices.LOAN_TYPE, null=True, default=0
    )
    primary_sub_loan_type = models.CharField(
        max_length=255, choices=global_choices.SUB_LOAN_TYPE, null=True, default=0
    )
    primary_purchase_price = models.FloatField(null=True)
    primary_lender_points = models.FloatField(null=True)
    primary_lender_points_options = models.CharField(
        max_length=255,
        choices=global_choices.LENDER_POINTS_OPTIONS,
        null=True,
        default=0,
    )
    primary_wholesale_fee = models.IntegerField(null=True)
    primary_wholesale_fee_options = models.CharField(
        max_length=255,
        choices=global_choices.WHOLESALE_FEE_OPTIONS,
        null=True,
        default=0,
    )
    primary_dollar_or_percent = models.CharField(
        max_length=255, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    primary_down_payment = models.CharField(max_length=255, null=True)
    pmi_value = models.CharField(max_length=255, null=True, blank=True, default="")
    primary_include_pmi = models.BooleanField(default=False, null=True)
    primary_interest_rate = models.FloatField(null=True)
    primary_term_of_loan = models.FloatField(null=True)
    primary_term_of_loan_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    primary_mortgage_term_of_loan = models.FloatField(null=True)
    primary_mortgage_term_of_loan_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    primary_annual_interest_rate = models.FloatField(null=True)
    primary_interest_only_term = models.FloatField(null=True)
    primary_interest_only_term_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    primary_total_remodel_cost = models.FloatField(null=True)
    #  secondary remodel
    secondary_loan_type = models.CharField(
        max_length=20, choices=global_choices.LOAN_TYPE, null=True, default=0
    )
    secondary_sub_loan_type = models.CharField(
        max_length=255, choices=global_choices.SUB_LOAN_TYPE, null=True, default=0
    )
    secondary_purchase_price = models.FloatField(null=True)
    secondary_lender_points = models.FloatField(null=True)
    secondary_lender_points_options = models.CharField(
        max_length=255,
        choices=global_choices.LENDER_POINTS_OPTIONS,
        null=True,
        default=0,
    )
    secondary_dollar_or_percent = models.CharField(
        max_length=255, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    secondary_down_payment = models.CharField(max_length=255, null=True)
    secondary_include_pmi = models.BooleanField(default=False, null=True)
    secondary_loan_amount = models.FloatField(null=True)
    secondary_interest_rate = models.FloatField(null=True)
    secondary_term_of_loan = models.FloatField(null=True)
    secondary_term_of_loan_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    secondary_mortgage_term_of_loan_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    secondary_mortgage_term_of_loan = models.FloatField(null=True)
    secondary_annual_interest_rate = models.FloatField(null=True)
    secondary_interest_only_term = models.FloatField(null=True)
    secondary_interest_only_term_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    secondary_total_remodel_cost = models.FloatField(null=True)
    cash_for_home_purchase_price = models.FloatField(null=True)
    cash_for_home_lender_points = models.FloatField(null=True)
    cash_for_home_lender_points_options = models.CharField(
        max_length=255,
        choices=global_choices.LENDER_POINTS_OPTIONS,
        null=True,
        default=0,
    )

    class Meta:
        abstract = True


class AbstractPropertyPermitFees(models.Model):
    city_permit_impact_fees_dollar_or_percent = models.CharField(
        max_length=255, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    city_permit_impact_fees = models.FloatField(null=True)
    title_and_escrow_fees_dollar_or_percent = models.CharField(
        max_length=255, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    title_and_escrow_fees = models.FloatField(null=True)
    architectural_fees = models.FloatField(null=True)
    monthly_home_insurance = models.FloatField(null=True)
    monthly_hoa_dues = models.FloatField(null=True)

    class Meta:
        abstract = True


class AbstractTaxes(models.Model):
    city_and_county_tax = models.FloatField(null=True)
    city_and_county_tax_fees_dollar_or_percent = models.CharField(
        max_length=255, choices=global_choices.DollarOrPercent, null=True, default=0
    )

    class Meta:
        abstract = True


class AbstractRemodelCost(models.Model):
    est_remodel_cost_psqft = models.FloatField(null=True)
    remodel_cost_option = models.CharField(
        max_length=50, choices=global_choices.REMODEL_COST_OPTION, null=True, default=0
    )
    adu_price = models.CharField(max_length=255, null=True, blank=True, default="")
    additional_units_price = models.JSONField(null=True)
    basement_price = models.CharField(max_length=255, null=True, blank=True, default="")

    class Meta:
        abstract = True


class AbstractAestheticItems(models.Model):
    item = models.CharField(max_length=255, null=True)
    value = models.FloatField(null=True)

    class Meta:
        abstract = True


class AbstractNonAestheticItems(models.Model):
    item = models.CharField(max_length=255, null=True)
    value = models.FloatField(null=True)

    class Meta:
        abstract = True


class AbstractAdditionRemodelItems(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    item = models.CharField(max_length=255, null=True)
    value = models.FloatField(null=True)

    class Meta:
        abstract = True


class AbstractClosingCost(models.Model):
    # closing_cost_option_resell = models.CharField(
    #     max_length=255, choices=global_choices.TITLEANDESCROWFEES, null=True, default=1
    # )
    closing_cost_option_purchase = models.CharField(
        max_length=255, choices=global_choices.TITLEANDESCROWFEES, null=True, default=0
    )
    # commission_on_resale_dollar_or_percent = models.CharField(
    #     max_length=255, choices=global_choices.DollarOrPercent, default=0, null=True
    # )
    # closing_cost_credit = models.FloatField(null=True)
    # New Fields
    other_listing_source = models.CharField(max_length=255, null=True, blank=True, default="")
    title_and_escrow_fees = models.FloatField(null=True)
    title_and_escrow_fees_dollar_or_percent = models.FloatField(null=True)
    custom_title_and_escrow_buyer_pays_percent = models.FloatField(null=True)
    custom_title_and_escrow_buyer_pays = models.CharField(max_length=255, null=True, blank=True, default="")

    commission_on_purchase_dollar_or_percent = models.FloatField(null=True)
    commission_on_purchase = models.CharField(max_length=255, null=True, blank=True, default="")
    commission_on_purchase_option = models.CharField(max_length=255, null=True, blank=True, default="")
    custom_commission_on_purchase_dollar_or_percent = models.FloatField(null=True)
    custom_commission_on_purchase = models.CharField(max_length=255, null=True, blank=True, default="")
    closing_cost_credit_purchase = models.FloatField(null=True)

    title_and_escrow_fees_resale_dollar_or_percent = models.FloatField(null=True)
    title_and_escrow_fees_resale = models.CharField(max_length=255, null=True, blank=True, default="")
    closing_cost_option_resell = models.CharField(max_length=255, null=True, blank=True, default="")
    custom_title_and_escrow_on_resale_dollar_or_percent = models.CharField(
        max_length=255, null=True, blank=True, default="")
    custom_title_and_escrow_on_resale = models.CharField(max_length=255, null=True, blank=True, default="")

    commission_on_resale_dollar_or_percent = models.FloatField(null=True)
    commission_on_resale = models.CharField(max_length=255, null=True, blank=True, default="")
    commission_on_resale_option = models.CharField(max_length=255, null=True, blank=True, default="")

    custom_commission_on_resale_dollar_or_percent = models.FloatField(null=True)
    custom_commission_on_resale = models.CharField(max_length=255, null=True, blank=True, default="")
    closing_cost_credit = models.CharField(max_length=255, null=True, blank=True, default="")

    class Meta:
        abstract = True


class AbstractRentalIncome(models.Model):
    owning_months = models.FloatField(null=True)
    owning_months_month_or_year = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )
    est_rental_income_per_month = models.FloatField(null=True)
    vacancy_months = models.FloatField(null=True)

    class Meta:
        abstract = True


class AbstractPropertyManagement(models.Model):
    property_management_fees_dollar_or_percent = models.CharField(
        max_length=50, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    property_management_fees = models.FloatField(null=True)
    annual_maintenance_dollar_or_percent = models.CharField(
        max_length=50, choices=global_choices.DollarOrPercent, null=True, default=0
    )
    annual_maintenance = models.CharField(max_length=255, null=True, blank=True, default="")

    class Meta:
        abstract = True


class AbstractOtherCosts(models.Model):
    miscellaneous = models.FloatField(null=True)
    utilities = models.CharField(max_length=255, null=True, blank=True, default="")

    class Meta:
        abstract = True


class AbstractCarryingMonths(models.Model):
    carrying_cost_owning_months = models.FloatField(null=True)
    carrying_cost_owning_months_option = models.CharField(
        max_length=255, choices=global_choices.MonthsYearOption, null=True, default=0
    )

    class Meta:
        abstract = True


class AbstractEstimatedResaleValue(models.Model):

    average_square_footage = models.CharField(max_length=255, null=True)
    average_price_psqft = models.CharField(max_length=255, null=True)
    average_lot_size = models.CharField(max_length=255, null=True)
    average_purchase_price = models.CharField(max_length=255, null=True)
    number_of_comps = models.IntegerField(null=True)
    comps_option = models.CharField(
        max_length=255, choices=global_choices.COMPS_OPTION, null=True, default=2
    )
    desired_resale_value = models.IntegerField(null=True)
    resale_value_option = models.CharField(
        max_length=255, choices=global_choices.RESALE_VALUE_OPTION, null=True, default=1
    )
    appreciation_value_option_desired = models.CharField(max_length=255, null=True, blank=True, default="")
    appreciation_value_option = models.CharField(max_length=255, null=True, blank=True, default="")
    resale_value_option_v = models.CharField(max_length=255, null=True, blank=True, default="")
    annual_property_appreciation = models.CharField(max_length=255, null=True, blank=True, default="")

    class Meta:
        abstract = True


class AbstractInvestorProfit(models.Model):
    investment_values_dollar_or_percent = models.CharField(
        max_length=255, null=True, choices=global_choices.DollarOrPercent, default=0
    )
    investor = models.CharField(null=True, max_length=255)
    equity = models.FloatField(null=True)
    profit = models.FloatField(null=True)

    class Meta:
        abstract = True


class AbstractEstimatedResaleValueComps(models.Model):
    street = models.CharField(max_length=255, null=True)
    city = models.CharField(max_length=255, null=True)
    state_or_province = models.CharField(max_length=255, null=True)
    per_sqft = models.FloatField(null=True)
    price = models.FloatField(null=True)
    bedrooms = models.FloatField(null=True)
    bathrooms = models.FloatField(null=True)
    year_built = models.CharField(null=True, max_length=255)
    sqft = models.FloatField(null=True)
    lot_size = models.CharField(null=True, max_length=255)
    close_date = models.CharField(null=True, max_length=255)
    new_lot_size = models.CharField(null=True, max_length=255)
    site_address = models.CharField(null=True, max_length=255)
    owner_name = models.CharField(null=True, max_length=255)
    house_number = models.CharField(null=True, max_length=255)
    street_name = models.CharField(null=True, max_length=255)
    garage = models.IntegerField(default=0, null=True)

    class Meta:
        abstract = True


class AbstractSummaryText(models.Model):
    text = models.TextField(null=True, blank=True)

    class Meta:
        abstract = True


class Contractor(models.Model):
    location = models.CharField(null=True, max_length=255, blank=True)
    final_remodel = models.FloatField(null=True, max_length=255, blank=True)
    final_new_construction = models.FloatField(null=True, max_length=255, blank=True)


class AbstractCustomComps(models.Model):
    address = models.CharField(null=True, max_length=255)
    close_date = models.CharField(null=True, max_length=255)
    per_sqft = models.FloatField(null=True)
    price = models.FloatField(null=True)
    bedrooms = models.FloatField(null=True)
    bathrooms = models.FloatField(null=True)
    year_built = models.CharField(null=True, max_length=255)
    sqft = models.FloatField(null=True)
    lot_size = models.CharField(null=True, max_length=255)
    analyzer = models.CharField(null=True, max_length=255)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
    )

    class Meta:
        abstract = True
