from datetime import datetime
from django.db import models
from deal_analyzer.models.abstract_models import (
    AbstractAdditionRemodelItems,
    AbstractAestheticItems,
    AbstractCarryingMonths,
    AbstractClosingCost,
    AbstractCustomComps,
    AbstractEstimatedResaleValue,
    AbstractInvestorProfit,
    AbstractNonAestheticItems,
    AbstractOtherCosts,
    AbstractPropertyInfo,
    AbstractPropertyManagement,
    AbstractPropertyPermitFees,
    AbstractRemodelCost,
    AbstractRentalIncome,
    AbstractSummaryText,
    AbstractTaxes,
    AstractFinanceOptions,
)


class RAAPropertyInfo(AbstractPropertyInfo):
    pass


class _RAARelation(models.Model):
    property_info = models.ForeignKey(RAAPropertyInfo, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.property_info.created = datetime.now()
        self.property_info.save()
        super().save(*args, **kwargs)


class RAAFinanceOptions(AstractFinanceOptions, _RAARelation):
    pass


class RAAPropertyPermitFees(AbstractPropertyPermitFees, _RAARelation):
    pass


class RAATaxes(AbstractTaxes, _RAARelation):
    pass


class RAAAestheticItems(AbstractAestheticItems, _RAARelation):
    pass


class RAANonAestheticItems(AbstractNonAestheticItems, _RAARelation):
    pass


class RAAAdditionRemodelItems(AbstractAdditionRemodelItems, _RAARelation):
    pass


class RAARemodelCost(AbstractRemodelCost, _RAARelation):
    est_new_construction_cost_psqft = models.FloatField(null=True)


class RAAClosingCost(AbstractClosingCost, _RAARelation):
    pass


class RAARentalIncome(AbstractRentalIncome, _RAARelation):
    pass


class RAAPropertyManagement(AbstractPropertyManagement, _RAARelation):
    pass


class RAAOtherCosts(AbstractOtherCosts, _RAARelation):
    pass


class RAACarryingMonths(AbstractCarryingMonths, _RAARelation):
    pass


class RAAEstResaleValue(AbstractEstimatedResaleValue, _RAARelation):
    pass


class RAAInvestorProfit(AbstractInvestorProfit, _RAARelation):
    pass


class RAASummaryText(AbstractSummaryText, _RAARelation):
    pass


class RAACustomComps(AbstractCustomComps, _RAARelation):
    pass
