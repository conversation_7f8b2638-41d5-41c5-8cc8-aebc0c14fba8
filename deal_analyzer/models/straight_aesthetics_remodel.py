from datetime import datetime
from django.db import models
from deal_analyzer.models.abstract_models import (
    AbstractAdditionRemodelItems,
    AbstractAestheticItems,
    AbstractCarryingMonths,
    AbstractClosingCost,
    AbstractCustomComps,
    AbstractEstimatedResaleValue,
    AbstractEstimatedResaleValueComps,
    AbstractInvestorProfit,
    AbstractNonAestheticItems,
    AbstractOtherCosts,
    AbstractPropertyInfo,
    AbstractPropertyManagement,
    AbstractPropertyPermitFees,
    AbstractRemodelCost,
    AbstractRentalIncome,
    AbstractSummaryText,
    AbstractTaxes,
    AstractFinanceOptions,
)
from deal_analyzer.utils import global_choices


class PropertyInfo(AbstractPropertyInfo):
    pass


class _PRRelation(models.Model):
    property_info = models.ForeignKey(PropertyInfo, on_delete=models.CASCADE)

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.property_info.created = datetime.now()
        self.property_info.save()
        super().save(*args, **kwargs)


class FinanceOptions(AstractFinanceOptions, _PRRelation):
    cash_for_home_wholesale_fee = models.IntegerField(null=True)
    cash_for_home_wholesale_fee_options = models.CharField(
        max_length=255,
        choices=global_choices.WHOLESALE_FEE_OPTIONS,
        null=True,
        default=0,
    )


class PropertyPermitFees(AbstractPropertyPermitFees, _PRRelation):
    pass


class Taxes(AbstractTaxes, _PRRelation):
    pass


class RemodelCost(AbstractRemodelCost, _PRRelation):
    pass


class AestheticItems(AbstractAestheticItems, _PRRelation):
    pass


class NonAestheticItems(AbstractNonAestheticItems, _PRRelation):
    pass


class AdditionRemodelItems(AbstractAdditionRemodelItems, _PRRelation):
    pass


class ClosingCost(AbstractClosingCost, _PRRelation):
    pass


class RentalIncome(AbstractRentalIncome, _PRRelation):
    pass


class PropertyManagement(AbstractPropertyManagement, _PRRelation):
    pass


class OtherCosts(AbstractOtherCosts, _PRRelation):
    pass


class CarryingCost(AbstractCarryingMonths, _PRRelation):
    pass


class InvestorProfit(AbstractInvestorProfit, _PRRelation):
    pass


class EstimatedResaleValue(AbstractEstimatedResaleValue, _PRRelation):
    pass


class SummaryText(AbstractSummaryText):
    property_info = models.ForeignKey(PropertyInfo, on_delete=models.CASCADE, null=True)


class EstimatedResaleValueComps(AbstractEstimatedResaleValueComps):
    pass


class CustomComps(AbstractCustomComps):
    property_info = models.ForeignKey(PropertyInfo, on_delete=models.CASCADE, null=True)
