from django.db import models
import uuid

from leads.models import BaseTimeStamp


class InvestorProfitCalc(models.Model):
    investment_values_choices = (
        ("0", "Dollar"),
        ("1", "Percent"),
    )

    investor = models.CharField(max_length=255)
    investment_values_dollar_or_percent = models.CharField(
        max_length=1, choices=investment_values_choices
    )
    equity_in_value = models.FloatField(null=True)
    equity_in_percent = models.FloatField(null=True)
    profit_in_value = models.FloatField(null=True)
    profit_in_percent = models.FloatField(null=True)
    roi_in_percent = models.FloatField(null=True)
    roi_in_value = models.FloatField(null=True)
    annual_roi = models.FloatField(default=0, null=True)


class Expenses(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    total_liquid_capital_required = models.FloatField(null=True)
    total_expenses = models.FloatField(null=True)
    estimated_net_profit = models.FloatField(null=True)
    model_type = models.CharField(max_length=255, null=True)
    model_id = models.CharField(max_length=255, null=True)

    investors_profit = models.ManyToManyField(InvestorProfitCalc)


class ZillowComps(BaseTimeStamp):
    model_type = models.CharField(max_length=255, null=True)
    model_id = models.CharField(max_length=255, null=True)
    sales_price = models.DecimalField(max_digits=10, decimal_places=2)
    gross_living_area = models.PositiveIntegerField()
    lot_size = models.PositiveIntegerField()
    num_bedrooms = models.PositiveIntegerField()
    num_bathrooms = models.PositiveIntegerField()
    description = models.TextField()
    address = models.CharField(max_length=255)
    price_per_gross_living_area = models.DecimalField(max_digits=10, decimal_places=2)
    images = models.JSONField()
    new = models.BooleanField()
    year_built = models.PositiveIntegerField()

    def __str__(self):
        return f"Property Listing - {self.address}"


class ZillowCompsSummary(BaseTimeStamp):
    average_total_price = models.DecimalField(max_digits=10, decimal_places=2)
    average_gross_living_area = models.PositiveIntegerField()
    average_lot_size = models.PositiveIntegerField()
    average_average_price_per_square_footage = models.DecimalField(max_digits=10, decimal_places=2)
    model_type = models.CharField(max_length=255)
    model_id = models.PositiveIntegerField()

    def __str__(self):
        return f"Zillow Comps Summary - {self.model_type} (ID: {self.model_id})"
