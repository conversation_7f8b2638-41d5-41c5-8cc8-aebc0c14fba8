from django.db import models
from django.conf import settings
from deal_analyzer.utils import global_choices
from register.models import StripeCustomer


class OneTimeSubscription(models.Model):
    sub_type = models.CharField(max_length=255, choices=global_choices.SUBSCRIPTION_TYPE, default="deal_analyzer")
    customer = models.ForeignKey(StripeCustomer, on_delete=models.CASCADE, null=True, default=None)
    subscription = models.CharField(max_length=255, null=True, default=None)
    status = models.CharField(max_length=255, choices=global_choices.ONETIME_STATUS, default=global_choices.PENDING)
