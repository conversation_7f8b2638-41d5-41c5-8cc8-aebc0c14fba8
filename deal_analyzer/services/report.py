from datetime import datetime
from deal_analyzer.exceptions.errors import ResourceNotFound
from deal_analyzer.models.buy_and_rent import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BRPropertyInfo
from deal_analyzer.models.new_construction import <PERSON><PERSON>ustomComps, NCPropertyInfo
from deal_analyzer.models.remodel_add_addition import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RAAPropertyInfo
from deal_analyzer.models.remodel_down_studs import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>om<PERSON>, RDSPropertyInfo
from deal_analyzer.models.remodel_down_to_studs_and_addition import R<PERSON><PERSON>ustom<PERSON>om<PERSON>, RDSAPropertyInfo
from deal_analyzer.models.straight_aesthetics_remodel import CustomComps, PropertyInfo
from deal_analyzer.serializers.buy_and_rent import BR<PERSON><PERSON>om<PERSON>ompsSerializer
from deal_analyzer.serializers.new_construction import NC<PERSON>ustomCompsSerializer
from deal_analyzer.serializers.remodel_add_addition import RAACustomCompsSerializer
from deal_analyzer.serializers.remodel_down_studs import <PERSON><PERSON><PERSON>ustom<PERSON>ompsSerializer
from deal_analyzer.serializers.remodel_down_to_studs_and_addition import RDS<PERSON><PERSON>om<PERSON>ompsSerializer
from deal_analyzer.serializers.straight_aesthetics_remodel import Custom<PERSON><PERSON>psSerializer


class ReportTypeSwitch:
    def __init__(self, report_type, property_id, request_user, view=None):
        self.report_type = report_type
        self.property_id = property_id
        self.request_user = request_user
        self.view = view

    def check_model(
        self,
    ):
        default = "wrong_report"
        func_report_type = None
        try:
            report_type = int(self.report_type) if self.report_type else None
        except Exception:
            report_type = None

        #  rewrite this if else block to concise syntax

        if report_type == 1:
            func_report_type = "straight_aesthetics"
        elif report_type == 2:
            func_report_type = "remodel_add_addition"
        elif report_type == 3:
            func_report_type = "remodel_down_studs"
        elif report_type == 4:
            func_report_type = "remodel_down_studs_addition"
        elif report_type == 5:
            func_report_type = "new_construction"
        elif report_type == 6:
            func_report_type = "buy_and_rent"
        return getattr(self, str(func_report_type), lambda: default)()

    def straight_aesthetics(self):
        if self.view == "check_type":
            return {
                "model": PropertyInfo,
                "serializer": CustomCompsSerializer,
                "custom_comps": CustomComps
            }
        if self.view == "delete":
            return self.delete_property(PropertyInfo)
        return self.implementation(PropertyInfo)

    def remodel_add_addition(self):
        if self.view == "check_type":
            return {
                "model": RAAPropertyInfo,
                "serializer": RAACustomCompsSerializer,
                "custom_comps": RAACustomComps

            }
        if self.view == "delete":
            return self.delete_property(RAAPropertyInfo)
        return self.implementation(RAAPropertyInfo)

    def remodel_down_studs(self):
        if self.view == "check_type":
            return {
                "model": RDSPropertyInfo,
                "serializer": RDSCustomCompsSerializer,
                "custom_comps": RDSCustomComps

            }
        if self.view == "delete":
            return self.delete_property(RDSPropertyInfo)
        return self.implementation(RDSPropertyInfo)

    def remodel_down_studs_addition(self):
        if self.view == "check_type":
            return {
                "model": RDSAPropertyInfo,
                "serializer": RDSACustomCompsSerializer,
                "custom_comps": RDSACustomComps

            }
        if self.view == "delete":
            return self.delete_property(RDSAPropertyInfo)
        return self.implementation(RDSAPropertyInfo)

    def new_construction(self):
        if self.view == "check_type":
            return {
                "model": NCPropertyInfo,
                "serializer": NCCustomCompsSerializer,
                "custom_comps": NCCustomComps

            }
        if self.view == "delete":
            return self.delete_property(NCPropertyInfo)
        return self.implementation(NCPropertyInfo)

    def buy_and_rent(self):
        if self.view == "check_type":
            return {
                "model": BRPropertyInfo,
                "serializer": BRCustomCompsSerializer,
                "custom_comps": BRCustomComps

            }
        if self.view == "delete":
            return self.delete_property(BRPropertyInfo)
        return self.implementation(BRPropertyInfo)

    def implementation(self, analyzer):
        try:
            obj = analyzer.objects.get(id=self.property_id, user=self.request_user)
        except (analyzer.DoesNotExist, ValueError):
            raise ResourceNotFound
        obj.completed_round = True
        obj.updated = datetime.now()
        obj.save()
        return obj

    def delete_property(self, analyzer):
        try:
            obj = analyzer.objects.get(id=self.property_id, user=self.request_user)
        except (analyzer.DoesNotExist, ValueError):
            raise ResourceNotFound
        obj.delete()
        return None
