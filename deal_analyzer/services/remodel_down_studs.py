from deal_analyzer.calculations.remodel_down_studs.main_calculations import (
    RemodelDownStudsCalculations,
)
from deal_analyzer.exceptions.errors import (
    NoCalculationsForGivenId,
    NoStartedDealRound,
)
from deal_analyzer.models.remodel_down_studs import (
    RDSAdditionRemodelItems,
    RDSAestheticItems,
    RDSCarryingMonths,
    RDSClosingCost,
    RDSCustomComps,
    RDSEstResaleValue,
    RDSFinanceOptions,
    RDSInvestorProfit,
    RDSNonAestheticItems,
    RDSOtherCosts,
    RDSPropertyInfo,
    RDSPropertyManagement,
    RDSPropertyPermitFees,
    RDSRemodelCost,
    RDSRentalIncome,
    RDSSummaryText,
    RDSTaxes,
)
from deal_analyzer.serializers.remodel_down_studs import (
    RDSAdditionRemodelItemsSerializer,
    RDSAestheticItemsSerializer,
    RDSCarryingMonthsSerializer,
    RDSClosingCostSerializer,
    RDSEstResaleValueSerializer,
    RDSFinanceOptionsSerializer,
    RDSInvestorProfitSerializer,
    RDSNonAestheticItemsSerializer,
    RDSOtherCostsSerializer,
    RDSPropertyInfoSerializer,
    RDSPropertyManagementSerializer,
    RDSPropertyPermitFeesSerializer,
    RDSRemodelCostSerializer,
    RDSRentalIncomeSerializer,
    RDSSummaryTextSerializer,
    RDSTaxesSerializer,
)


class RDSModelSwitch:
    def __init__(self, user, property_id=None):
        self.user = user
        self.property_id = property_id
        self.analyzer_type = 3

    def check_model(self, model):
        default = "wrong_model"

        return getattr(self, str(model), lambda: default)()

    def get_users_property_info(self):
        try:
            property_info = RDSPropertyInfo.objects.filter(
                id=self.property_id, user=self.user
            ).values()
            return property_info

        except RDSPropertyInfo.DoesNotExist:
            raise NoStartedDealRound

    def property_information(self):
        data_dict = {
            "model_name": RDSPropertyInfo,
            "serializer": RDSPropertyInfoSerializer,
        }
        return data_dict

    def finance_options(self):
        data_dict = {
            "model_name": RDSFinanceOptions,
            "serializer": RDSFinanceOptionsSerializer,
        }
        return data_dict

    def property_permit_fees(self):
        data_dict = {
            "model_name": RDSPropertyPermitFees,
            "serializer": RDSPropertyPermitFeesSerializer,
        }
        return data_dict

    def taxes(self):
        data_dict = {
            "model_name": RDSTaxes,
            "serializer": RDSTaxesSerializer,
        }
        return data_dict

    def remodel_cost(self):
        data_dict = {
            "model_name": RDSRemodelCost,
            "serializer": RDSRemodelCostSerializer,
        }
        return data_dict

    def aesthetic_items(self):
        data_dict = {
            "model_name": RDSAestheticItems,
            "serializer": RDSAestheticItemsSerializer,
        }
        return data_dict

    def non_aesthetic_items(self):
        data_dict = {
            "model_name": RDSNonAestheticItems,
            "serializer": RDSNonAestheticItemsSerializer,
        }
        return data_dict

    def additional_remodel_items(self):
        data_dict = {
            "model_name": RDSAdditionRemodelItems,
            "serializer": RDSAdditionRemodelItemsSerializer,
        }
        return data_dict

    def closing_cost(self):
        data_dict = {
            "model_name": RDSClosingCost,
            "serializer": RDSClosingCostSerializer,
        }
        return data_dict

    def rental_income(self):
        data_dict = {
            "model_name": RDSRentalIncome,
            "serializer": RDSRentalIncomeSerializer,
        }
        return data_dict

    def property_management(self):
        data_dict = {
            "model_name": RDSPropertyManagement,
            "serializer": RDSPropertyManagementSerializer,
        }
        return data_dict

    def other_costs(self):
        data_dict = {"model_name": RDSOtherCosts, "serializer": RDSOtherCostsSerializer}
        return data_dict

    def carrying_costs(self):
        data_dict = {
            "model_name": RDSCarryingMonths,
            "serializer": RDSCarryingMonthsSerializer,
        }
        return data_dict

    def est_resale_value(self):
        data_dict = {
            "model_name": RDSEstResaleValue,
            "serializer": RDSEstResaleValueSerializer,
        }
        return data_dict

    def investor_profit(self):

        data_dict = {
            "model_name": RDSInvestorProfit,
            "serializer": RDSInvestorProfitSerializer,
        }
        return data_dict

    def summary_text(self):
        data_dict = {
            "model_name": RDSSummaryText,
            "serializer": RDSSummaryTextSerializer
        }
        return data_dict

    def all_data(self):

        property_info = self.get_users_property_info()

        if property_info:
            property_info_id = property_info[0]["id"]
        else:
            property_info_id = None

        data_dict = {
            "property_info": property_info,
            "finance_options": RDSFinanceOptions.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_permit_fees": RDSPropertyPermitFees.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "taxes": RDSTaxes.objects.filter(property_info=property_info_id).values()
            or None,
            "remodel_cost": {
                "remodel_cost": RDSRemodelCost.objects.filter(
                    property_info=property_info_id
                ).values(),
                "aesthetic_items": RDSAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "non_aesthetic_items": RDSNonAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "additional_remodel_items": RDSAdditionRemodelItems.objects.filter(
                    property_info=property_info_id
                ).values(),
            },
            "closing_cost": RDSClosingCost.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "rental_income": RDSRentalIncome.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_management": RDSPropertyManagement.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "other_costs": RDSOtherCosts.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "carrying_cost": RDSCarryingMonths.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "est_resale_value": RDSEstResaleValue.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "custom_comps": RDSCustomComps.objects.filter(
                property_info=property_info_id, user=self.user,
                analyzer=self.analyzer_type).values(),
            "investor_profit": RDSInvestorProfit.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "summary": RDSSummaryText.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
        }
        return data_dict

    def summary_service(self):
        summary = {}
        try:
            property_info = RDSPropertyInfo.objects.get(
                id=self.property_id, user=self.user
            )
        except RDSPropertyInfo.DoesNotExist:
            raise NoCalculationsForGivenId

        calculations = RemodelDownStudsCalculations(property_info.id, self.user)
        summary = calculations.summary()
        return summary
