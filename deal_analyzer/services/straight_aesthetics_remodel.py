from deal_analyzer.exceptions.errors import NoCalculationsForGivenId, NoStartedDealRound, ResourceNotFound
from deal_analyzer.models.straight_aesthetics_remodel import (
    AdditionRemodelItems,
    AestheticItems,
    CarryingCost,
    ClosingCost,
    CustomComps,
    EstimatedResaleValue,
    FinanceOptions,
    InvestorProfit,
    NonAestheticItems,
    OtherCosts,
    PropertyInfo,
    PropertyManagement,
    PropertyPermitFees,
    RemodelCost,
    RentalIncome,
    SummaryText,
    Taxes,
)
from deal_analyzer.serializers.straight_aesthetics_remodel import (
    AdditionRemodelItemsSerializer,
    AestheticItemsSerializer,
    CarryingCostSerializer,
    ClosingCostSerializer,
    EstimatedResaleValueSerializer,
    FinanceOptionsSerializer,
    InvestorProfitSerializer,
    NonAestheticItemsSerializer,
    OtherCostsSerializer,
    PropertyInfoSerializer,
    PropertyManagementSerializer,
    PropertyPermitFeesSerializer,
    RemodelCostSerializer,
    RentalIncomeSerializer,
    SummaryTextSerializer,
    TaxesSerializer,
)
from deal_analyzer.calculations.straight_aesthetics.main_calculations import StraightAetheticsRemodelCalculations


class ModelSwitch:
    def __init__(self, user, property_id=None):
        self.user = user
        self.property_id = property_id
        self.analyzer_type = 1

    def check_model(self, model):
        default = "wrong_model"

        return getattr(self, str(model), lambda: default)()

    def get_users_property_info(self):
        try:
            property_info = PropertyInfo.objects.filter(id=self.property_id,
                                                        user=self.user
                                                        ).values()
            if not property_info:
                raise ResourceNotFound
            return property_info

        except PropertyInfo.DoesNotExist:
            raise NoStartedDealRound

    def property_information(self):
        data_dict = {
            "model_name": PropertyInfo,
            "serializer": PropertyInfoSerializer,
        }
        return data_dict

    def finance_options(self):
        data_dict = {
            "model_name": FinanceOptions,
            "serializer": FinanceOptionsSerializer,
        }
        return data_dict

    def property_permit_fees(self):
        data_dict = {
            "model_name": PropertyPermitFees,
            "serializer": PropertyPermitFeesSerializer,
        }
        return data_dict

    def taxes(self):
        data_dict = {
            "model_name": Taxes,
            "serializer": TaxesSerializer,
        }
        return data_dict

    def remodel_cost(self):
        data_dict = {
            "model_name": RemodelCost,
            "serializer": RemodelCostSerializer
        }
        return data_dict

    def aesthetic_items(self):
        data_dict = {
            "model_name": AestheticItems,
            "serializer": AestheticItemsSerializer,
        }
        return data_dict

    def non_aesthetic_items(self):
        data_dict = {
            "model_name": NonAestheticItems,
            "serializer": NonAestheticItemsSerializer,
        }
        return data_dict

    def additional_remodel_items(self):
        data_dict = {
            "model_name": AdditionRemodelItems,
            "serializer": AdditionRemodelItemsSerializer,
        }
        return data_dict

    def closing_cost(self):
        data_dict = {
            "model_name": ClosingCost,
            "serializer": ClosingCostSerializer
        }
        return data_dict

    def rental_income(self):
        data_dict = {
            "model_name": RentalIncome,
            "serializer": RentalIncomeSerializer
        }
        return data_dict

    def property_management(self):
        data_dict = {
            "model_name": PropertyManagement,
            "serializer": PropertyManagementSerializer
        }
        return data_dict

    def other_costs(self):
        data_dict = {
            "model_name": OtherCosts,
            "serializer": OtherCostsSerializer
        }
        return data_dict

    def carrying_costs(self):
        data_dict = {
            "model_name": CarryingCost,
            "serializer": CarryingCostSerializer
        }
        return data_dict

    def est_resale_value(self):
        data_dict = {
            "model_name": EstimatedResaleValue,
            "serializer": EstimatedResaleValueSerializer
        }
        return data_dict

    def investor_profit(self):

        data_dict = {
            "model_name": InvestorProfit,
            "serializer": InvestorProfitSerializer,
        }
        return data_dict

    def summary_text(self):
        data_dict = {
            "model_name": SummaryText,
            "serializer": SummaryTextSerializer
        }
        return data_dict

    def all_data(self):

        property_info = self.get_users_property_info()

        if property_info:
            property_info_id = property_info[0]['id']
        else:
            property_info_id = None

        data_dict = {
            "property_info": property_info,
            "finance_options": FinanceOptions.objects.filter(property_info=property_info_id).values() or None,
            "property_permit_fees": PropertyPermitFees.objects.filter(property_info=property_info_id).values() or None,
            "taxes": Taxes.objects.filter(property_info=property_info_id).values() or None,
            "remodel_cost": {
                "remodel_cost": RemodelCost.objects.filter(property_info=property_info_id).values(),
                "aesthetic_items": AestheticItems.objects.filter(property_info=property_info_id).values(),
                "non_aesthetic_items":  NonAestheticItems.objects.filter(property_info=property_info_id).values(),
                "additional_remodel_items":  AdditionRemodelItems.objects.filter(
                                                            property_info=property_info_id).values(),
            },
            "closing_cost": ClosingCost.objects.filter(property_info=property_info_id).values() or None,
            "rental_income": RentalIncome.objects.filter(property_info=property_info_id).values() or None,
            "property_management": PropertyManagement.objects.filter(property_info=property_info_id).values() or None,
            "other_costs": OtherCosts.objects.filter(property_info=property_info_id).values() or None,
            "carrying_cost": CarryingCost.objects.filter(property_info=property_info_id).values() or None,
            "est_resale_value": EstimatedResaleValue.objects.filter(property_info=property_info_id).values() or None,
            "custom_comps": CustomComps.objects.filter(
                                    property_info=property_info_id, user=self.user, analyzer=self.analyzer_type
                                    ).values(),
            "investor_profit": InvestorProfit.objects.filter(property_info=property_info_id).values() or [],
            "summary": SummaryText.objects.filter(property_info=property_info_id).values() or None,
        }
        return data_dict

    def summary_service(self):
        try:
            property_info = PropertyInfo.objects.get(
                id=self.property_id, user=self.user
            )
        except PropertyInfo.DoesNotExist:
            raise NoCalculationsForGivenId

        str_calc = StraightAetheticsRemodelCalculations(property_info.id, self.user)
        summary = str_calc.summary()
        return summary
