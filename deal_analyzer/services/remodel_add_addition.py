from deal_analyzer.calculations.remodel_add_addition.main_calculations import (
    RemodelAddAdditionCalculations,
)

from deal_analyzer.exceptions.errors import (
    NoCalculationsForGivenId,
    NoStartedDealRound,
)
from deal_analyzer.models.remodel_add_addition import (
    RAAAdditionRemodelItems,
    RAAAestheticItems,
    RAACarryingMonths,
    RAAClosingCost,
    RAACustomComps,
    RAAEstResaleValue,
    RAAFinanceOptions,
    RAAInvestorProfit,
    RAANonAestheticItems,
    RAAOtherCosts,
    RAAPropertyInfo,
    RAAPropertyManagement,
    RAAPropertyPermitFees,
    RAARemodelCost,
    RAARentalIncome,
    RAASummaryText,
    RAATaxes,
)
from deal_analyzer.serializers.remodel_add_addition import (
    RAAAdditionRemodelItemsSerializer,
    RAAAestheticItemsSerializer,
    RAACarryingMonthsSerializer,
    RAAClosingCostSerializer,
    RAAEstResaleValueSerializer,
    RAAFinanceOptionsSerializer,
    RAAInvestorProfitSerializer,
    RAANonAestheticItemsSerializer,
    RAAOtherCostsSerializer,
    RAAPropertyInfoSerializer,
    RAAPropertyManagementSerializer,
    RAAPropertyPermitFeesSerializer,
    RAARemodelCostSerializer,
    RAARentalIncomeSerializer,
    RAASummaryTextSerializer,
    RAATaxesSerializer,
)


class RAAModelSwitch:
    def __init__(self, user, property_id=None):
        self.user = user
        self.property_id = property_id
        self.analyzer_type = 2

    def check_model(self, model):
        default = "wrong_model"

        return getattr(self, str(model), lambda: default)()

    def get_users_property_info(self):
        try:
            property_info = RAAPropertyInfo.objects.filter(
                id=self.property_id, user=self.user
            ).values()
            return property_info

        except RAAPropertyInfo.DoesNotExist:
            raise NoStartedDealRound

    def property_information(self):
        data_dict = {
            "model_name": RAAPropertyInfo,
            "serializer": RAAPropertyInfoSerializer,
        }
        return data_dict

    def finance_options(self):
        data_dict = {
            "model_name": RAAFinanceOptions,
            "serializer": RAAFinanceOptionsSerializer,
        }
        return data_dict

    def property_permit_fees(self):
        data_dict = {
            "model_name": RAAPropertyPermitFees,
            "serializer": RAAPropertyPermitFeesSerializer,
        }
        return data_dict

    def taxes(self):
        data_dict = {
            "model_name": RAATaxes,
            "serializer": RAATaxesSerializer,
        }
        return data_dict

    def remodel_cost(self):
        data_dict = {
            "model_name": RAARemodelCost,
            "serializer": RAARemodelCostSerializer,
        }
        return data_dict

    def aesthetic_items(self):
        data_dict = {
            "model_name": RAAAestheticItems,
            "serializer": RAAAestheticItemsSerializer,
        }
        return data_dict

    def non_aesthetic_items(self):
        data_dict = {
            "model_name": RAANonAestheticItems,
            "serializer": RAANonAestheticItemsSerializer,
        }
        return data_dict

    def additional_remodel_items(self):
        data_dict = {
            "model_name": RAAAdditionRemodelItems,
            "serializer": RAAAdditionRemodelItemsSerializer,
        }
        return data_dict

    def closing_cost(self):
        data_dict = {
            "model_name": RAAClosingCost,
            "serializer": RAAClosingCostSerializer,
        }
        return data_dict

    def rental_income(self):
        data_dict = {
            "model_name": RAARentalIncome,
            "serializer": RAARentalIncomeSerializer,
        }
        return data_dict

    def property_management(self):
        data_dict = {
            "model_name": RAAPropertyManagement,
            "serializer": RAAPropertyManagementSerializer,
        }
        return data_dict

    def other_costs(self):
        data_dict = {"model_name": RAAOtherCosts, "serializer": RAAOtherCostsSerializer}
        return data_dict

    def carrying_costs(self):
        data_dict = {
            "model_name": RAACarryingMonths,
            "serializer": RAACarryingMonthsSerializer,
        }
        return data_dict

    def est_resale_value(self):
        data_dict = {
            "model_name": RAAEstResaleValue,
            "serializer": RAAEstResaleValueSerializer,
        }
        return data_dict

    def investor_profit(self):

        data_dict = {
            "model_name": RAAInvestorProfit,
            "serializer": RAAInvestorProfitSerializer,
        }
        return data_dict

    def summary_text(self):
        data_dict = {
            "model_name": RAASummaryText,
            "serializer": RAASummaryTextSerializer
        }
        return data_dict

    def all_data(self):

        property_info = self.get_users_property_info()

        if property_info:
            property_info_id = property_info[0]["id"]
        else:
            property_info_id = None

        data_dict = {
            "property_info": property_info,
            "finance_options": RAAFinanceOptions.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_permit_fees": RAAPropertyPermitFees.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "taxes": RAATaxes.objects.filter(property_info=property_info_id).values()
            or None,
            "remodel_cost": {
                "remodel_cost": RAARemodelCost.objects.filter(
                    property_info=property_info_id
                ).values(),
                "aesthetic_items": RAAAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "non_aesthetic_items": RAANonAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "additional_remodel_items": RAAAdditionRemodelItems.objects.filter(
                    property_info=property_info_id
                ).values(),
            },
            "closing_cost": RAAClosingCost.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "rental_income": RAARentalIncome.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_management": RAAPropertyManagement.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "other_costs": RAAOtherCosts.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "carrying_cost": RAACarryingMonths.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "est_resale_value": RAAEstResaleValue.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "custom_comps": RAACustomComps.objects.filter(
                property_info=property_info_id,
                user=self.user,
                analyzer=self.analyzer_type,
            ).values(),
            "investor_profit": RAAInvestorProfit.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "summary": RAASummaryText.objects.filter(
                property_info=property_info_id
            ).values()
            or None
        }
        return data_dict

    def summary_service(self):
        summary = {}
        try:
            property_info = RAAPropertyInfo.objects.get(
                id=self.property_id, user=self.user
            )
        except RAAPropertyInfo.DoesNotExist:
            raise NoCalculationsForGivenId

        calculations = RemodelAddAdditionCalculations(property_info.id, self.user)
        summary = calculations.summary()
        return summary
