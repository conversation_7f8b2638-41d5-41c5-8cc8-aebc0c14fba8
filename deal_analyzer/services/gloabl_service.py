from deal_analyzer.exceptions.errors import (
    InvalidAnalyzerException,
    InvalidCompsOptionException,
    ResourceNotFound,
)
from deal_analyzer.models.straight_aesthetics_remodel import (
    EstimatedResaleValueComps,
)


def average_values_for_bpohomes_comps(all_ids):
    number_of_comps = len(all_ids)
    square_footage = 0
    lot_size = 0
    purchase_price = 0
    price_psqft = 0

    for uuid in all_ids:

        try:
            record = EstimatedResaleValueComps.objects.get(id=uuid)
        except Exception:
            raise ResourceNotFound

        purchase_price += float(record.price or 0)
        lot_size += float(record.new_lot_size or 0)
        square_footage += float(record.sqft or 0)
        price_psqft += float(record.per_sqft or 0)

    return {
        "average_square_footage": square_footage,
        "average_lot_size": lot_size,
        "average_purchase_price": purchase_price,
        "average_price_psqft": price_psqft,
        "number_of_comps": number_of_comps,
    }


def average_values_for_custom_comps(property_info, user, analyzer, instance_id, custom_comps_model):
    if not analyzer:
        raise InvalidAnalyzerException
    square_footage = 0
    lot_size = 0
    purchase_price = 0
    price_psqft = 0
    try:
        record = custom_comps_model.objects.filter(
            property_info=instance_id, user=user, analyzer=analyzer
        ).values()
    except Exception:
        raise ResourceNotFound

    number_of_comps = len(record)
    for uuid in record:

        purchase_price += float(uuid["price"] or 0)
        lot_size += float(uuid["lot_size"] or 0)
        square_footage += float(uuid["sqft"] or 0)
        price_psqft += float(uuid["per_sqft"] or 0)

    return {
        "average_square_footage": square_footage,
        "average_lot_size": lot_size,
        "average_purchase_price": purchase_price,
        "average_price_psqft": price_psqft,
        "number_of_comps": number_of_comps,
    }


def average_values_of_both(property_info, user, all_ids, analyzer, instance_id, custom_comps_model):
    bpo_comps = average_values_for_bpohomes_comps(all_ids)
    custom_comps = average_values_for_custom_comps(property_info, user, analyzer, instance_id, custom_comps_model)

    return {
        "average_square_footage": bpo_comps["average_square_footage"]
        + custom_comps["average_square_footage"],
        "average_lot_size": bpo_comps["average_lot_size"]
        + custom_comps["average_lot_size"],
        "average_purchase_price": bpo_comps["average_purchase_price"]
        + custom_comps["average_purchase_price"],
        "average_price_psqft": bpo_comps["average_price_psqft"]
        + custom_comps["average_price_psqft"],
        "number_of_comps": bpo_comps["number_of_comps"]
        + custom_comps["number_of_comps"],
    }


def calculate_average_values_for_comps(request, property_info, user, analyzer, instance_id, custom_comps_model):
    all_ids = request.get("ids", [])
    comps_option = request.get("comps_option", 2)

    if comps_option == 1:
        data_values = average_values_for_custom_comps(property_info, user, analyzer, instance_id, custom_comps_model)
    elif comps_option == 2:
        data_values = average_values_for_bpohomes_comps(all_ids)
    elif comps_option == 3:
        data_values = average_values_of_both(property_info, user, all_ids, analyzer, instance_id, custom_comps_model)
    else:
        raise InvalidCompsOptionException

    number_of_comps = data_values["number_of_comps"]
    data = {
        "average_square_footage": round(
            data_values["average_square_footage"] / (number_of_comps or 1), 2
        ),
        "average_lot_size": round(
            data_values["average_lot_size"] / (number_of_comps or 1), 2
        ),
        "average_purchase_price": round(
            data_values["average_purchase_price"] / (number_of_comps or 1), 2
        ),
        "average_price_psqft": round(
            data_values["average_price_psqft"] / (number_of_comps or 1), 2
        ),
        "number_of_comps": number_of_comps,
    }
    return data
