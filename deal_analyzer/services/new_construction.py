from deal_analyzer.calculations.new_construction.main_calculations import (
    NewConstructionCalculations,
)
from deal_analyzer.exceptions.errors import (
    NoCalculationsForGivenId,
    NoStartedDealRound,
)
from deal_analyzer.models.new_construction import (
    NCAdditionRemodelItems,
    NCAestheticItems,
    NCCarryingMonths,
    NCClosingCost,
    NCCustomComps,
    NCDemolishingCost,
    NCDevelopmentCost,
    NCEstResaleValue,
    NCFinanceOptions,
    NCInvestorProfit,
    NCNonAestheticItems,
    NCOtherCosts,
    NCPropertyInfo,
    NCPropertyManagement,
    NCPropertyPermitFees,
    NCRentalIncome,
    NCSummaryText,
    NCTaxes,
)
from deal_analyzer.serializers.new_construction import (
    NCAdditionRemodelItemsSerializer,
    NCAestheticItemsSerializer,
    NCCarryingMonthsSerializer,
    NCClosingCostSerializer,
    NCDemolishingCostSerializer,
    NCDevelopmentCostSerializer,
    NCEstResaleValueSerializer,
    NCFinanceOptionsSerializer,
    NCInvestorProfitSerializer,
    NCNonAestheticItemsSerializer,
    NCOtherCostsSerializer,
    NCPropertyInfoSerializer,
    NCPropertyManagementSerializer,
    NCPropertyPermitFeesSerializer,
    NCRentalIncomeSerializer,
    NCSummaryTextSerializer,
    NCTaxesSerializer,
)


class NCModelSwitch:
    def __init__(self, user, property_id=None):
        self.user = user
        self.property_id = property_id
        self.analyzer_type = 5

    def check_model(self, model):
        default = "wrong_model"

        return getattr(self, str(model), lambda: default)()

    def get_users_property_info(self):
        try:
            property_info = NCPropertyInfo.objects.filter(
                id=self.property_id, user=self.user
            ).values()
            return property_info

        except NCPropertyInfo.DoesNotExist:
            raise NoStartedDealRound

    def property_information(self):
        data_dict = {
            "model_name": NCPropertyInfo,
            "serializer": NCPropertyInfoSerializer,
        }
        return data_dict

    def finance_options(self):
        data_dict = {
            "model_name": NCFinanceOptions,
            "serializer": NCFinanceOptionsSerializer,
        }
        return data_dict

    def property_permit_fees(self):
        data_dict = {
            "model_name": NCPropertyPermitFees,
            "serializer": NCPropertyPermitFeesSerializer,
        }
        return data_dict

    def taxes(self):
        data_dict = {
            "model_name": NCTaxes,
            "serializer": NCTaxesSerializer,
        }
        return data_dict

    def demolishing_cost(self):
        data_dict = {
            "model_name": NCDemolishingCost,
            "serializer": NCDemolishingCostSerializer,
        }
        return data_dict

    def development_cost(self):
        data_dict = {
            "model_name": NCDevelopmentCost,
            "serializer": NCDevelopmentCostSerializer,
        }
        return data_dict

    def aesthetic_items(self):
        data_dict = {
            "model_name": NCAestheticItems,
            "serializer": NCAestheticItemsSerializer,
        }
        return data_dict

    def non_aesthetic_items(self):
        data_dict = {
            "model_name": NCNonAestheticItems,
            "serializer": NCNonAestheticItemsSerializer,
        }
        return data_dict

    def additional_remodel_items(self):
        data_dict = {
            "model_name": NCAdditionRemodelItems,
            "serializer": NCAdditionRemodelItemsSerializer,
        }
        return data_dict

    def closing_cost(self):
        data_dict = {
            "model_name": NCClosingCost,
            "serializer": NCClosingCostSerializer,
        }
        return data_dict

    def rental_income(self):
        data_dict = {
            "model_name": NCRentalIncome,
            "serializer": NCRentalIncomeSerializer,
        }
        return data_dict

    def property_management(self):
        data_dict = {
            "model_name": NCPropertyManagement,
            "serializer": NCPropertyManagementSerializer,
        }
        return data_dict

    def other_costs(self):
        data_dict = {"model_name": NCOtherCosts, "serializer": NCOtherCostsSerializer}
        return data_dict

    def carrying_costs(self):
        data_dict = {
            "model_name": NCCarryingMonths,
            "serializer": NCCarryingMonthsSerializer,
        }
        return data_dict

    def est_resale_value(self):
        data_dict = {
            "model_name": NCEstResaleValue,
            "serializer": NCEstResaleValueSerializer,
        }
        return data_dict

    def investor_profit(self):

        data_dict = {
            "model_name": NCInvestorProfit,
            "serializer": NCInvestorProfitSerializer,
        }
        return data_dict

    def summary_text(self):
        data_dict = {
            "model_name": NCSummaryText,
            "serializer": NCSummaryTextSerializer
        }
        return data_dict

    def all_data(self):

        property_info = self.get_users_property_info()

        if property_info:
            property_info_id = property_info[0]["id"]
        else:
            property_info_id = None

        data_dict = {
            "property_info": property_info,
            "finance_options": NCFinanceOptions.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_permit_fees": NCPropertyPermitFees.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "taxes": NCTaxes.objects.filter(property_info=property_info_id).values()
            or None,
            "demolishing_cost": NCDemolishingCost.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "development_cost": {
                "development_cost": NCDevelopmentCost.objects.filter(
                    property_info=property_info_id
                ).values(),
                "aesthetic_items": NCAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "non_aesthetic_items": NCNonAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "additional_remodel_items": NCAdditionRemodelItems.objects.filter(
                    property_info=property_info_id
                ).values(),
            },
            "closing_cost": NCClosingCost.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "rental_income": NCRentalIncome.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_management": NCPropertyManagement.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "other_costs": NCOtherCosts.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "carrying_cost": NCCarryingMonths.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "est_resale_value": NCEstResaleValue.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "custom_comps": NCCustomComps.objects.filter(
                property_info=property_info_id,
                user=self.user,
                analyzer=self.analyzer_type,
            ).values(),
            "investor_profit": NCInvestorProfit.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "summary": NCSummaryText.objects.filter(
                property_info=property_info_id
            ).values()
            or None
        }
        return data_dict

    def summary_service(self):
        summary = {}
        try:
            property_info = NCPropertyInfo.objects.get(
                id=self.property_id, user=self.user
            )
        except NCPropertyInfo.DoesNotExist:
            raise NoCalculationsForGivenId

        calculations = NewConstructionCalculations(property_info.id, self.user)
        summary = calculations.summary()
        return summary
