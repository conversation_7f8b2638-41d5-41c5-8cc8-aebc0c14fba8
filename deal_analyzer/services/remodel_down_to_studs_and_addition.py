from deal_analyzer.calculations.remodel_down_to_studs_and_addition.main_calculations import (
    RemodelDownStudsAdditionCalculations,
)
from deal_analyzer.exceptions.errors import (
    NoCalculationsForGivenId,
    NoStartedDealRound,
)
from deal_analyzer.models.remodel_down_to_studs_and_addition import (
    RDSAAdditionRemodelItems,
    RDSAAestheticItems,
    RDSACarryingMonths,
    RDSAClosingCost,
    RDSACustomComps,
    RDSAEstResaleValue,
    RDSAFinanceOptions,
    RDSAInvestorProfit,
    RDSANonAestheticItems,
    RDSAOtherCosts,
    RDSAPropertyInfo,
    RDSAPropertyManagement,
    RDSAPropertyPermitFees,
    RDSARemodelCost,
    RDSARentalIncome,
    RDSASummaryText,
    RDSATaxes,
)
from deal_analyzer.serializers.remodel_down_to_studs_and_addition import (
    RDSAAdditionRemodelItemsSerializer,
    RDSAAestheticItemsSerializer,
    RDSACarryingMonthsSerializer,
    RDSAClosingCostSerializer,
    RDSAEstResaleValueSerializer,
    RDSAFinanceOptionsSerializer,
    RDSAInvestorProfitSerializer,
    RDSANonAestheticItemsSerializer,
    RDSAOtherCostsSerializer,
    RDSAPropertyInfoSerializer,
    RDSAPropertyManagementSerializer,
    RDSAPropertyPermitFeesSerializer,
    RDSARemodelCostSerializer,
    RDSARentalIncomeSerializer,
    RDSASummaryTextSerializer,
    RDSATaxesSerializer,
)


class RDSAModelSwitch:
    def __init__(self, user, property_id=None):
        self.user = user
        self.property_id = property_id
        self.analyzer_type = 4

    def check_model(self, model):
        default = "wrong_model"

        return getattr(self, str(model), lambda: default)()

    def get_users_property_info(self):
        try:
            property_info = RDSAPropertyInfo.objects.filter(
                id=self.property_id, user=self.user
            ).values()
            return property_info

        except RDSAPropertyInfo.DoesNotExist:
            raise NoStartedDealRound

    def property_information(self):
        data_dict = {
            "model_name": RDSAPropertyInfo,
            "serializer": RDSAPropertyInfoSerializer,
        }
        return data_dict

    def finance_options(self):
        data_dict = {
            "model_name": RDSAFinanceOptions,
            "serializer": RDSAFinanceOptionsSerializer,
        }
        return data_dict

    def property_permit_fees(self):
        data_dict = {
            "model_name": RDSAPropertyPermitFees,
            "serializer": RDSAPropertyPermitFeesSerializer,
        }
        return data_dict

    def taxes(self):
        data_dict = {
            "model_name": RDSATaxes,
            "serializer": RDSATaxesSerializer,
        }
        return data_dict

    def remodel_cost(self):
        data_dict = {
            "model_name": RDSARemodelCost,
            "serializer": RDSARemodelCostSerializer,
        }
        return data_dict

    def aesthetic_items(self):
        data_dict = {
            "model_name": RDSAAestheticItems,
            "serializer": RDSAAestheticItemsSerializer,
        }
        return data_dict

    def non_aesthetic_items(self):
        data_dict = {
            "model_name": RDSANonAestheticItems,
            "serializer": RDSANonAestheticItemsSerializer,
        }
        return data_dict

    def additional_remodel_items(self):
        data_dict = {
            "model_name": RDSAAdditionRemodelItems,
            "serializer": RDSAAdditionRemodelItemsSerializer,
        }
        return data_dict

    def closing_cost(self):
        data_dict = {
            "model_name": RDSAClosingCost,
            "serializer": RDSAClosingCostSerializer,
        }
        return data_dict

    def rental_income(self):
        data_dict = {
            "model_name": RDSARentalIncome,
            "serializer": RDSARentalIncomeSerializer,
        }
        return data_dict

    def property_management(self):
        data_dict = {
            "model_name": RDSAPropertyManagement,
            "serializer": RDSAPropertyManagementSerializer,
        }
        return data_dict

    def other_costs(self):
        data_dict = {
            "model_name": RDSAOtherCosts,
            "serializer": RDSAOtherCostsSerializer,
        }
        return data_dict

    def carrying_costs(self):
        data_dict = {
            "model_name": RDSACarryingMonths,
            "serializer": RDSACarryingMonthsSerializer,
        }
        return data_dict

    def est_resale_value(self):
        data_dict = {
            "model_name": RDSAEstResaleValue,
            "serializer": RDSAEstResaleValueSerializer,
        }
        return data_dict

    def investor_profit(self):

        data_dict = {
            "model_name": RDSAInvestorProfit,
            "serializer": RDSAInvestorProfitSerializer,
        }
        return data_dict

    def summary_text(self):
        data_dict = {
            "model_name": RDSASummaryText,
            "serializer": RDSASummaryTextSerializer
        }
        return data_dict

    def all_data(self):

        property_info = self.get_users_property_info()

        if property_info:
            property_info_id = property_info[0]["id"]
        else:
            property_info_id = None

        data_dict = {
            "property_info": property_info,
            "finance_options": RDSAFinanceOptions.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_permit_fees": RDSAPropertyPermitFees.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "taxes": RDSATaxes.objects.filter(property_info=property_info_id).values()
            or None,
            "remodel_cost": {
                "remodel_cost": RDSARemodelCost.objects.filter(
                    property_info=property_info_id
                ).values(),
                "aesthetic_items": RDSAAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "non_aesthetic_items": RDSANonAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "additional_remodel_items": RDSAAdditionRemodelItems.objects.filter(
                    property_info=property_info_id
                ).values(),
            },
            "closing_cost": RDSAClosingCost.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "rental_income": RDSARentalIncome.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_management": RDSAPropertyManagement.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "other_costs": RDSAOtherCosts.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "carrying_cost": RDSACarryingMonths.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "est_resale_value": RDSAEstResaleValue.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "custom_comps": RDSACustomComps.objects.filter(
                property_info=property_info_id,
                user=self.user,
                analyzer=self.analyzer_type,
            ).values(),
            "investor_profit": RDSAInvestorProfit.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "summary": RDSASummaryText.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
        }
        return data_dict

    def summary_service(self):
        summary = {}
        try:
            property_info = RDSAPropertyInfo.objects.get(
                id=self.property_id, user=self.user
            )
        except RDSAPropertyInfo.DoesNotExist:
            raise NoCalculationsForGivenId

        calculations = RemodelDownStudsAdditionCalculations(property_info.id, self.user)
        summary = calculations.summary()
        return summary
