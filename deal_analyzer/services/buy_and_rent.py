from deal_analyzer.calculations.buy_and_rent.main_calculations import (
    BuyAndRentCalculations,
)

from deal_analyzer.exceptions.errors import (
    NoCalculationsForGivenId,
    NoStartedDealRound,
)
from deal_analyzer.models.buy_and_rent import (
    BRAdditionRemodelItems,
    BRAestheticItems,
    BRCarryingMonths,
    BRClosingCost,
    BRCustomComps,
    BREstResaleValue,
    BRFinanceOptions,
    BRInvestorProfit,
    BRNonAestheticItems,
    BROtherCosts,
    BRPropertyInfo,
    BRPropertyManagement,
    BRPropertyPermitFees,
    BRRemodelCost,
    BRRentalIncome,
    BRSummaryText,
    BRTaxes,
)
from deal_analyzer.serializers.buy_and_rent import (
    BRAdditionRemodelItemsSerializer,
    BRAestheticItemsSerializer,
    BRCarryingMonthsSerializer,
    BRClosingCostSerializer,
    BREstResaleValueSerializer,
    BRFinanceOptionsSerializer,
    BRInvestorProfitSerializer,
    BRNonAestheticItemsSerializer,
    BROtherCostsSerializer,
    BRPropertyInfoSerializer,
    BRPropertyManagementSerializer,
    BRPropertyPermitFeesSerializer,
    BRRemodelCostSerializer,
    BRRentalIncomeSerializer,
    BRSummaryTextSerializer,
    BRTaxesSerializer,
)


class BRModelSwitch:
    def __init__(self, user, property_id=None):
        self.user = user
        self.property_id = property_id
        self.analyzer_type = 6

    def check_model(self, model):
        default = "wrong_model"

        return getattr(self, str(model), lambda: default)()

    def get_users_property_info(self):
        try:
            property_info = BRPropertyInfo.objects.filter(
                id=self.property_id, user=self.user
            ).values()
            return property_info

        except BRPropertyInfo.DoesNotExist:
            raise NoStartedDealRound

    def property_information(self):
        data_dict = {
            "model_name": BRPropertyInfo,
            "serializer": BRPropertyInfoSerializer,
        }
        return data_dict

    def finance_options(self):
        data_dict = {
            "model_name": BRFinanceOptions,
            "serializer": BRFinanceOptionsSerializer,
        }
        return data_dict

    def property_permit_fees(self):
        data_dict = {
            "model_name": BRPropertyPermitFees,
            "serializer": BRPropertyPermitFeesSerializer,
        }
        return data_dict

    def taxes(self):
        data_dict = {
            "model_name": BRTaxes,
            "serializer": BRTaxesSerializer,
        }
        return data_dict

    def remodel_cost(self):
        data_dict = {
            "model_name": BRRemodelCost,
            "serializer": BRRemodelCostSerializer,
        }
        return data_dict

    def aesthetic_items(self):
        data_dict = {
            "model_name": BRAestheticItems,
            "serializer": BRAestheticItemsSerializer,
        }
        return data_dict

    def non_aesthetic_items(self):
        data_dict = {
            "model_name": BRNonAestheticItems,
            "serializer": BRNonAestheticItemsSerializer,
        }
        return data_dict

    def additional_remodel_items(self):
        data_dict = {
            "model_name": BRAdditionRemodelItems,
            "serializer": BRAdditionRemodelItemsSerializer,
        }
        return data_dict

    def closing_cost(self):
        data_dict = {
            "model_name": BRClosingCost,
            "serializer": BRClosingCostSerializer,
        }
        return data_dict

    def rental_income(self):
        data_dict = {
            "model_name": BRRentalIncome,
            "serializer": BRRentalIncomeSerializer,
        }
        return data_dict

    def property_management(self):
        data_dict = {
            "model_name": BRPropertyManagement,
            "serializer": BRPropertyManagementSerializer,
        }
        return data_dict

    def other_costs(self):
        data_dict = {"model_name": BROtherCosts, "serializer": BROtherCostsSerializer}
        return data_dict

    def carrying_costs(self):
        data_dict = {
            "model_name": BRCarryingMonths,
            "serializer": BRCarryingMonthsSerializer,
        }
        return data_dict

    def est_resale_value(self):
        data_dict = {
            "model_name": BREstResaleValue,
            "serializer": BREstResaleValueSerializer,
        }
        return data_dict

    def investor_profit(self):

        data_dict = {
            "model_name": BRInvestorProfit,
            "serializer": BRInvestorProfitSerializer,
        }
        return data_dict

    def summary_text(self):
        data_dict = {
            "model_name": BRSummaryText,
            "serializer": BRSummaryTextSerializer
        }
        return data_dict

    def all_data(self):

        property_info = self.get_users_property_info()

        if property_info:
            property_info_id = property_info[0]["id"]
        else:
            property_info_id = None

        data_dict = {
            "property_info": property_info,
            "finance_options": BRFinanceOptions.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_permit_fees": BRPropertyPermitFees.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "taxes": BRTaxes.objects.filter(property_info=property_info_id).values()
            or None,
            "remodel_cost": {
                "remodel_cost": BRRemodelCost.objects.filter(
                    property_info=property_info_id
                ).values(),
                "aesthetic_items": BRAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "non_aesthetic_items": BRNonAestheticItems.objects.filter(
                    property_info=property_info_id
                ).values(),
                "additional_remodel_items": BRAdditionRemodelItems.objects.filter(
                    property_info=property_info_id
                ).values(),
            },
            "closing_cost": BRClosingCost.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "rental_income": BRRentalIncome.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "property_management": BRPropertyManagement.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "other_costs": BROtherCosts.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "carrying_cost": BRCarryingMonths.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "est_resale_value": BREstResaleValue.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "custom_comps": BRCustomComps.objects.filter(
                property_info=property_info_id,
                user=self.user,
                analyzer=self.analyzer_type,
            ).values(),
            "investor_profit": BRInvestorProfit.objects.filter(
                property_info=property_info_id
            ).values()
            or None,
            "summary": BRSummaryText.objects.filter(
                property_info=property_info_id
            ).values()
            or None
        }
        return data_dict

    def summary_service(self):
        summary = {}
        try:
            property_info = BRPropertyInfo.objects.get(
                id=self.property_id, user=self.user
            )
        except BRPropertyInfo.DoesNotExist:
            raise NoCalculationsForGivenId

        calculations = BuyAndRentCalculations(property_info.id, self.user)
        summary = calculations.summary()
        return summary
