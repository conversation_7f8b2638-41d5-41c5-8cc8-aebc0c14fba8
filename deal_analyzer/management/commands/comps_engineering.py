from django.core.management.base import BaseCommand

from deal_analyzer.models.straight_aesthetics_remodel import EstimatedResaleValueComps

class Command(BaseCommand):
    help = 'Restructure discrepancies in data'

    def get_all_comps(self, *args, **kwargs):
        comps = EstimatedResaleValueComps.objects.all().order_by('id')
        for comp in comps:
            try:
                if comp.lot_size:
                    split_lot_size = comp.lot_size.split()
                    if split_lot_size[1] == 'AC':
                        new_size = float(43560 * int(float(split_lot_size[0])))
                    elif split_lot_size[1] == 'SF':
                        new_size = split_lot_size[0]
                comp.new_lot_size = new_size
                comp.save()
            except Exception as e:
                pass
        return 1
        
    def handle(self, *args, **kwargs):
        self.get_all_comps()
