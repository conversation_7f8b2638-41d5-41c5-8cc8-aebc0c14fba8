# Generated by Django 3.2.15 on 2023-11-29 16:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0086_investorsession'),
        ('investors', '0022_remove_premieragentinvestor_last_known_address'),
    ]

    operations = [
        migrations.CreateModel(
            name='CsvData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('county_name', models.Char<PERSON>ield(max_length=250, null=True)),
                ('notes', models.TextField(null=True)),
                ('zip', models.CharField(max_length=250, null=True)),
                ('telephone', models.CharField(max_length=250, null=True)),
                ('city', models.CharField(max_length=250, null=True)),
                ('last_known_investment', models.Char<PERSON>ield(max_length=250, null=True)),
                ('state', models.Char<PERSON>ield(max_length=250, null=True)),
                ('investor_name', models.CharField(max_length=250, null=True)),
                ('how_much_liquid_capital', models.FloatField(default=None, null=True)),
                ('use_hard_money_to_fund', models.BooleanField(default=False)),
                ('is_wholesaler', models.BooleanField(default=False)),
                ('investing_years', models.IntegerField(default=None, null=True)),
                ('is_accompanied_with_a_wholesale', models.BooleanField(blank=True, null=True)),
                ('is_finder_fee_buyer_commission', models.BooleanField(blank=True, null=True)),
                ('is_relisted_by_my_brokerage', models.BooleanField(blank=True, null=True)),
                ('phone_number', models.CharField(max_length=250, null=True)),
                ('email', models.CharField(max_length=250, null=True)),
                ('address', models.CharField(max_length=250, null=True)),
                ('source', models.CharField(choices=[('csv import', 'csv import'), ('website', 'website'), ('bpo_list', 'bpo_list')], default='website', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('can_edit', models.BooleanField(default=False)),
                ('agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='register.agent')),
                ('areas_of_focus', models.ManyToManyField(blank=True, to='investors.AreaOfFocusTypes')),
                ('type_of_investments', models.ManyToManyField(to='investors.InvestmentTypes')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
