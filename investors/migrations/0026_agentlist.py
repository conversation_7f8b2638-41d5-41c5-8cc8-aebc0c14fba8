# Generated by Django 3.2.15 on 2025-01-17 21:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0109_stripesubscription_is_social_media'),
        ('investors', '0025_auto_20250117_2140'),
    ]

    operations = [
        migrations.CreateModel(
            name='AgentList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source', models.<PERSON>r<PERSON>ield(choices=[
                 ('csv import', 'csv import'), ('website', 'website'), ('bpo_list', 'bpo_list')], default='csv import', max_length=10)),
                ('full_name', models.CharField(blank=True, max_length=200, null=True)),
                ('email', models.CharField(blank=True, max_length=200, null=True)),
                ('mobile', models.CharField(blank=True, max_length=200, null=True)),
                ('city', models.CharField(blank=True, max_length=200, null=True)),
                ('county', models.CharField(blank=True, max_length=200, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='register.agent')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
