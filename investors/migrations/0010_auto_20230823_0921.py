# Generated by Django 3.2.15 on 2023-08-23 09:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('investors', '0009_remove_premieragentinvestor_area_of_focus'),
    ]

    operations = [
        migrations.CreateModel(
            name='AreaOfFocusTypes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
            ],
        ),
        migrations.AddField(
            model_name='premieragentinvestor',
            name='source',
            field=models.CharField(choices=[('csv import', 'csv import'), ('website', 'website')], default='website', max_length=10),
        ),
        migrations.AddField(
            model_name='premieragentinvestor',
            name='areas_of_focus',
            field=models.ManyToManyField(to='investors.AreaOfFocusTypes'),
        ),
    ]
