from django.core.management.base import BaseCommand
import os
import csv
from bpohomes_backend.settings import BASE_DIR
from datetime import datetime

from investors.models.csv_data import CsvData
from investors.models.premier_agent_investor import AreaOfFocusTypes, InvestmentTypes
from register.utils.investor_list import get_is_hard_money, get_wholesaler


csv_file_path = os.path.join(BASE_DIR, "investors/data", "update1.csv")


def get_gender(data):
    if data == "M":
        return "male"
    else:
        return "female"


class Command(BaseCommand):
    help = "Populates the base table"

    def handle(self, *args, **options):
        self.stdout.write("Starting Command............")
        total_value = 3001
        total_done = 0

        with open(csv_file_path, "r") as file:
            reader = csv.DictReader(file)
            for row in reader:
                self.stdout.write(f"done {total_done} out of {total_value}")
                if CsvData.objects.filter(email=row["email"]).exists():
                    get_investor = CsvData.objects.filter(
                        email=row.get("email")
                    ).first()
                    get_investor.investor_name = row.get("investor_name", None)
                    get_investor.address = row.get("address", None)
                    get_investor.city = row.get("city", None)
                    get_investor.last_known_investment = row.get(
                        "last_known_investment", None
                    )
                    get_investor.state = row.get("state", None)
                    get_investor.zip = row.get("zip", None)
                    get_investor.how_much_liquid_capital = row.get(
                        "how_much_liquid_capital", None
                    )

                    get_investor.phone_number = row.get("telephone", None)
                    get_investor.email = row.get("email", None)
                    get_investor.county_name = row.get("county", None)
                    print(row.get("county", None))
                    get_investor.source = "csv import"
                    get_investor.notes = row.get("notes", None)
                    get_investor.is_wholesaler = get_wholesaler(
                        row.get("is_wholesaler", None)
                    )
                    get_investor.use_hard_money_to_fund = get_is_hard_money(
                        row.get("use_hard_money_to_fund", None)
                    )

                    get_type_lists = []
                    for types in row.get("investor_types", "").split(","):
                        if InvestmentTypes.objects.filter(
                            name__icontains=types
                        ).exists():
                            get_type = (
                                InvestmentTypes.objects.filter(name__icontains=types)
                                .first()
                                .id
                            )
                        else:
                            get_type = InvestmentTypes.objects.create(name=types).id

                        get_type_lists.append(get_type)

                    get_investor.type_of_investments.set(get_type_lists)

                    get_area_focus_lists = []
                    for focus in row.get("area_of_focus", "").split(","):
                        if AreaOfFocusTypes.objects.filter(
                            name__icontains=focus
                        ).exists():
                            get_type = (
                                AreaOfFocusTypes.objects.filter(name__icontains=focus)
                                .first()
                                .id
                            )
                        else:
                            get_type = AreaOfFocusTypes.objects.create(name=focus).id

                        get_area_focus_lists.append(get_type)

                    get_investor.areas_of_focus.set(get_area_focus_lists)

                    get_investor.save()
                    self.stdout.write("Updated")
                else:
                    self.stdout.write("Saved")
                    create_investor = CsvData.objects.create(
                        investor_name=row.get("investor_name", None),
                    )

                    create_investor.investor_name = row.get("investor_name", None)
                    create_investor.city = row.get("city", None)
                    create_investor.last_known_investment = row.get(
                        "last_known_investment", None
                    )
                    create_investor.state = row.get("state", None)
                    create_investor.zip = row.get("zip", None)
                    create_investor.how_much_liquid_capital = row.get(
                        "how_much_liquid_capital", None
                    )
                    create_investor.phone_number = row.get("telephone", None)
                    create_investor.email = row.get("email", None)
                    create_investor.notes = row.get("notes", None)
                    create_investor.source = "csv import"
                    print(row.get("county", None))
                    create_investor.county_name = row.get("county", None)
                    create_investor.is_wholesaler = get_wholesaler(
                        row.get("is_wholesaler", None)
                    )
                    create_investor.use_hard_money_to_fund = get_is_hard_money(
                        row.get("use_hard_money_to_fund", None)
                    )

                    get_type_lists = []
                    for types in row.get("investor_types", "").split(","):
                        if InvestmentTypes.objects.filter(
                            name__icontains=types
                        ).exists():
                            get_type = (
                                InvestmentTypes.objects.filter(name__icontains=types)
                                .first()
                                .id
                            )
                        else:
                            get_type = InvestmentTypes.objects.create(name=types).id

                        get_type_lists.append(get_type)

                    create_investor.type_of_investments.set(get_type_lists)

                    get_area_focus_lists = []
                    for focus in row.get("area_of_focus", "").split(","):
                        if AreaOfFocusTypes.objects.filter(
                            name__icontains=focus
                        ).exists():
                            get_type = (
                                AreaOfFocusTypes.objects.filter(name__icontains=focus)
                                .first()
                                .id
                            )
                        else:
                            get_type = AreaOfFocusTypes.objects.create(name=focus).id

                        get_area_focus_lists.append(get_type)

                    create_investor.areas_of_focus.set(get_area_focus_lists)
                    create_investor.save()
                total_done += 1
