from django.core.management.base import BaseCommand
import os
import csv
from bpohomes_backend.settings import BASE_DIR
from datetime import datetime

from investors.models.csv_data import CsvData
from investors.models.premier_agent_investor import (
    AreaOfFocusTypes,
    InvestmentTypes,
    PremierAgentInvestor,
)
from register.utils.investor_list import get_is_hard_money, get_wholesaler


csv_file_path = os.path.join(BASE_DIR, "investors/data", "update1.csv")


def get_gender(data):
    if data == "M":
        return "male"
    else:
        return "female"


class Command(BaseCommand):
    help = "Populates the base table"

    def handle(self, *args, **options):
        self.stdout.write("Starting Command............")
        total_value = 3001
        total_done = 0

        for row in PremierAgentInvestor.objects.all():
            data = CsvData.objects.filter(email=row.email).first()
            try:
                print(data.email)
                row.phone_number = data.phone_number
                row.save()
            except:
                print("Error")
            total_done += 1
            self.stdout.write("Updated")
            self.stdout.write(f"done {total_done} out of {total_value}")
