from django.db import models
from operations.models import Marketer

# Create your models here.


class PracticeCompensation(models.Model):
    marketer = models.ForeignKey(
        Marketer,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="practicemarketer",
    )
    first_commision = models.FloatField(default=0)
    monthly_commision = models.FloatField(default=0)
    agent = models.ForeignKey(
        "register.Agent",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="practiceagent",
    )
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    subscription = models.OneToOneField(
        "register.StripeSubscription",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="practicesubscription",
    )
    package = models.CharField(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=False)
    package_amount = models.FloatField(default=0)
    data = models.TextField(null=True)


class PracticeAgentCompensation(models.Model):
    agent = models.OneToOneField(
        "register.Agent",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="practiceagentcompensation",
    )
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    amount = models.FloatField(default=0)
    points = models.IntegerField(default=0)


class PracticeAgentCompensationReport(models.Model):
    agent = models.ForeignKey(
        "register.Agent",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="practiceagentcompensationreport",
    )
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    amount = models.FloatField(default=0)
    points = models.IntegerField(default=0)
    is_withdrawn = models.BooleanField(default=False)


class PracticeCompensationBackup(models.Model):
    marketer = models.OneToOneField(
        Marketer, on_delete=models.SET_NULL, null=True, blank=True
    )
    first_commision = models.FloatField(default=0)
    monthly_commision = models.FloatField(default=0)
    date_created = models.DateTimeField(auto_now_add=True, blank=True, null=True)
