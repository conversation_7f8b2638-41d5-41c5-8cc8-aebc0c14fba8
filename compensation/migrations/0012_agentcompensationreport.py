# Generated by Django 3.2.15 on 2022-12-12 16:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0045_agent_brokers_ein'),
        ('compensation', '0011_agentcompensation_points'),
    ]

    operations = [
        migrations.CreateModel(
            name='AgentCompensationReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('amount', models.FloatField(default=0)),
                ('points', models.IntegerField(default=0)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='agentcompensationreport', to='register.agent')),
            ],
        ),
    ]
