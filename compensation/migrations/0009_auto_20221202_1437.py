# Generated by Django 3.2.15 on 2022-12-02 14:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0057_subtarget_package_name'),
        ('register', '0042_agent_ghanaian_marketer'),
        ('compensation', '0008_agentcompensation'),
    ]

    operations = [
        migrations.AlterField(
            model_name='agentcompensation',
            name='agent',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='agentcompensation', to='register.agent'),
        ),
        migrations.CreateModel(
            name='CompensationBackup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_commision', models.FloatField(default=0)),
                ('monthly_commision', models.FloatField(default=0)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('marketer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='operations.marketer')),
            ],
        ),
        migrations.CreateModel(
            name='AgentCompensationBackup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('amount', models.FloatField(default=0)),
                ('agent', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='register.agent')),
            ],
        ),
    ]
