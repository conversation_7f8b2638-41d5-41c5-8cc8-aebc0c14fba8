# Generated by Django 3.2.15 on 2025-06-26 16:22

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0062_alter_task_date_created'),
        ('register', '0112_auto_20250415_2133'),
        ('compensation', '0014_alter_agentcompensation_date_created_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PracticeCompensationBackup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_commision', models.FloatField(default=0)),
                ('monthly_commision', models.FloatField(default=0)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('marketer', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='operations.marketer')),
            ],
        ),
        migrations.CreateModel(
            name='PracticeCompensation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_commision', models.FloatField(default=0)),
                ('monthly_commision', models.FloatField(default=0)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('package', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=False)),
                ('package_amount', models.FloatField(default=0)),
                ('data', models.TextField(null=True)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practiceagent', to='register.agent')),
                ('marketer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practicemarketer', to='operations.marketer')),
                ('subscription', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='practicesubscription', to='register.stripesubscription')),
            ],
        ),
        migrations.CreateModel(
            name='PracticeAgentCompensationReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('amount', models.FloatField(default=0)),
                ('points', models.IntegerField(default=0)),
                ('is_withdrawn', models.BooleanField(default=False)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='practiceagentcompensationreport', to='register.agent')),
            ],
        ),
        migrations.CreateModel(
            name='PracticeAgentCompensation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('amount', models.FloatField(default=0)),
                ('points', models.IntegerField(default=0)),
                ('agent', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='practiceagentcompensation', to='register.agent')),
            ],
        ),
    ]
