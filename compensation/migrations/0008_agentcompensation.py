# Generated by Django 3.2.15 on 2022-11-30 14:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0042_agent_ghanaian_marketer'),
        ('compensation', '0007_compensation_data'),
    ]

    operations = [
        migrations.CreateModel(
            name='AgentCompensation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('amount', models.FloatField(default=0)),
                ('agent', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='agent0compensation', to='register.agent')),
            ],
        ),
    ]
