# Generated by Django 3.2.15 on 2022-11-16 14:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('register', '0041_auto_20221114_1506'),
        ('operations', '0056_task_scheduled_marketer'),
    ]

    operations = [
        migrations.CreateModel(
            name='Compensation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.FloatField(default=0)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('package', models.CharField(max_length=255)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='agent', to='register.agent')),
                ('marketer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='marketer', to='operations.marketer')),
            ],
        ),
    ]
