from django.urls import path
from . import views

urlpatterns = [
    path("get-agent-amount/", views.AgentCompensationView.as_view()),
    path("get-compensation-amount/", views.CompensationAmount.as_view()),
    path(
        "snr-marketers-compensation-view/", views.SnrMargetersCompensationView.as_view()
    ),
    path(
        "snr-marketers-compensation-package-count/",
        views.SnrMarketerCompensationPackageCount.as_view(),
    ),
    path(
        "snr-marketers-compensation-amount-view/",
        views.SnrMargetersCompensationAmountView.as_view(),
    ),
    path("marketers-compensation-view/", views.MargetersCompensationView.as_view()),
    path("operations-compensation-view/", views.OperationsCompensationView.as_view()),
    path(
        "operations-compensation-package-view/",
        views.OperationsCompensationPackageView.as_view(),
    ),
    path(
        "operations-compensation-view-amount/",
        views.OperationsCompensationViewAmount.as_view(),
    ),
    path("compensation-detail/<int:pk>/", views.CompensationDetail.as_view()),
    path("team-view/", views.TeamView.as_view()),
    path("report-view/", views.ReportsOnMarketers.as_view()),
    path("redraw-all-marketers-money/", views.RedrawAllMarketersMoney.as_view()),
    path("redraw-all-agent-money/", views.RedrawAllAgentMoney.as_view()),
    path("redraw-agent-money/", views.RedrawAgentMoney.as_view()),
    path("agent-report/", views.AgentReport.as_view()),
]
