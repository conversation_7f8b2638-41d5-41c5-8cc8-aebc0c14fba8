from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db.models import Sum, Q
from compensation.models import (
    Compensation,
)
from compensation.serializers.compensation import (
    AgentCompensationSerializer,
)


class OperationsCompensationView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        package = self.request.GET.get("package", None)
        is_active = self.request.GET.get("active", None)
        year = self.request.GET.get("year", None)
        month = self.request.GET.get("month", None)
        marketer_category = self.request.GET.get("marketer_category", None)
        # perform filter lookups
        if year:
            if package and is_active:
                first_compensation = Compensation.objects.filter(
                    Q(package=package) & Q(is_active=is_active.capitalize())
                ).filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )

                serializer = AgentCompensationSerializer(first_compensation, many=True)
                return Response(serializer.data)

            elif package:
                first_compensation = Compensation.objects.filter(
                    Q(package=package)
                ).filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )

                serializer = AgentCompensationSerializer(first_compensation, many=True)
                return Response(serializer.data)

            elif is_active:
                first_compensation = Compensation.objects.filter(
                    Q(is_active=is_active.capitalize())
                ).filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )

                serializer = AgentCompensationSerializer(first_compensation, many=True)
                return Response(serializer.data)

            else:
                first_compensation = Compensation.objects.filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )

                serializer = AgentCompensationSerializer(first_compensation, many=True)

                return Response(serializer.data)

        if year and month:
            if package and is_active:
                first_compensation = (
                    Compensation.objects.filter(
                        Q(package=package) & Q(is_active=is_active.capitalize())
                    )
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                )

                serializer = AgentCompensationSerializer(first_compensation, many=True)
                return Response(serializer.data)

            elif package:
                first_compensation = (
                    Compensation.objects.filter(Q(package=package))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                )

                serializer = AgentCompensationSerializer(first_compensation, many=True)
                return Response(serializer.data)

            elif is_active:
                first_compensation = (
                    Compensation.objects.filter(Q(is_active=is_active.capitalize()))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                )

                serializer = AgentCompensationSerializer(first_compensation, many=True)
                return Response(serializer.data)

            else:
                first_compensation = Compensation.objects.filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                ).filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )

                serializer = AgentCompensationSerializer(first_compensation, many=True)
                return Response(serializer.data)

        elif marketer_category:
            first_compensation = Compensation.objects.filter(
                Q(is_active=True, marketer__category=marketer_category)
            )

            serializer = AgentCompensationSerializer(first_compensation, many=True)
            return Response(serializer.data)

        else:
            first_compensation = Compensation.objects.filter(Q(is_active=True))

            serializer = AgentCompensationSerializer(first_compensation, many=True)
            return Response(serializer.data)


class OperationsCompensationViewAmount(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        package = self.request.GET.get("package", None)
        is_active = self.request.GET.get("active", None)
        year = self.request.GET.get("year", None)
        month = self.request.GET.get("month", None)
        # perform filter lookups
        if year:
            if package and is_active:
                package_amount = (
                    Compensation.objects.filter(
                        Q(package=package) & Q(is_active=is_active.capitalize())
                    )
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .aggregate(Sum("package_amount"))
                )

                sum_of_package_amount = package_amount["package_amount__sum"]

                number_of_subscription = (
                    Compensation.objects.filter(
                        Q(package=package) & Q(is_active=is_active.capitalize())
                    )
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .count()
                )

                return Response(
                    {
                        "package_amount": sum_of_package_amount,
                        "number_of_subscription": number_of_subscription,
                    },
                    status=status.HTTP_200_OK,
                )

            elif package:
                package_amount = (
                    Compensation.objects.filter(Q(package=package))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .aggregate(Sum("package_amount"))
                )

                sum_of_package_amount = package_amount["package_amount__sum"]

                number_of_subscription = (
                    Compensation.objects.filter(Q(package=package))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .count()
                )

                return Response(
                    {
                        "package_amount": sum_of_package_amount,
                        "number_of_subscription": number_of_subscription,
                    },
                    status=status.HTTP_200_OK,
                )

            elif is_active:
                package_amount = (
                    Compensation.objects.filter(Q(is_active=is_active.capitalize()))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .aggregate(Sum("package_amount"))
                )

                sum_of_package_amount = package_amount["package_amount__sum"]

                number_of_subscription = (
                    Compensation.objects.filter(Q(is_active=is_active.capitalize()))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .count()
                )

                return Response(
                    {
                        "package_amount": sum_of_package_amount,
                        "number_of_subscription": number_of_subscription,
                    },
                    status=status.HTTP_200_OK,
                )

            else:
                package_amount = Compensation.objects.filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                ).aggregate(Sum("package_amount"))

                sum_of_package_amount = package_amount["package_amount__sum"]

                number_of_subscription = Compensation.objects.filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                ).count()

                return Response(
                    {
                        "package_amount": sum_of_package_amount,
                        "number_of_subscription": number_of_subscription,
                    },
                    status=status.HTTP_200_OK,
                )

        if year and month:
            if package and is_active:
                package_amount = (
                    Compensation.objects.filter(
                        Q(package=package) & Q(is_active=is_active.capitalize())
                    )
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                    .aggregate(Sum("package_amount"))
                )

                sum_of_package_amount = package_amount["package_amount__sum"]

                number_of_subscription = (
                    Compensation.objects.filter(
                        Q(package=package) & Q(is_active=is_active.capitalize())
                    )
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                    .count()
                )

                return Response(
                    {
                        "package_amount": sum_of_package_amount,
                        "number_of_subscription": number_of_subscription,
                    },
                    status=status.HTTP_200_OK,
                )

            elif package:
                package_amount = (
                    Compensation.objects.filter(Q(package=package))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                    .aggregate(Sum("package_amount"))
                )

                sum_of_package_amount = package_amount["package_amount__sum"]

                number_of_subscription = (
                    Compensation.objects.filter(Q(package=package))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                    .count()
                )

                return Response(
                    {
                        "package_amount": sum_of_package_amount,
                        "number_of_subscription": number_of_subscription,
                    },
                    status=status.HTTP_200_OK,
                )

            elif is_active:
                package_amount = (
                    Compensation.objects.filter(Q(is_active=is_active.capitalize()))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                    .aggregate(Sum("package_amount"))
                )

                sum_of_package_amount = package_amount["package_amount__sum"]

                number_of_subscription = (
                    Compensation.objects.filter(Q(is_active=is_active.capitalize()))
                    .filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                    .count()
                )

                return Response(
                    {
                        "package_amount": sum_of_package_amount,
                        "number_of_subscription": number_of_subscription,
                    },
                    status=status.HTTP_200_OK,
                )

            else:
                package_amount = (
                    Compensation.objects.filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                    .aggregate(Sum("package_amount"))
                )

                sum_of_package_amount = package_amount["package_amount__sum"]

                number_of_subscription = (
                    Compensation.objects.filter(
                        Q(subscription__current_period_start__year__lte=year)
                        & Q(subscription__current_period_end__year__gte=year)
                    )
                    .filter(
                        Q(subscription__current_period_start__month__lte=month)
                        & Q(subscription__current_period_end__month__gte=month)
                    )
                    .count()
                )

                return Response(
                    {
                        "package_amount": sum_of_package_amount,
                        "number_of_subscription": number_of_subscription,
                    },
                    status=status.HTTP_200_OK,
                )


class OperationsCompensationPackageView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        year = self.request.GET.get("year", None)
        month = self.request.GET.get("month", None)
        # perform filter lookups
        if year:
            bpo_farm = (
                Compensation.objects.filter(Q(is_active=True) & Q(package="bpo-farm"))
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            premier_agent_website = (
                Compensation.objects.filter(
                    Q(is_active=True) & Q(package="premier-agent-website")
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            deal_analyzer = (
                Compensation.objects.filter(
                    Q(is_active=True) & Q(package="deal-analyzer")
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            deal_analyzer_unlimited = (
                Compensation.objects.filter(
                    Q(is_active=True) & Q(package="deal-analyzer-unlimited")
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            combo_package = (
                Compensation.objects.filter(
                    Q(is_active=True) & Q(package="combo-package")
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            package_amount = (
                Compensation.objects.filter(Q(is_active=True))
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .aggregate(Sum("package_amount"))
            )

            sum_of_package_amount = package_amount["package_amount__sum"]

        if year and month:
            print("\n\n\n\n\n")

            bpo_farm = (
                Compensation.objects.filter(Q(is_active=True) & Q(package="bpo-farm"))
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            premier_agent_website = (
                Compensation.objects.filter(
                    Q(is_active=True) & Q(package="premier-agent-website")
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            deal_analyzer = (
                Compensation.objects.filter(
                    Q(is_active=True) & Q(package="deal-analyzer")
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            deal_analyzer_unlimited = (
                Compensation.objects.filter(
                    Q(is_active=True) & Q(package="deal-analyzer-unlimited")
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            combo_package = (
                Compensation.objects.filter(
                    Q(is_active=True) & Q(package="combo-package")
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            package_amount = (
                Compensation.objects.filter(Q(is_active=True))
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .aggregate(Sum("package_amount"))
            )

            sum_of_package_amount = package_amount["package_amount__sum"]

        return Response(
            {
                "bpo_farm": bpo_farm,
                "premier_agent_website": premier_agent_website,
                "deal_analyzer": deal_analyzer,
                "deal_analyzer_unlimited": deal_analyzer_unlimited,
                "combo_package": combo_package,
                "package_amount": sum_of_package_amount,
            },
            status=status.HTTP_200_OK,
        )
