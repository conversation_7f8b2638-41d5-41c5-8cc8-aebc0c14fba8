from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db.models import Sum
from compensation.models import (
    Compensation,
    AgentCompensation,
)


class AgentCompensationView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        agent_id = self.request.GET.get("agent_id", None)

        # perform filter lookups
        if agent_id:
            try:
                agent_amount = AgentCompensation.objects.get(agent_id=agent_id)

                return Response(
                    {
                        "agent_amount": agent_amount.amount,
                    },
                    status=status.HTTP_200_OK,
                )
            except Exception:
                return Response(
                    {
                        "agent_amount": 0,
                    },
                    status=status.HTTP_200_OK,
                )

        return Response(
            {"detail": "agent id needed"},
            status=status.HTTP_404_NOT_FOUND,
        )


class CompensationAmount(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        marketer_id = self.request.GET.get("marketer_id", None)

        # perform filter lookups
        if marketer_id:
            first_compensation = Compensation.objects.filter(
                marketer_id=marketer_id, is_active=True
            ).aggregate(Sum("first_commision"))
            sum_of_first_compensation = first_compensation["first_commision__sum"]

            first_compensation_count = Compensation.objects.filter(
                marketer_id=marketer_id, is_active=True
            )
            first_compensation_counter = 0
            for i in first_compensation_count:
                if i.first_commision == 0:
                    pass
                else:
                    first_compensation_counter = first_compensation_counter + 1

            monthly_commision = Compensation.objects.filter(
                marketer_id=marketer_id, is_active=True
            ).aggregate(Sum("monthly_commision"))
            sum_of_monthly_commision = monthly_commision["monthly_commision__sum"]
            monthly_commision_count = Compensation.objects.filter(
                marketer_id=marketer_id, is_active=True
            ).count()

            return Response(
                {
                    "sum_of_first_compensation": sum_of_first_compensation,
                    "sum_of_monthly_commision": sum_of_monthly_commision,
                    "first_compensation_count": first_compensation_counter,
                    "monthly_commision_count": monthly_commision_count,
                },
                status=status.HTTP_200_OK,
            )

        return Response(
            {},
            status=status.HTTP_404_NOT_FOUND,
        )
