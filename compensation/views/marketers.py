from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.db.models import Sum, Q
from compensation.models import (
    Compensation,
)
from compensation.serializers.compensation import (
    AgentCompensationSerializer,
)


class SnrMargetersCompensationView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        snr_marketer_id = self.request.GET.get("snr_marketer_id", None)
        package = self.request.GET.get("package", None)

        # perform filter lookups
        if snr_marketer_id and package:
            first_compensation = Compensation.objects.filter(
                marketer__team__snr_marketer_id=snr_marketer_id,
                package=package,
                is_active=True,
            )

            serializer = AgentCompensationSerializer(first_compensation, many=True)
            return Response(serializer.data)

        if snr_marketer_id:
            first_compensation = Compensation.objects.filter(
                marketer__team__snr_marketer_id=snr_marketer_id, is_active=True
            )
            serializer = AgentCompensationSerializer(first_compensation, many=True)
            return Response(serializer.data)

        return Response(
            {"data": "snr_marketer_id is empty"},
            status=status.HTTP_404_NOT_FOUND,
        )


class SnrMarketerCompensationPackageCount(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        year = self.request.GET.get("year", None)
        month = self.request.GET.get("month", None)
        snr_marketer_id = self.request.GET.get("snr_marketer_id", None)
        # perform filter lookups
        if year:
            bpo_farm = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="bpo-farm")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            premier_agent_website = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="premier-agent-website")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            deal_analyzer = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="deal-analyzer")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            deal_analyzer_unlimited = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="deal-analyzer-unlimited")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            combo_package = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="combo-package")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .count()
            )

            package_amount = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .aggregate(Sum("package_amount"))
            )

            sum_of_package_amount = package_amount["package_amount__sum"]

        if year and month:
            print("\n\n\n\n\n")

            bpo_farm = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="bpo-farm")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            premier_agent_website = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="premier-agent-website")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            deal_analyzer = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="deal-analyzer")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            deal_analyzer_unlimited = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="deal-analyzer-unlimited")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            combo_package = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(package="combo-package")
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .count()
            )

            package_amount = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .aggregate(Sum("package_amount"))
            )

            sum_of_package_amount = package_amount["package_amount__sum"]

        return Response(
            {
                "bpo_farm": bpo_farm,
                "premier_agent_website": premier_agent_website,
                "deal_analyzer": deal_analyzer,
                "deal_analyzer_unlimited": deal_analyzer_unlimited,
                "combo_package": combo_package,
                "package_amount": sum_of_package_amount,
            },
            status=status.HTTP_200_OK,
        )


class SnrMargetersCompensationAmountView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        year = self.request.GET.get("year", None)
        month = self.request.GET.get("month", None)
        snr_marketer_id = self.request.GET.get("snr_marketer_id", None)

        # perform filter lookups
        if year:
            package_amount = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .aggregate(Sum("package_amount"))
            )

            sum_of_package_amount = package_amount["package_amount__sum"]

        elif year and month:
            package_amount = (
                Compensation.objects.filter(
                    Q(is_active=True)
                    & Q(marketer__team__snr_marketer_id=snr_marketer_id)
                )
                .filter(
                    Q(subscription__current_period_start__year__lte=year)
                    & Q(subscription__current_period_end__year__gte=year)
                )
                .filter(
                    Q(subscription__current_period_start__month__lte=month)
                    & Q(subscription__current_period_end__month__gte=month)
                )
                .aggregate(Sum("package_amount"))
            )

            sum_of_package_amount = package_amount["package_amount__sum"]

        else:
            sum_of_package_amount = "cannot pass in only snr Marketer id you need to add a year or year and month"

        return Response(
            {
                "package_amount": sum_of_package_amount,
            },
            status=status.HTTP_200_OK,
        )


class MargetersCompensationView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        marketer_id = self.request.GET.get("marketer_id", None)

        # perform filter lookups
        if marketer_id:
            first_compensation = Compensation.objects.filter(
                marketer_id=marketer_id, is_active=True
            )

            serializer = AgentCompensationSerializer(first_compensation, many=True)
            return Response(serializer.data)

        return Response(
            {"data": "marketer id is needed"},
            status=status.HTTP_404_NOT_FOUND,
        )
