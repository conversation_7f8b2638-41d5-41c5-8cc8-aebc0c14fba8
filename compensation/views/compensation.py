from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.http import Http404
from compensation.serializers.compensation import (
    CompensationAgentCreateReportSerialiser,
    CompensationAgentReportSerialiser,
    CompensationSerializer,
    TeamSerializer
)
from compensation.serializers.defaults import (
    ReportMarketerListRetrieveSerializer,
)
from compensation.models import (
    Compensation,
    AgentCompensation,
    CompensationBackup,
    AgentCompensationReport,
)
from compensation.tasks import send_operations_email_task
from operations.models import MarketingTeam, Marketer


class CompensationDetail(APIView):
    permission_classes = [IsAuthenticated]

    def get_object(self, pk):
        try:
            return Compensation.objects.get(pk=pk)
        except Compensation.DoesNotExist:
            raise Http404

    def get(self, request, pk, format=None):
        get_compensation = self.get_object(pk)
        serializer = CompensationSerializer(get_compensation, many=False)
        return Response(serializer.data)


class TeamView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        get_all_team = MarketingTeam.objects.all()

        serializer = TeamSerializer(get_all_team, many=True)
        return Response(serializer.data)


class ReportsOnMarketers(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        marketer_category = self.request.GET.get("marketer_category", None)

        if marketer_category:
            get_all_marketers = Marketer.objects.filter(category=marketer_category)

            serializer = ReportMarketerListRetrieveSerializer(
                get_all_marketers, many=True
            )
            return Response(serializer.data)

        else:
            get_all_marketers = Marketer.objects.all()

            serializer = ReportMarketerListRetrieveSerializer(
                get_all_marketers, many=True
            )
            return Response(serializer.data)


class RedrawAllMarketersMoney(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, request):
        get_marketer_category = request.data.get("marketer_category", None)

        for payment in Compensation.objects.filter(
            is_active=True, marketer__category=get_marketer_category
        ):
            try:
                get_marketer = CompensationBackup.objects.get(marketer=payment.marketer)
                get_marketer.first_commision = (
                    get_marketer.first_commision + payment.first_commision
                )
                get_marketer.monthly_commision = (
                    get_marketer.monthly_commision + payment.monthly_commision
                )
            except CompensationBackup.DoesNotExist:
                create_agent_compensation = CompensationBackup.objects.create(
                    marketer=payment.marketer,
                    first_commision=payment.first_commision,
                    monthly_commision=payment.monthly_commision,
                )
                print(create_agent_compensation)

            payment.first_commision = 0
            payment.monthly_commision = 0
            payment.save()

        return Response(
            {"data": "Amount has been successfully redrawn"},
            status=status.HTTP_200_OK,
        )


class AgentReport(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, request, *args, **kwargs):
        if AgentCompensationReport.objects.filter(
            agent_id=request.data["agent"], is_withdrawn=False
        ).exists():
            return Response(
                {
                    "detail": "User has already made a withdrawal request",
                },
                status=status.HTTP_200_OK,
            )

        else:
            serializer = CompensationAgentCreateReportSerialiser(data=request.data)

            if serializer.is_valid():
                amount_save = serializer.save()
                send_operations_email_task.delay(
                    agent_name=amount_save.user.first_name,
                    amount=amount_save.amount,
                    email=amount_save.user.email,
                    
                )
                return Response(
                    {
                        "detail": "withdrawal request was successful",
                    },
                    status=status.HTTP_200_OK,
                )

            return Response(
                {
                    "detail": "there was an error",
                    "data": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get(self, *args, **kwargs):
        get_all_agent = AgentCompensationReport.objects.filter(is_withdrawn=False)

        serializer = CompensationAgentReportSerialiser(get_all_agent, many=True)
        return Response(serializer.data)


class RedrawAllAgentMoney(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, request):
        for agent in AgentCompensationReport.objects.filter(is_withdrawn=False):
            try:
                get_agent_compensation = AgentCompensation.objects.get(
                    agent=agent.agent
                )
                get_agent_compensation.amount = (
                    get_agent_compensation.amount - agent.amount
                )
                get_agent_compensation.save()
                agent.is_withdrawn = True
                agent.save()
            except Exception:
                pass

        return Response(
            {"data": "Amount has been successfully redrawn"},
            status=status.HTTP_200_OK,
        )


class RedrawAgentMoney(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, request):
        request_id = request.data.get("request_id", None)
        try:
            get_report = AgentCompensationReport.objects.get(pk=request_id)
            get_agent_compensation = AgentCompensation.objects.get(
                agent=get_report.agent
            )
            get_agent_compensation.amount = (
                get_agent_compensation.amount - get_report.amount
            )
            get_agent_compensation.save()
            get_report.is_withdrawn = True
            get_report.save()

            return Response(
                {"data": "Amount has been successfully redrawn"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"data": str(e)},
                status=status.HTTP_404_NOT_FOUND,
            )
