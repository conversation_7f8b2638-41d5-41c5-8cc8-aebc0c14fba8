from compensation.serializers.defaults import (
    AgentViewSerializer,
    StripeSubscriptionSerializer,
)
from core.serializers import UserSerializer
from rest_framework import serializers

# from .signals import subscription_created
from compensation.models import Compensation, AgentCompensationReport

from register.models import Agent
from operations.serializers import SubTargetSerializer

from operations.models import Marketer, MarketingTeam, SalesTarget
from django.db.models import Sum


class CompensationSalesTargetSerializer(serializers.ModelSerializer):
    sub_targets = SubTargetSerializer(many=True)

    class Meta:
        model = SalesTarget
        fields = [
            "id",
            "title",
            "owner",
            "period",
            "date_due",
            "amount",
            "amount_per_team",
            "sub_targets",
        ]


class CompensationMarketerListRetrieveSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    team = serializers.SerializerMethodField()

    class Meta:
        model = Marketer
        fields = [
            "id",
            "corporate_id",
            "phone",
            "address",
            "hiring_date",
            "profile_image",
            "team_id",
            "team",
            "affiliate_link",
            "user",
            "offices",
            "tasks",
            "category",
        ]
        read_only_fields = ("user", "offices", "tasks")

    def get_team(self, obj):
        try:
            return obj.team.name
        except Exception:
            return None


class CompensationAgentCreateReportSerialiser(serializers.ModelSerializer):
    class Meta:
        model = AgentCompensationReport
        fields = ["id", "agent", "points", "amount", "date_created"]


class CompensationAgentReportSerialiser(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField()

    class Meta:
        model = AgentCompensationReport
        fields = ["id", "agent", "points", "amount", "date_created"]

    def get_agent(self, obj):
        try:
            get_agent = Agent.objects.get(id=obj.agent.id)
            serializer = AgentViewSerializer(get_agent, many=False)
            return serializer.data
        except Exception:
            return None


class AgentCompensationSerializer(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField(read_only=True)
    subscription = StripeSubscriptionSerializer()
    marketer = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Compensation
        fields = [
            "id",
            "package",
            "agent",
            "marketer",
            "subscription",
            "first_commision",
            "monthly_commision",
        ]

    def get_agent(self, obj):
        try:
            get_agent = Agent.objects.get(id=obj.agent.id)
            print(get_agent)
            serializer = AgentViewSerializer(get_agent, many=False)
            return serializer.data
        except Exception:
            return None

    def get_marketer(self, obj):
        try:
            get_marketer = Marketer.objects.get(id=obj.marketer.id)
            serializer = CompensationMarketerListRetrieveSerializer(
                get_marketer, many=False
            )
            return serializer.data
        except Exception:
            return None


class CompensationSerializer(serializers.ModelSerializer):
    subscription = StripeSubscriptionSerializer()
    agent = serializers.SerializerMethodField(read_only=True)
    marketer = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Compensation
        fields = [
            "marketer",
            "first_commision",
            "monthly_commision",
            "agent",
            "date_created",
            "subscription",
            "package",
        ]

    def get_agent(self, obj):
        try:
            get_agent = Agent.objects.get(id=obj.agent.id)
            serializer = AgentViewSerializer(get_agent, many=False)
            return serializer.data
        except Exception:
            return None

    def get_marketer(self, obj):
        try:
            get_marketer = Marketer.objects.get(id=obj.marketer.id)
            serializer = CompensationMarketerListRetrieveSerializer(
                get_marketer, many=False
            )
            return serializer.data
        except Exception:
            return None


class TeamSerializer(serializers.ModelSerializer):
    sales_target = serializers.SerializerMethodField(read_only=True)
    total_amount = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = MarketingTeam
        fields = [
            "id",
            "name",
            "members",
            "snr_marketer",
            "sales_target",
            "total_amount",
        ]

    def get_total_amount(self, obj):
        try:
            package_amount = Compensation.objects.filter(
                marketer__team_id=obj.id, is_active=True
            ).aggregate(Sum("package_amount"))
            sum_of_package_amount = package_amount["package_amount__sum"]

            return sum_of_package_amount
        except Exception:
            return None

    def get_sales_target(self, obj):
        try:
            get_sales = SalesTarget.objects.filter(id=obj.sales_target.id)
            serializer = CompensationSalesTargetSerializer(get_sales, many=True)
            return serializer.data
        except Exception:
            return None
