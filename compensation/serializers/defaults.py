from core.serializers import UserSerializer
from rest_framework import serializers
from django.db.models import Sum

# from .signals import subscription_created
from compensation.models import Compensation

from register.models import Agent, StripeSubscription

from operations.models import Marketer


class AgentViewSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = Agent
        fields = [
            "id",
            "user",
            "brokerage_name",
            "brokerage_address",
            "brokerage_phone",
            "brokers_ein",
        ]


class StripeSubscriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = StripeSubscription
        fields = [
            "id",
            "current_period_start",
        ]


class ReportMarketerListRetrieveSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    team = serializers.SerializerMethodField()
    first_commision = serializers.SerializerMethodField()
    monthly_commision = serializers.SerializerMethodField()
    team = serializers.SerializerMethodField()

    class Meta:
        model = Marketer
        fields = [
            "id",
            "profile_image",
            "team_id",
            "team",
            "user",
            "category",
            "first_commision",
            "monthly_commision",
        ]

    def get_team(self, obj):
        try:
            return obj.team.name
        except Exception:
            return None

    def get_first_commision(self, obj):
        try:
            first_commision = Compensation.objects.filter(
                marketer_id=obj.id, is_active=True
            ).aggregate(Sum("first_commision"))
            sum_of_first_commision = first_commision["first_commision__sum"]

            return sum_of_first_commision
        except Exception:
            return None

    def get_monthly_commision(self, obj):
        monthly_commision = Compensation.objects.filter(
            marketer_id=obj.id, is_active=True
        ).aggregate(Sum("monthly_commision"))
        sum_of_monthly_commision = monthly_commision["monthly_commision__sum"]

        return sum_of_monthly_commision
