import logging
from django.core.mail import BadHeaderError
from templated_mail.mail import BaseEmailMessage

logger = logging.getLogger("aws_logger")


def send_operations_email(agent_name, amount, email):
    header = "New lead from Contact form"

    context = {
        "header": header,
        "amount": amount,
        "agent_name": agent_name,
    }

    recipient = email
    try:
        message = BaseEmailMessage(
            template_name="emails/contact_agent.html", context=context
        )
        message.send([recipient])
    except BadHeaderError:
        pass
