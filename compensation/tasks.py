from __future__ import absolute_import, unicode_literals
from celery import shared_task
from celery.utils.log import get_task_logger
from compensation.emails import send_operations_email
from register.models import StripeSubscription, Agent
from .models import Compensation


logger = get_task_logger(__name__)


@shared_task()
def check_monthly_marketer_payment():
    for active in StripeSubscription.objects.filter(
        interval="yearly", status="past_due"
    ):
        get_agent = Agent.objects.get(user=active.user)
        if active.package == "bpo-farm":
            if get_agent.marketer.category == "us_agent":
                get_compensation = Compensation.objects.filter(agent=get_agent).first()
                get_compensation.monthly_commision += 20
                get_compensation.save()

        if (
            active.package == "premier-agent-website"
            or active.package == "combo-package"
            or active.package == "deal-analyzer-unlimited"
            or active.package == "deal-analyzer"
        ):
            if get_agent.marketer.category == "us_agent":
                get_agent = Agent.objects.get(user=active.user)
                get_compensation = Compensation.objects.filter(agent=get_agent).first()
                get_compensation.monthly_commision += 20
                get_compensation.save()


@shared_task()
def send_operations_email_task(agent_name, amount, email):
    logger.info("Sending send_operations_email")
    return send_operations_email(agent_name, amount, email)
