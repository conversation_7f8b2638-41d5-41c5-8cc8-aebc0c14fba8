from __future__ import absolute_import, unicode_literals
from bpo_listings.es_search.search_data.generate_query import generate_search_query
from celery import shared_task
from celery.utils.log import get_task_logger
from bpo_listings import MLS_INDEX_NAME, SOLD_MLS_INDEX_NAME
from bpo_listings.es_search.search_data.search import search_data
from bpo_listings.es_search import es


logger = get_task_logger(__name__)


@shared_task()
def delete_sold_listings_in_mls():
    query = {"status": "sold"}
    get_params = generate_search_query(query, 1, None)
    search = search_data(MLS_INDEX_NAME, get_params, 50, 1)
    for obj in search["hits"]["hits"]:
        response = es.delete(index=MLS_INDEX_NAME, id=obj["_id"])
        logger.info(response["result"])


@shared_task()
def delete_active_listings_in_sold_mls():
    query = {"status": "active"}
    get_params = generate_search_query(query, 1, None)
    search = search_data(SOLD_MLS_INDEX_NAME, get_params, 50, 1)
    for obj in search["hits"]["hits"]:
        response = es.delete(index=SOLD_MLS_INDEX_NAME, id=obj["_id"])
        logger.info(response["result"])
