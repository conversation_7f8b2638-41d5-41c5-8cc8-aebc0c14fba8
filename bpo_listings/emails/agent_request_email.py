import logging
from django.core.mail import BadHeaderError
from templated_mail.mail import BaseEmailMessage
from rest_framework import status
from rest_framework.response import Response

from bpo_listings.models.claim_listing import ClaimListing

logger = logging.getLogger("aws_logger")

# Function to send an email to the agent when a user requests a property
def send_agent_request_email(
    claim_listing_id, name, email, subject, from_email
    ):
    """
    Send an email to the agent when a user requests a property.
    """
    claim_listing = ClaimListing.objects.get(id=claim_listing_id)
    context = {
        "name": name,
        "email": email,
        "subject": subject,
        "from_email": from_email,
        'listing_id': claim_listing.listing_id,
        'listing_photo': claim_listing.listing_photo,
        'listing_address': claim_listing.listing_address,
        'claimed_status': claim_listing.claimed,
        'listing_url': claim_listing.listing_url,
        'offer_date_available': claim_listing.offer_date_available,
        'give_authorization': claim_listing.give_authorization,
        'seller_view_offer': claim_listing.seller_view_offer,
        'display_offer_review_status': claim_listing.display_offer_review_status,
        'request_review_duration': claim_listing.request_review_duration,
        'hours_days': claim_listing.hours_days,
        'display_duration_response': claim_listing.display_duration_response,
        'review_offer_date': claim_listing.review_offer_date,
        'commission_available': claim_listing.commission_available,
        'email_commission_rate': claim_listing.email_commission_rate,
        'pay_buyer_broker': claim_listing.pay_buyer_broker,
        'amount_percentage': claim_listing.amount_percentage,
        'total_commission_rate': claim_listing.total_commission_rate,
        'total_commission': claim_listing.total_commission,
        'lock_box_available': claim_listing.lock_box_available,
        'lock_box_type': claim_listing.lock_box_type,
        'lock_box_location': claim_listing.lock_box_location,
        'private_notes': claim_listing.private_notes
    }
    recipient = [email]
    context = {
        "name": name,
        "email": email,
        "subject": subject,
        "from_email": from_email,
    }

    recipient = [email]

    try:
        message = BaseEmailMessage(
            template_name="emails/agent_request.html", context=context
        )
        message.send(recipient)
        logger.info(f"Email sent to {email} successfully.")
        return True
    except BadHeaderError:
        return False