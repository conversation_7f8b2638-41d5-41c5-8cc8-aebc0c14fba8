from django.db import models


class searchQuery(models.Model):
    search = models.Char<PERSON>ield(max_length=500)
    created = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.search

    class Meta:
        verbose_name_plural = "Search Queries"


class LisingImages(models.Model):
    listing_id = models.CharField(max_length=500,db_index=True)
    image = models.ImageField(upload_to='listing_images/')


class ListingViewCount(models.Model):
    listing_id = models.BigIntegerField(db_index=True)
    category = models.Char<PERSON>ield(max_length=225,null=True)
    count = models.BigIntegerField(default=0)

    def __str__(self):
        return self.listing_id