# models.py
from django.db import models

class DataSourceProgress(models.Model):
    DATA_SOURCE_CHOICES = [
        ('all_mls', 'all_mls'),
        ('all_crmls', 'all_crmls'),
        ('openhouse_crmls', 'openhouse_crmls'),
        ('openhouse_mls', 'openhouse_mls'),
    ]

    data_source = models.CharField(max_length=100, choices=DATA_SOURCE_CHOICES)
    last_processed_page = models.IntegerField(default=0)
    updated_at = models.DateTimeField(auto_now=True)