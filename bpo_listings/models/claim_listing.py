from django.db import models
from django.core.validators import Min<PERSON><PERSON>ue<PERSON>alida<PERSON>, MaxValueValidator
from bpo_listings.utils.choices import (
    OFFER_DATE_CHOICES,
    GIVE_AUTHORISATION_CHOICES,
    OFFER_VIEW_CHOICES,
    DISPLAY_OFFER_CHOICES,
    HOURS_DAYS_CHOICES,
    DISPLAY_DURATION_CHOICES,
    COMMISSION_OFFER_CHOICES,
    EMAIL_COMMISSION_CHOICES,
    PAYER_CHOICES,
    AMOUNT_PERCENTAGE_CHOICES,
    PROPERTY_LOCK_CHOICES,
    LOCK_TYPE_CHOICES,
    TOTAL_COMMISSION_CHOICES
)
# Create your models here.
class ClaimListing(models.Model):
   
    listing_id = models.BigIntegerField(unique=True, db_index=True)
    claimed = models.BooleanField(default=False)
    total_commission = models.Char<PERSON>ield(
        max_length=20,
        choices=TOTAL_COMMISSION_CHOICES,
        null=True
    )
    review_offer_date = models.DateTimeField(null=True)
    offer_date_available = models.CharField(
        max_length=20,
        choices=OFFER_DATE_CHOICES,
        null=True
    )
    give_authorization = models.CharField(
        max_length=20,
        choices=GIVE_AUTHORISATION_CHOICES,
        null=True
    )
    seller_view_offer = models.CharField(
        max_length=20,
        choices=OFFER_VIEW_CHOICES,
        null=True
    )
    display_offer_review_status = models.CharField(
        max_length=20,
        choices=DISPLAY_OFFER_CHOICES,
        null=True
    )
    hours_days = models.CharField(
        max_length=20,
        choices=HOURS_DAYS_CHOICES,
        null=True
    )
    request_review_duration = models.IntegerField(
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(500)]
    )
    display_duration_response = models.CharField(
        max_length=20,
        choices=DISPLAY_DURATION_CHOICES,
        null=True
    )
    commission_available = models.CharField(
        max_length=20,
        choices=COMMISSION_OFFER_CHOICES,
        null=True
    )
    email_commission_rate = models.CharField(
        max_length=20,
        choices=EMAIL_COMMISSION_CHOICES,
        null=True
    )
    pay_buyer_broker = models.CharField(
        max_length=20,
        choices=PAYER_CHOICES,
        null=True
    )
    amount_percentage = models.CharField(
        max_length=20,
        choices=AMOUNT_PERCENTAGE_CHOICES,
        null=True
    )
    total_commission_rate = models.IntegerField(
        null=True,
        validators=[MinValueValidator(0), MaxValueValidator(1000000)]
    )
    lock_box_available = models.CharField(
        max_length=20,
        choices=PROPERTY_LOCK_CHOICES,
        null=True
    )
    lock_box_type = models.CharField(
        max_length=20,
        choices=LOCK_TYPE_CHOICES,
        null=True
    )
    other_lock_box_type = models.CharField(max_length=50, null=True)
    lock_box_location = models.CharField(max_length=500, null=True)
    private_notes = models.TextField(null=True, blank=True)
    agent = models.ForeignKey(
        "register.Agent",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="agentclaimlisting",
    )
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    listing_photo = models.CharField(max_length=500, null=True)
    listing_url = models.CharField(max_length=300, null=True)
    listing_address = models.CharField(max_length=300, null=True)

    def __str__(self):
        return self.listing_id

class ClaimUserRequest(models.Model):
    claim_listing = models.ForeignKey(
        ClaimListing,
        on_delete=models.CASCADE,
        related_name="claim_user_requests",
    )
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    email = models.EmailField(max_length=255)
    phone = models.CharField(max_length=20, null=True, blank=True)
    request_status = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    agent = models.ForeignKey(
        "register.Agent",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="agentclaimuserrequest",
    )

    def __str__(self):
        return f"ClaimUserRequest {self.id} - {self.request_status}"