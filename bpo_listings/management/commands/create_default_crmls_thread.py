import json
import os
from django.core.management.base import BaseCommand
from bpo_listings import ALL_CRMLS_INDEX_NAME, ES_PAGE_LIMIT
from bpo_listings.es_search.utils.filter_null_photos import exclude_null
from bpo_listings.es_search.utils.get_coordinates import add_missing_coordinates
from bpo_listings.es_search.utils.get_count import get_count
from bpo_listings.es_search.utils.listings_validation import create_or_update_listings
from bpo_listings.es_search.utils.process_listing import get_last_processed_page, update_last_processed_page
from bpo_listings.utils.crmls_handler import Crmls<PERSON>andler
from bpo_listings.utils.generics.es_data_formatters import crmls_data_formater
from concurrent.futures import ThreadPoolExecutor, as_completed
from django.db import IntegrityError

from bpo_listings.utils.generics.image_controller import process_high_images

crmlshandler = CrmlsHandler()

class Command(BaseCommand):
    help = "Create crmls listings on elastic search"

    def handle(self, *args, **options):
        self.stdout.write("Starting CRMLS processing...")

        source_name = "all_crmls"
        total_pages = get_count("all_crmls")
        start_page = get_last_processed_page(source_name) + 1

        authenticate = crmlshandler.authenticate()
        max_threads = min(5, os.cpu_count() * 2)

        def process_page(i):
            try:
                data = crmlshandler.all_data(authenticate, i, ES_PAGE_LIMIT)
                formatted = list(map(
                    lambda obj: crmls_data_formater(ALL_CRMLS_INDEX_NAME, obj),
                    data["value"]
                ))

                #pulling coordinates for null long and lat
                with ThreadPoolExecutor(max_workers=10) as cordinate_pool:
                    processed = list(cordinate_pool.map(add_missing_coordinates, formatted))

                with ThreadPoolExecutor(max_workers=10) as image_pool:
                    processed = list(image_pool.map(process_high_images, formatted))

                filtered = list(filter(exclude_null, processed))
                create_or_update_listings(filtered, ALL_CRMLS_INDEX_NAME)

                update_last_processed_page(source_name, i)

                return f"✅ Page {i} done"

            except Exception as e:
                return f"❌ Page {i} failed: {e}"

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            futures = {
                executor.submit(process_page, i): i
                for i in range(start_page, total_pages)
            }

            for future in as_completed(futures):
                result = future.result()
                self.stdout.write(result)

        self.stdout.write("🎉 All pages processed or skipped!")