from django.core.management.base import BaseCommand
from bpo_listings.es_search import es


class Command(BaseCommand):
    help = "Deletes an index In elastic Search"

    def add_arguments(self, parser):
        # Define the command-line arguments
        parser.add_argument("index", type=str, help="name of the index")

    def handle(self, *args, **options):
        self.stdout.write("Starting the command...")

        index = options["index"]

        response = es.indices.delete(index=index)

        # Check if the deletion was successful
        if response["acknowledged"]:
            self.stdout.write(f"{index} deleted successfully")
        else:
            self.stdout.write(f"Failed to delete index '{index}'.")
