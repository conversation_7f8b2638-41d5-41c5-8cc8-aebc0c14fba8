from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import os
from django.core.management.base import BaseCommand
from bpo_listings import ES_PAGE_LIMIT, OPENHOUSE_MLS_INDEX_NAME
from bpo_listings.es_search.utils.filter_null_photos import exclude_null
from bpo_listings.es_search.utils.get_coordinates import add_missing_coordinates
from bpo_listings.es_search.utils.get_count import get_count
from bpo_listings.es_search.utils.listings_validation import create_or_update_listings
from bpo_listings.utils.generics.es_data_formatters import open_house_mls_data_formater
from bpo_listings.utils.generics.image_controller import process_high_images
from bpo_listings.utils.mls_handler import MlsHandler

mlshandler = MlsHandler()


class Command(BaseCommand):
    help = "Create openhouse mls listings on elastic search"

    def handle(self, *args, **options):
        self.stdout.write("Starting the command...")
        count = get_count("openhouse_mls")
        page_num = count + 1

        # Automatically choose thread count (up to 5x CPU for I/O bound)
        max_threads = min(5, os.cpu_count() * 2)

        def process_page(i):
            data = mlshandler.all_open_house(i, ES_PAGE_LIMIT)
            self.stdout.write(
                f"range: {i} out of {count} openhouse mls saved({ES_PAGE_LIMIT} on each page)"
            )
            formatted_data = list(
                map(
                    lambda obj: open_house_mls_data_formater(obj),
                    data["value"],
                )
            )

            #pulling coordinates for null long and lat
            with ThreadPoolExecutor(max_workers=10) as cordinate_pool:
                processed = list(cordinate_pool.map(add_missing_coordinates, formatted_data))

            with ThreadPoolExecutor(max_workers=10) as image_pool:
                processed = list(image_pool.map(process_high_images, formatted_data))

            filtered_data = list(filter(exclude_null, formatted_data))

            create_or_update_listings(filtered_data, OPENHOUSE_MLS_INDEX_NAME)

            return f"{i} out of {count} pages done ({ES_PAGE_LIMIT} per page)"

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            futures = {executor.submit(process_page, i): i for i in range(1, page_num)}

            for future in as_completed(futures):
                result = future.result()
                self.stdout.write(result)
        
        self.stdout.write("finally done")
