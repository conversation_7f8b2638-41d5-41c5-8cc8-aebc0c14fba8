from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import json
import os
from django.core.management.base import BaseCommand
from bpo_listings import ES_PAGE_LIMIT, OPENHOUSE_MLS_INDEX_NAME
from bpo_listings.es_search.search_data.merge_search import backend_elastic_search
from bpo_listings.es_search.utils.filter_null_photos import exclude_null
from bpo_listings.es_search.utils.get_coordinates import add_missing_coordinates
from bpo_listings.es_search.utils.get_count import get_count
from bpo_listings.es_search.utils.listings_validation import create_or_update_listings
from bpo_listings.utils.generics.es_data_formatters import open_house_mls_data_formater
from bpo_listings.utils.generics.image_controller import process_high_images
from bpo_listings.utils.mls_handler import MlsHandler

mlshandler = MlsHandler()


class Command(BaseCommand):
    help = "Create openhouse mls listings on elastic search"

    def handle(self, *args, **options):
        self.stdout.write("Starting the command...")
        params = {}
        index_list = ['all_mls', 'all_crmls', 'openhouse_mls', 'openhouse_crmls']
        limit = 50

        for index, index_name in enumerate(index_list):
            self.stdout.write(f"Processing index: {index_name}")
            page_num = 1

            if index_name in ["all_crmls", "openhouse_crmls"]:
                list_type = "crmls"
            else:
                list_type = "mls"

            while True:
                self.stdout.write(f"Fetching page {page_num} from index {index_name} with list type {list_type}")
                data = backend_elastic_search(params, 1, index_name, limit, page_num, list_type)
                listings = data.get("data", [])

                if not listings:
                    self.stdout.write(f"No more listings found in index {index_name} at page {page_num}")
                    break

                self.stdout.write(f"Processing {len(listings)} listings from page {page_num} of index {index_name}")

                # pulling coordinates for null long and lat
                with ThreadPoolExecutor(max_workers=10) as cordinate_pool:
                    processed = list(cordinate_pool.map(add_missing_coordinates, listings))

                self.stdout.write(f"Updating listings in Elasticsearch for index {index_name}")
                create_or_update_listings(processed, index_name)
                page_num += 1

        self.stdout.write("Finished processing all indices.")