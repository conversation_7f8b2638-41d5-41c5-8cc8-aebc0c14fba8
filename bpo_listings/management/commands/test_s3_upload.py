import os
import boto3
from io import BytesIO
from PIL import Image
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = "Test S3 image upload"

    def add_arguments(self, parser):
        parser.add_argument("image_path", type=str, help="Local path to the image")

    def handle(self, *args, **options):
        image_path = options["image_path"]
        bucket_name = "bpohomes-listings"

        if not os.path.exists(image_path):
            self.stderr.write(self.style.ERROR(f"File not found: {image_path}"))
            return

        try:
            image = Image.open(image_path)
            image_format = image.format or "JPEG"
            img_data = BytesIO()
            image.save(img_data, format=image_format)
            img_data.seek(0)

            file_name = os.path.basename(image_path)
            s3_key = f"test-uploads/{file_name}"

            s3 = boto3.client("s3",
                           aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                            region_name="us-west-1"
                            )
            s3.upload_fileobj(
                img_data,
                bucket_name,
                s3_key,
                
            )

            url = f"https://{bucket_name}.s3.amazonaws.com/{s3_key}"
            self.stdout.write(self.style.SUCCESS(f"Uploaded successfully: {url}"))

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Upload failed: {e}"))
