from datetime import date, <PERSON>elta
import os
from django.core.management.base import BaseCommand
from bpo_listings import ALL_CRMLS_INDEX_NAME, ES_PAGE_LIMIT
from bpo_listings.es_search.utils.filter_null_photos import exclude_null
from bpo_listings.es_search.utils.get_coordinates import add_missing_coordinates
from bpo_listings.es_search.utils.get_count import get_count
from bpo_listings.es_search.utils.listings_validation import create_or_update_listings
from bpo_listings.utils.crmls_handler import Crmls<PERSON>andler
from bpo_listings.utils.generics.es_data_formatters import crmls_data_formater
from concurrent.futures import ThreadPoolExecutor, as_completed

from bpo_listings.utils.generics.image_controller import process_high_images

crmlshandler = CrmlsHandler()


class Command(BaseCommand):
    help = "Create crmls listings on elastic search"

    def handle(self, *args, **options):
        self.stdout.write("Starting the command...")

        count = get_count("all_crmls", is_yesteday=True)
        page_num = count + 1
        authenticate = crmlshandler.authenticate()

        # Automatically choose thread count (up to 5x CPU for I/O bound)
        max_threads = min(5, os.cpu_count() * 2)
        def process_page(i):
            data = crmlshandler.all_data(authenticate, i, ES_PAGE_LIMIT, is_yesteday=True)
            self.stdout.write(
                f"range: {i} out of {count} crmls saved({ES_PAGE_LIMIT} on each page)"
            )

            formatted = list(
                map(
                    lambda obj: crmls_data_formater(ALL_CRMLS_INDEX_NAME, obj),
                    data["value"],
                )
            )

            #pulling coordinates for null long and lat
            with ThreadPoolExecutor(max_workers=10) as cordinate_pool:
                processed = list(cordinate_pool.map(add_missing_coordinates, formatted))

            with ThreadPoolExecutor(max_workers=10) as image_pool:
                processed = list(image_pool.map(process_high_images, formatted))

            filtered = list(filter(exclude_null, processed))
            create_or_update_listings(filtered, ALL_CRMLS_INDEX_NAME)
            self.stdout.write(
                "*******************************************************************"
            )
            self.stdout.write(
                f"{i} out of {count} crmls saved({ES_PAGE_LIMIT} on each page"
            )

            self.stdout.write(
                "*******************************************************************"
            )


            return f"{i} out of {count} pages done ({ES_PAGE_LIMIT} per page)"

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            futures = {executor.submit(process_page, i): i for i in range(1, page_num)}

            for future in as_completed(futures):
                result = future.result()
                self.stdout.write(result)

        self.stdout.write("✅ All pages processed successfully.")
        self.stdout.write("finally done")
        self.stdout.write(f"Total pages processed: {count}")