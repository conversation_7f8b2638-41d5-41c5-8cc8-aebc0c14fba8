from django.core.management.base import BaseCommand
from bpo_listings.es_search import es


class Command(BaseCommand):
    help = "Create an index In elastic Search"

    def add_arguments(self, parser):
        # Define the command-line arguments
        parser.add_argument("index", type=str, help="name of the index")
        parser.add_argument("number_of_replica", type=str, help="number of copies")

    def handle(self, *args, **options):
        self.stdout.write("Starting the command...")
        index = options["index"]
        number_of_replica = options["number_of_replica"]

        index_settings = {"settings": {"number_of_replicas": number_of_replica}}

        # Create the index with the specified settings
        es.indices.create(index=index, body=index_settings)

        self.stdout.write("created successfully")
