from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
import os
from django.core.management.base import BaseCommand
from bpo_listings import CRMLS_OPENHOUSE_INDEX_NAME, ES_PAGE_LIMIT
from bpo_listings.es_search.utils.filter_null_photos import exclude_null
from bpo_listings.es_search.utils.get_coordinates import add_missing_coordinates
from bpo_listings.es_search.utils.get_count import get_count
from bpo_listings.es_search.utils.listings_validation import create_or_update_listings
from bpo_listings.es_search.utils.process_listing import get_last_processed_page, update_last_processed_page
from bpo_listings.utils.crmls_handler import CrmlsHandler
from bpo_listings.utils.generics.es_data_formatters import (
    open_house_crmls_data_formater,
)
from bpo_listings.utils.generics.image_controller import process_high_images

crmlshandler = CrmlsHandler()


class Command(BaseCommand):
    help = "Create crmls listings on elastic search"

    def handle(self, *args, **options):
        self.stdout.write("Starting the command...")

        source_name = "openhouse_crmls"

        total_pages = get_count("openhouse_crmls")
        start_page = get_last_processed_page(source_name) + 1

        authenticate = crmlshandler.authenticate()

        max_threads = min(5, os.cpu_count() * 2)
        def process_page(i):
            try:

                data = crmlshandler.all_open_house(authenticate, i, ES_PAGE_LIMIT)
                self.stdout.write(
                    f"range: {i} out of {total_pages} openhouse crmls saved({ES_PAGE_LIMIT} on each page)"
                )
                filtered_openhouses = [
                    value for value in data["value"] if value["Property"] is not None
                ]

                formatted_data = list(
                    map(
                        lambda obj: open_house_crmls_data_formater(obj),
                        filtered_openhouses,
                    )
                )

                #pulling coordinates for null long and lat
                with ThreadPoolExecutor(max_workers=10) as cordinate_pool:
                    processed = list(cordinate_pool.map(add_missing_coordinates, formatted_data))

                with ThreadPoolExecutor(max_workers=10) as image_pool:
                    processed = list(image_pool.map(process_high_images, formatted_data))

                filtered_data = list(filter(exclude_null, formatted_data))

                create_or_update_listings(filtered_data, CRMLS_OPENHOUSE_INDEX_NAME)

                update_last_processed_page(source_name, i)



                self.stdout.write(
                    "*******************************************************************"
                )
                
                self.stdout.write(
                    f"{i} out of {total_pages} crmls saved({ES_PAGE_LIMIT} on each page"
                )

                self.stdout.write(
                    "*******************************************************************"
                )

            except Exception as e:
                return f"❌ Page {i} failed: {e}"


            return f"{i} out of {total_pages} pages done ({ES_PAGE_LIMIT} per page)"


        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            futures = {
                executor.submit(process_page, i): i
                for i in range(start_page, total_pages)
            }

            for future in as_completed(futures):
                result = future.result()
                self.stdout.write(result)


        self.stdout.write("finally done")
