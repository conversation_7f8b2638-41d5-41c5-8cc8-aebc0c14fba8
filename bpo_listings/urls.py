from django.urls import path

from bpo_listings.views.crmls.default import (
    CrmlsDetail,
    CrmlslsListings,
    RentalCrmlslsListings,
    ResidentialCrmlslsListings,
)
from bpo_listings.views.crmls.openhouse import (
    OpenHouseCrmlsDetail,
    OpenHouseCrmlsListings,
)
from bpo_listings.views.crmls.sold import (
    RentalSoldCrmlslsListings,
    ResidentialSoldCrmlslsListings,
    SoldCrmlsDetail,
    SoldCrmlslsListings,
)
from bpo_listings.views.listings_views.search_query import ListingView
from bpo_listings.views.merge.default import (
    MergeListings,
    MergeRentalListings,
    MergeResidentialListings,
)
from bpo_listings.views.merge.openhouse import MergeOpenHouseListings
from bpo_listings.views.merge.sold import (
    MergeRentalSoldListings,
    MergeResidentialSoldListings,
    MergeSoldListings,
)
from bpo_listings.views.mls.default import (
    MlsDetail,
    MlsListings,
    RentalMlsListings,
    ResidentialMlsListings,
)
from bpo_listings.views.mls.openhouse import OpenHouseMlsDetail, OpenHouseMlsListings
from bpo_listings.views.mls.sold import (
    RentalSoldMlsListings,
    ResidentialSoldMlsListings,
    SoldMlsDetail,
    SoldMlsListings,
)
from bpo_listings.views.search_query.search_query import ReverseGeocodeView, SearchUpload

urlpatterns = [
    # RESIDENTIAL
    path("residential/merge_listings/", MergeResidentialListings.as_view()),
    path("residential/merge_listings/sold/", MergeResidentialSoldListings.as_view()),
    path("residential/mls/", ResidentialMlsListings.as_view()),
    path("residential/crmls/", ResidentialCrmlslsListings.as_view()),
    path("residential/mls/sold/", ResidentialSoldMlsListings.as_view()),
    path("residential/crmls/sold/", ResidentialSoldCrmlslsListings.as_view()),
    # RENTAL
    path("rental/merge_listings/", MergeRentalListings.as_view()),
    path("rental/merge_listings/sold/", MergeRentalSoldListings.as_view()),
    path("rental/mls/", RentalMlsListings.as_view()),
    path("rental/crmls/", RentalCrmlslsListings.as_view()),
    path("rental/mls/sold/", RentalSoldMlsListings.as_view()),
    path("rental/crmls/sold/", RentalSoldCrmlslsListings.as_view()),
    # OPENHOUSE
    path("openhouse/mls/", OpenHouseMlsListings.as_view()),
    path("openhouse/crmls/", OpenHouseCrmlsListings.as_view()),
    path("openhouse/merge_listings/", MergeOpenHouseListings.as_view()),
    # DEFAULT SEARCH
    path("mls/", MlsListings.as_view()),
    path("mls/sold/", SoldMlsListings.as_view()),
    path("crmls/", CrmlslsListings.as_view()),
    path("crmls/sold/", SoldCrmlslsListings.as_view()),
    path("merge_listings/", MergeListings.as_view()),
    path("merge_listings/sold/", MergeSoldListings.as_view()),
    # GET DETAILS
    path("mls/<int:pk>/", MlsDetail.as_view()),
    path("crmls/<int:pk>/", CrmlsDetail.as_view()),
    path("openhouse/mls/<int:pk>/", OpenHouseMlsDetail.as_view()),
    path("openhouse/crmls/<int:pk>/", OpenHouseCrmlsDetail.as_view()),
    path("sold/mls/<int:pk>/", SoldMlsDetail.as_view()),
    path("sold/crmls/<int:pk>/", SoldCrmlsDetail.as_view()),

    # SAVE SEARCH
    path("search-upload/", SearchUpload.as_view()),
    path("get-location/", ReverseGeocodeView.as_view()),

    # VIEW COUNT
    path("listing-count/", ListingView.as_view()),


]
