# Generated by Django 3.2.15 on 2025-04-22 21:08

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0112_auto_20250415_2133'),
        ('bpo_listings', '0006_datasourceprogress'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimListing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('listing_id', models.BigIntegerField(db_index=True, unique=True)),
                ('claimed', models.BooleanField(default=False)),
                ('concession_available', models.CharField(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('review_offer_date', models.DateTimeField(null=True)),
                ('offer_date_available', models.Char<PERSON><PERSON>(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('give_authorization', models.CharField(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('seller_view_offer', models.CharField(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('display_offer_review_status', models.CharField(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('hours_days', models.CharField(choices=[
                 ('hours', 'Hours'), ('days', 'Days')], max_length=20, null=True)),
                ('request_review_duration', models.IntegerField(null=True, validators=[
                 django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(500)])),
                ('display_duration_response', models.CharField(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('commission_available', models.CharField(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('email_commission_rate', models.CharField(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('pay_buyer_broker', models.CharField(choices=[
                 ('seller', 'Seller'), ('sellerbrokerage', 'Seller Brokerage')], max_length=20, null=True)),
                ('amount_percentage', models.CharField(choices=[
                 ('amount', 'Amount'), ('percentage', 'Percentage')], max_length=20, null=True)),
                ('total_commission_rate', models.IntegerField(null=True, validators=[
                 django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1000000)])),
                ('lock_box_available', models.CharField(choices=[
                 ('yes', 'Yes'), ('no', 'No')], max_length=20, null=True)),
                ('lock_box_type', models.CharField(choices=[
                 ('supra', 'Supra'), ('combo', 'Combo'), ('other', 'Other')], max_length=20, null=True)),
                ('other_lock_box_type', models.CharField(max_length=50, null=True)),
                ('lock_box_location', models.CharField(max_length=500, null=True)),
                ('private_notes', models.TextField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE,
                 related_name='agentclaimlisting', to='register.agent')),
            ],
        ),
    ]
