# Generated by Django 3.2.15 on 2025-04-11 18:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bpo_listings', '0005_alter_listingviewcount_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='DataSourceProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_source', models.CharField(choices=[('all_mls', 'all_mls'), ('all_crmls', 'all_crmls'), ('openhouse_crmls', 'openhouse_crmls'), ('openhouse_mls', 'openhouse_mls')], max_length=100)),
                ('last_processed_page', models.IntegerField(default=0)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
