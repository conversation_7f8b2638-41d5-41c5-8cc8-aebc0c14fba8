# Generated by Django 3.2.15 on 2025-05-02 16:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('register', '0112_auto_20250415_2133'),
        ('bpo_listings', '0008_auto_20250424_1447'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimUserRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=50)),
                ('last_name', models.Char<PERSON>ield(max_length=50)),
                ('email', models.Email<PERSON>ield(max_length=255)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('request_status', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='agentclaimuserrequest', to='register.agent')),
                ('claim_listing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='claim_user_requests', to='bpo_listings.claimlisting')),
            ],
        ),
    ]
