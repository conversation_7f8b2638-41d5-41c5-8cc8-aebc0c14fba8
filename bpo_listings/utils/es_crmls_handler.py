import requests
import os
from django.core.cache import cache
from bpo_listings.utils.generics.generic_filters import (
    crmls_odata_filters,
    constant_values,
)
from bpo_listings.utils.generics.data_formatters import (
    crmls_data_formater,
    open_house_crmls_data_formater,
)
from bpo_listings.utils.generics.city_filters import city_filter
from bpo_listings import CRMLS_URL


def search_query(values):
    filter_query1 = "("
    for value in values:
        query = city_filter(value)
        filter_query1 += f"{crmls_odata_filters['city']} '{query}') or "
        filter_query1 += f"{crmls_odata_filters['address']} '{query}') or "
    return filter_query1


class CrmlsHandler:
    client_id = os.getenv("CRMLS_CLIENT_ID")
    client_secret = os.getenv("CRMLS_CLIENT_SECRET")

    def authenticate(self):
        endpoint_url = f"{CRMLS_URL}oidc/connect/token"
        payload = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "scope": "api",
            "grant_type": "client_credentials",
        }
        response = requests.post(
            endpoint_url,
            data=payload,
        )
        json_response = response.json()

        session_key = "crmls_access_token"
        value = json_response["access_token"]

        # Set the value in the session cache
        cache.set(session_key, value, 25200)

        return json_response["access_token"]

    def all(self, filters, access_token, page_num=0, limit=10):
        headers = {"Authorization": f"Bearer {access_token}"}
        if page_num == 0:
            skip = 0
        else:
            skip = int(limit) * (int(page_num) - 1)
        exclude_filter = f"({filters}) and {constant_values}"

        response = requests.get(
            f"{CRMLS_URL}odata/Property?"
            + "$filter="
            + exclude_filter
            + "&$skip="
            + str(skip)
            + "&$top="
            + str(limit)
            + "&$expand=Media"
            + "&$count=true",
            headers=headers,
        )

        response_json = response.json()
        try:
            response_json["statusCode"]
            access_token = self.authenticate()
            headers = {"Authorization": f"Bearer {access_token}"}

            response = requests.get(
                f"{CRMLS_URL}odata/Property?"
                + "$filter="
                + exclude_filter
                + "&$skip="
                + str(skip)
                + "&$top="
                + str(limit)
                + "&$expand=Media"
                + "&$count=true",
                headers=headers,
            )

        except Exception:
            pass

        return response.json()

    def all_open_house(self, access_token, page_num=0, limit=10):
        if page_num == 0:
            skip = 0
        else:
            skip = int(limit) * (int(page_num) - 1)

        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(
            f"{CRMLS_URL}odata/OpenHouse?"
            + "$expand=Property($expand=Media)&$filter=Property/any(c:ListingId ne null)"
            + "&$skip="
            + str(skip)
            + "&$top="
            + str(limit)
            + "&$count=true",
            headers=headers,
        )

        response_json = response.json()

        try:
            response_json["statusCode"]
            access_token = self.authenticate()
            headers = {"Authorization": f"Bearer {access_token}"}

            response = requests.get(
                f"{CRMLS_URL}odata/OpenHouse?"
                + "$expand=Property"
                + "&$skip="
                + str(skip)
                + "&$top="
                + str(limit)
                + "&$count=true",
                headers=headers,
            )

        except Exception:
            pass

        return response.json()