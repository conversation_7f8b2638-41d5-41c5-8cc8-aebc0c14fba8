import json
import requests
import os
from bpo_listings.utils.generics.generic_filters import (
    mls_odata_filters,
    constant_values,
)
from datetime import date, timedelta
from bpo_listings.utils.generics.city_filters import city_filter
from bpo_listings.utils.generics.mls_cities import mls_city_search


def address_search_query(values):
    filter_query1 = "("
    for value in values:
        query = city_filter(value)
        filter_query1 += f"{mls_odata_filters['address']} '{query}') or "
    return filter_query1


class MlsHandler:
    mls_key = os.getenv("MLS_KEY")

    def all(self, page_num=0, limit=10):
        headers = {"Authorization": f"Bearer {self.mls_key}"}
        if page_num == 0:
            skip = 0
        else:
            skip = int(limit) * (int(page_num) - 1)
        exclude_filter = (
            f"MlsStatus ne  ResourceEnums.MlsStatus'Sold' and {constant_values}"
        )

        response = requests.get(
            "https://data.api.mlslistings.com/idx/Property?"
            + "$filter="
            + exclude_filter
            + "&$skip="
            + str(skip)
            + "&$top="
            + str(limit)
            + "&$expand=Media"
            + "&$count=true",
            headers=headers,
        )

        return response.json()

    def all_open_house(self, page_num=0, limit=10, is_yesteday=False):
        if page_num == 0:
            skip = 0
        else:
            skip = int(limit) * (int(page_num) - 1)

        open_house_filter = f"OpenHouseYN eq true and MlsStatus ne  ResourceEnums.MlsStatus'Sold' and {constant_values}"


        if is_yesteday:
            yesterday = date.today() - timedelta(days=1)
            formatted_yesterday = yesterday.isoformat()
            open_house_filter = f"ListingContractDate eq {formatted_yesterday} and {constant_values}"



        headers = {"Authorization": f"Bearer {self.mls_key}"}
        response = requests.get(
            "https://data.api.mlslistings.com/idx/Property?$filter="
            + open_house_filter
            + "&$skip="
            + str(skip)
            + "&$top="
            + str(limit)
            + "&$expand=Media"
            + "&$orderby=ListingContractDate desc"
            + "&$count=true",
            headers=headers,
        )

        return response.json()


    def all_data(self, page_num=0, limit=10, is_yesteday=False):
        headers = {"Authorization": f"Bearer {self.mls_key}"}
        if page_num == 0:
            skip = 0
        else:
            skip = int(limit) * (int(page_num) - 1)
        exclude_filter = (
            f"{constant_values}"
        )

        if is_yesteday:
            yesterday = date.today() - timedelta(days=1)
            formatted_yesterday = yesterday.isoformat()
            exclude_filter = f"ListingContractDate eq {formatted_yesterday} and {constant_values}"


        response = requests.get(
            "https://data.api.mlslistings.com/idx/Property?"
            + "$filter="
            + exclude_filter
            + "&$skip="
            + str(skip)
            + "&$top="
            + str(limit)
            + "&$expand=Media"
            + "&$orderby=ListingContractDate desc"
            + "&$count=true",
            headers=headers,
        )

        return response.json()


    def get_detail(self, id):
        filter_query = f"ListingKeyNumeric eq {id}"

        headers = {"Authorization": f"Bearer {self.mls_key}"}

        response = requests.get(
            "https://data.api.mlslistings.com/idx/Property?$filter="
            + filter_query
            + "&$expand=Media",
            headers=headers,
        )

        return response.json()
