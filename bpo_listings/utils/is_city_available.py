import os
import googlemaps


api_key = os.getenv("GOOGLE_API")


def is_city_available(city_name):
    gmaps = googlemaps.Client(key=api_key)

    # Use Places API to search for places near the city with a general type
    places_result = gmaps.places(query=city_name)

    # Extract names of places containing certain keywords
    keyword = city_name.split()[
        0
    ].lower()  # Use the first word of the city name as a keyword
    related_names = [
        place["name"]
        for place in places_result.get("results", [])
        if keyword in place["name"].lower()
    ]

    if related_names:
        return True
    return False
