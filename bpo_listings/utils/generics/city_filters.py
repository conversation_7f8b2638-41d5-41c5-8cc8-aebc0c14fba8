from bpo_listings.utils.generics.mls_city_checker import listing_city_data


def city_filter(city, listing_type = "mls"):
    if city == "palo alto" or city == "Palo alto":
        return "paloalto"
    if city == "san jose" or city == "San jose" or city == "San Jose":
        return "sanjose"
    if city == "san francisco":
        return "sanfrancisco"
    if city == "san francisco":
        return "sanfrancisco"
    if city == "menlo park":
        return "menlopark"
    if city == "redwood city":
        return "redwoodcity"
    return city


def city_filter_recent(city):
    if city == "paloalto" or city == "Paloalto":
        return "palo alto"
    if city == "sanjose" or city == "Sanjose" or city == "SanJose":
        return "san jose"
    if city == "sanfrancisco":
        return "san francisco"
    if city == "sanfrancisco":
        return "san francisco"
    if city == "menlopark":
        return "menlo park"
    if city == "redwoodcity":
        return "redwood city"
    return city

def get_listing_city(city, listing_type="mls"):

    if listing_type == "openhouse_crmls":
        listing_type = "crmls"
    elif listing_type == "openhouse_mls":
        listing_type = "mls"

    

    city = city.title()  # normalize input
    mapping = listing_city_data.get(listing_type, {})
    return mapping.get(city, city)