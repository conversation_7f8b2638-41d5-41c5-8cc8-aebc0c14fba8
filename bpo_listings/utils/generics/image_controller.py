import os
import boto3
import requests
from PIL import Image
from io import BytesIO
from django.core.files.base import ContentFile
from urllib.parse import urlparse
from bpo_listings.models import LisingImages
import uuid

s3 = boto3.client("s3",
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
        region_name="us-west-1"
        )
BUCKET_NAME = "bpohomes-listings"



def is_high_resolution_image(url):
    try:
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            image = Image.open(BytesIO(response.content))
            image.verify()  # Verify the integrity of the image
            image = Image.open(BytesIO(response.content))
            image.load()  # Load the image to catch any potential issues
            width, height = image.size
            return width >= 500 and height >= 500, image
        else:
            return False, None
    except (IOError, SyntaxError) as e:
        print(f"Invalid image {url}: {e}")
        return False, None

    except Exception:
        return False, None



# checking resolution
def get_high_resolution_image(images, id):
    high_resolution_images = []
    for url in images[:50]:
        is_valid, image = is_high_resolution_image(url)
        if is_valid:
            # Save the image to the MlsImages model
            try:
                img_data = BytesIO()
                image.save(img_data, format=image.format)
                img_data.seek(0)
                
                # Extracting image file name from URL
                file_name = os.path.basename(url)
                
                # Creating a ContentFile object to save to the model
                image_file = ContentFile(img_data.getvalue(), name=file_name)
                
                # Save the image to the model
                images = LisingImages.objects.create(listing_id=id,image=image_file)

                high_resolution_images.append(images.image.url)
            except Exception as e:
                print(f"Error saving image {url}: {e}")
            # MlsImages.objects.create(image_url=url, image_file=img_data)
            
    return high_resolution_images

def process_high_images(item):
    high_resolution_images = []
    photos = item.get("photos", [])  # assuming 'photos' is a list of URLs in the item
    listingId = item.get("listingId", [])  # assuming 'photos' is a list of URLs in the item

    for url in photos:
        is_valid, image = is_high_resolution_image(url)
        if is_valid:
            try:
                img_data = BytesIO()
                image_format = image.format if image.format else "JPEG"
                image.save(img_data, format=image_format)
                img_data.seek(0)

                # Generate a unique filename using UUID
                file_ext = image_format.lower()
                unique_filename = f"{uuid.uuid4()}.{file_ext}"

                # Optional: prefix with MLS ID or directory structure
                s3_key = f"listings/{item.get('listing', 'mls')}/{unique_filename}"

                s3.upload_fileobj(
                    img_data,
                    BUCKET_NAME,
                    s3_key,
                )

                # Construct public S3 URL (assuming public-read ACL)
                s3_url = f"https://{BUCKET_NAME}.s3.amazonaws.com/{s3_key}"
                high_resolution_images.append(s3_url)

            except Exception as e:
                print(f"Error saving image {url}: {e}")

    # Replace original photo URLs with your hosted ones
    item["photos"] = high_resolution_images
    return item