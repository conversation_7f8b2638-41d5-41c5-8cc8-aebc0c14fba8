mls_odata_filters = {}
mls_odata_filters["city"] = "City eq ResourceEnums.City"
mls_odata_filters["address"] = "contains(tolower(UnparsedAddress),"
mls_odata_filters["beds"] = "BedroomsTotal eq "
mls_odata_filters["baths"] = "BathroomsFull eq "
mls_odata_filters["property_type"] = "PropertyType eq ResourceEnums.PropertyType"
mls_odata_filters[
    "property_sub_type"
] = "PropertySubType eq  ResourceEnums.PropertySubType"
mls_odata_filters["min_price"] = "ListPrice ge "
mls_odata_filters["max_price"] = " ListPrice le "
mls_odata_filters["status"] = "MlsStatus eq  ResourceEnums.MlsStatus"
mls_odata_filters["min_area"] = "LotSizeSquareFeet ge "
mls_odata_filters["max_area"] = " LotSizeSquareFeet le "
mls_odata_filters["min_year"] = "YearBuilt ge "
mls_odata_filters["max_year"] = " YearBuilt le "


crmls_odata_filters = {}
crmls_odata_filters["city"] = "contains(tolower(City),"
crmls_odata_filters["address"] = "contains(tolower(UnparsedAddress),"
crmls_odata_filters["beds"] = "BedroomsTotal eq "
crmls_odata_filters["baths"] = "BathroomsFull eq "
crmls_odata_filters["property_type"] = "PropertyType eq"
crmls_odata_filters["property_sub_type"] = "PropertySubType eq  "
crmls_odata_filters["min_price"] = "ListPrice ge "
crmls_odata_filters["max_price"] = " ListPrice le "
crmls_odata_filters["min_area"] = "LotSizeSquareFeet ge "
crmls_odata_filters["max_area"] = " LotSizeSquareFeet le "
crmls_odata_filters["min_year"] = "YearBuilt ge "
crmls_odata_filters["max_year"] = " YearBuilt le "
crmls_odata_filters["status"] = "MlsStatus eq "


constant_values = "LotSizeAcres ne null and BathroomsFull ne null and GarageSpaces ne null and Cooling ne null and Roof ne null and LotSizeSquareFeet ne null"