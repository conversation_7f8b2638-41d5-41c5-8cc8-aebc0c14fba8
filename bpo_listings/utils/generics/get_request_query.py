from bpo_listings.es_search.search_data.generate_query import generate_search_query
from bpo_listings.utils.generics.city_filters import city_filter


def request_query_elastic(query, property_type, mls_type):
    new_query_list = {}
    for var in query:
        if var == "page_num":
            pass
        elif var == "limit":
            pass
        elif var == "id":
            pass
        elif var == "city":
            city = query[var]
            new_query_list[var] = city
        elif var == "q":
            new_cities = [city for city in query.getlist(var)]
            if not len(query[var]) == 0:
                new_query_list[var] = new_cities

        else:
            try:
                new_query_list[var] = int(query[var])
            except ValueError:
                new_query_list[var] = query[var]

    if property_type:
        new_query_list["property_type"] = property_type
    if len(new_query_list) <= 1:
        query = generate_search_query(new_query_list, 0, mls_type)
    else:
        if new_query_list["property_type"] == "residential":
            query = generate_search_query(new_query_list, 2, mls_type)
        elif new_query_list["property_type"] == "rental":
            query = generate_search_query(new_query_list, 2, mls_type)
        else:
            query = generate_search_query(new_query_list, 1, mls_type)

    return query


def request_query_merge_elastic(query, property_type):
    new_query_list = {}
    for var in query:
        if var == "page_num":
            pass
        elif var == "limit":
            pass
        elif var == "id":
            pass
        elif var == "city":
            city = query[var]
            new_query_list[var] = city
        elif var == "q":
            new_cities = [city for city in query.getlist(var)]
            if not len(query[var]) == 0:
                new_query_list[var] = new_cities

        else:
            try:
                new_query_list[var] = int(query[var])
            except ValueError:
                new_query_list[var] = query[var]

    if property_type:
        new_query_list["property_type"] = property_type
    if len(new_query_list) <= 1:
        minimum_should_match = 0
    else:
        if new_query_list["property_type"] == "residential":
            minimum_should_match = 2
        elif new_query_list["property_type"] == "rental":
            minimum_should_match = 2
        else:
            minimum_should_match = 1

    return new_query_list, minimum_should_match


def request_query(query):
    new_query_list = {}
    for var in query:
        if var == "page_num":
            pass
        elif var == "limit":
            pass
        elif var == "city":
            city = city_filter(query[var])
            new_query_list[var] = city
        elif var == "q":
            new_query_list[var] = query.getlist(var)

        else:
            try:
                new_query_list[var] = int(query[var])
            except ValueError:
                new_query_list[var] = query[var]

    return new_query_list
