import json


def mls_data_formater(data):
    data_json = {}
    data_json["property"] = {}
    data_json["property"]["roof"] = data["Roof"]
    data_json["property"]["cooling"] = data["Cooling"]
    data_json["property"]["style"] = data["ArchitecturalStyle"]
    data_json["property"]["area"] = data["LivingArea"]
    data_json["property"]["bathsFull"] = data["BathroomsFull"]
    data_json["property"]["bathsHalf"] = data["BathroomsHalf"]
    data_json["property"]["stories"] = data["StoriesTotal"]
    data_json["property"]["fireplaces"] = data["FireplacesTotal"]
    data_json["property"]["flooring"] = data["Flooring"]
    data_json["property"]["heating"] = data["Heating"]
    data_json["property"]["bathrooms"] = data["BathroomsFull"]
    data_json["property"]["foundation"] = data["FoundationDetails"]
    data_json["property"]["laundryFeatures"] = data["LaundryFeatures"]
    data_json["property"]["occupantName"] = data["BuilderName"]
    data_json["property"]["ownerName"] = data["Ownership"]
    data_json["property"]["lotDescription"] = data["LotDimensionsSource"]
    data_json["property"]["pool"] = data["PoolFeatures"]
    data_json["property"]["subType"] = data["PropertySubType"]
    data_json["property"]["bedrooms"] = data["BedroomsTotal"]
    data_json["property"]["interiorFeatures"] = data["InteriorFeatures"]
    data_json["property"]["lotSize"] = data["LotSizeSquareFeet"]
    data_json["property"]["areaSource"] = data["BuildingAreaSource"]
    data_json["property"]["maintenanceExpense"] = "null"
    data_json["property"]["additionalRooms"] = "null"
    data_json["property"]["exteriorFeatures"] = data["ExteriorFeatures"]
    data_json["property"]["water"] = data["WaterSource"]
    data_json["property"]["view"] = data["View"]
    data_json["property"]["lotSizeArea"] = data["LotSizeSquareFeet"]
    data_json["property"]["subdivision"] = data["SubdivisionName"]
    data_json["property"]["construction"] = data["ConstructionMaterials"]
    data_json["property"]["parking"] = {}
    data_json["property"]["parking"]["leased"] = data["NumberOfUnitsLeased"]
    data_json["property"]["parking"]["spaces"] = data["OpenParkingSpaces"]
    data_json["property"]["parking"]["description"] = data["OriginatingSystemName"]
    data_json["property"]["lotSizeAreaUnits"] = "SquareFeet"
    data_json["property"]["type"] = data["PropertyType"]
    data_json["property"]["garageSpaces"] = data["GarageSpaces"]
    data_json["property"]["bathsThreeQuarter"] = data["BathroomsThreeQuarter"]
    data_json["property"]["accessibility"] = data["AccessibilityFeatures"]
    data_json["property"]["acres"] = data["LotSizeAcres"]
    data_json["property"]["occupantType"] = data["OccupantType"]
    data_json["property"]["subTypeText"] = data["PropertySubType"]
    data_json["property"]["yearBuilt"] = data["YearBuilt"]
    data_json["property"]["horseYN"] = data["HorseYN"]
    data_json["property"]["sewer"] = data["Sewer"]
    data_json["property"]["zoning"] = data["Zoning"]
    data_json["property"]["utilities"] = data["Utilities"]
    data_json["mlsId"] = data["ListingKeyNumeric"]
    data_json["showingContactPhone"] = data["Telephone"]
    data_json["terms"] = data["ListingTerms"]
    data_json["showingInstructions"] = data["AlignedShowingURL"]
    data_json["office"] = {}
    data_json["office"]["contact"] = data["ListOfficeKeyNumeric"]
    data_json["office"]["name"] = data["ListOfficeName"]
    data_json["office"]["servingName"] = data["ListOfficeName"]
    data_json["office"]["brokerid"] = data["ListOfficeMlsId"]
    data_json["leaseTerm"] = data["LeaseTerm"]
    data_json["disclaimer"] = data["Disclaimer"]
    data_json["specialListingConditions"] = data["SpecialListingConditions"]
    data_json["originalListPrice"] = data["ListPrice"]
    data_json["address"] = {}
    data_json["address"]["crossStreet"] = data["CrossStreet"]
    data_json["address"]["state"] = data["StateOrProvince"]
    data_json["address"]["country"] = data["Country"]
    data_json["address"]["postalCode"] = data["PostalCode"]
    data_json["address"]["streetName"] = data["StreetName"]
    data_json["address"]["streetNumberText"] = data["StreetNumber"]
    data_json["address"]["city"] = data["City"]
    data_json["address"]["streetNumber"] = data["StreetNumber"]
    data_json["address"]["full"] = data["UnparsedAddress"]
    data_json["address"]["unit"] = "null"
    data_json["agreement"] = data["ListingAgreement"]
    data_json["listDate"] = data["ListingContractDate"]
    data_json["agent"] = {}
    data_json["agent"]["officeMlsId"] = data["ListAgentMlsId"]
    data_json["agent"]["lastName"] = data["ListAgentLastName"]
    data_json["agent"]["contact"] = {}
    data_json["agent"]["contact"]["email"] = data["ListAgentEmail"]
    data_json["agent"]["contact"]["office"] = data["ListAgentPreferredPhone"]
    data_json["agent"]["contact"]["cell"] = data["CoListAgentPreferredPhone"]
    data_json["agent"]["address"] = data["Location"]
    data_json["agent"]["modified"] = "null"
    data_json["agent"]["firstName"] = data["ListAgentFirstName"]
    data_json["agent"]["id"] = data["ListAgentKeyNumeric"]
    data_json["modified"] = "null"
    data_json["school"] = {}
    data_json["school"]["middleSchool"] = data["MiddleOrJuniorSchool"]
    data_json["school"]["highSchool"] = data["HighSchool"]
    data_json["school"]["elementarySchool"] = data["ElementarySchool"]
    data_json["school"]["district"] = data["ElementarySchoolDistrict"]
    data_json["listPrice"] = data["ListPrice"]
    data_json["internetAddressDisplay"] = data["InternetAddressDisplayYN"]
    data_json["listingId"] = data["ListingId"]
    data_json["mls"] = {}
    data_json["mls"]["status"] = data["MlsStatus"]
    data_json["mls"]["area"] = data["MLSAreaMajor"]
    data_json["mls"]["daysOnMarket"] = "null"
    data_json["mls"]["originalEntryTimestamp"] = data["MiddleOrJuniorSchool"]
    data_json["mls"]["originatingSystemName"] = data["OriginatingSystemName"]
    data_json["mls"]["statusText"] = data["StandardStatus"]
    data_json["mls"]["areaMinor"] = "null"
    data_json["internetEntireListingDisplay"] = data[
        "InternetEntireListingDisplayYN"
    ]
    data_json["geo"] = {}
    data_json["geo"]["county"] = data["CountyOrParish"]
    data_json["geo"]["lat"] = data["Latitude"]
    data_json["geo"]["lng"] = data["Longitude"]
    data_json["geo"]["marketArea"] = data["MLSAreaMajor"]
    data_json["geo"]["directions"] = data["Directions"]
    data_json["tax"] = {}
    data_json["tax"]["taxYear"] = "null"
    data_json["tax"]["taxAnnualAmount"] = data["TaxAnnualAmount"]
    data_json["tax"]["id"] = data["ListAgentPreferredPhone"]
    data_json["coAgent"] = {}
    data_json["coAgent"]["officeMlsId"] = data["CoBuyerOfficeMlsId"]
    data_json["coAgent"]["lastName"] = data["CoBuyerAgentLastName"]
    data_json["coAgent"]["contact"] = {}
    data_json["coAgent"]["contact"]["email"] = "null"
    data_json["coAgent"]["contact"]["office"] = data["CoBuyerOfficeKeyNumeric"]
    data_json["coAgent"]["contact"]["cell"] = "null"
    data_json["coAgent"]["address"] = data["CoBuyerOfficeName"]
    data_json["coAgent"]["modified"] = "null"
    data_json["coAgent"]["firstName"] = data["CoBuyerAgentFirstName"]
    data_json["coAgent"]["id"] = data["CoBuyerAgentMlsId"]
    data_json["sales"] = "...."
    data_json["ownership"] = data["Ownership"]
    data_json["leaseType"] = data["ExistingLeaseType"]
    data_json["virtualTourUrl"] = data["VirtualTourURLBranded"]
    data_json["openHouseSchedule"] = data["OpenHouseSchedule"]
    data_json["remarks"] = data["PublicRemarks"]
    data_json["closeDate"] = data["CloseDate"]
    data_json["closePrice"] = data["ClosePrice"]
    data_json["listing"] = "mls"

    try:
        get_photo_list = [photo["MediaURL"] for photo in data["Media"]]
        photo_list = get_photo_list
    except Exception:
        photo_list = []
    data_json["photos"] = photo_list

    return data_json


def mls_data_formater1(datas):
    objects = []
    for data in datas:
        data_json = {}
        data_json["property"] = {}
        data_json["property"]["roof"] = data["Roof"]
        data_json["property"]["cooling"] = data["Cooling"]
        data_json["property"]["style"] = data["ArchitecturalStyle"]
        data_json["property"]["area"] = data["LivingArea"]
        data_json["property"]["bathsFull"] = data["BathroomsFull"]
        data_json["property"]["bathsHalf"] = data["BathroomsHalf"]
        data_json["property"]["stories"] = data["StoriesTotal"]
        data_json["property"]["fireplaces"] = data["FireplacesTotal"]
        data_json["property"]["flooring"] = data["Flooring"]
        data_json["property"]["heating"] = data["Heating"]
        data_json["property"]["bathrooms"] = data["BathroomsFull"]
        data_json["property"]["foundation"] = data["FoundationDetails"]
        data_json["property"]["laundryFeatures"] = data["LaundryFeatures"]
        data_json["property"]["occupantName"] = data["BuilderName"]
        data_json["property"]["ownerName"] = data["Ownership"]
        data_json["property"]["lotDescription"] = data["LotDimensionsSource"]
        data_json["property"]["pool"] = data["PoolFeatures"]
        data_json["property"]["subType"] = data["PropertySubType"]
        data_json["property"]["bedrooms"] = data["BedroomsTotal"]
        data_json["property"]["interiorFeatures"] = data["InteriorFeatures"]
        data_json["property"]["lotSize"] = data["LotSizeSquareFeet"]
        data_json["property"]["areaSource"] = data["BuildingAreaSource"]
        data_json["property"]["maintenanceExpense"] = "null"
        data_json["property"]["additionalRooms"] = "null"
        data_json["property"]["exteriorFeatures"] = data["ExteriorFeatures"]
        data_json["property"]["water"] = data["WaterSource"]
        data_json["property"]["view"] = data["View"]
        data_json["property"]["lotSizeArea"] = data["LotSizeSquareFeet"]
        data_json["property"]["subdivision"] = data["SubdivisionName"]
        data_json["property"]["construction"] = data["ConstructionMaterials"]
        data_json["property"]["parking"] = {}
        data_json["property"]["parking"]["leased"] = data["NumberOfUnitsLeased"]
        data_json["property"]["parking"]["spaces"] = data["OpenParkingSpaces"]
        data_json["property"]["parking"]["description"] = data["OriginatingSystemName"]
        data_json["property"]["lotSizeAreaUnits"] = "SquareFeet"
        data_json["property"]["type"] = data["PropertyType"]
        data_json["property"]["garageSpaces"] = data["GarageSpaces"]
        data_json["property"]["bathsThreeQuarter"] = data["BathroomsThreeQuarter"]
        data_json["property"]["accessibility"] = data["AccessibilityFeatures"]
        data_json["property"]["acres"] = data["LotSizeAcres"]
        data_json["property"]["occupantType"] = data["OccupantType"]
        data_json["property"]["subTypeText"] = data["PropertySubType"]
        data_json["property"]["yearBuilt"] = data["YearBuilt"]
        data_json["property"]["horseYN"] = data["HorseYN"]
        data_json["property"]["sewer"] = data["Sewer"]
        data_json["property"]["zoning"] = data["Zoning"]
        data_json["property"]["utilities"] = data["Utilities"]
        data_json["mlsId"] = data["ListingKeyNumeric"]
        data_json["showingContactPhone"] = data["Telephone"]
        data_json["terms"] = data["ListingTerms"]
        data_json["showingInstructions"] = data["AlignedShowingURL"]
        data_json["office"] = {}
        data_json["office"]["contact"] = data["ListOfficeKeyNumeric"]
        data_json["office"]["name"] = data["ListOfficeName"]
        data_json["office"]["servingName"] = data["ListOfficeName"]
        data_json["office"]["brokerid"] = data["ListOfficeMlsId"]
        data_json["leaseTerm"] = data["LeaseTerm"]
        data_json["disclaimer"] = data["Disclaimer"]
        data_json["specialListingConditions"] = data["SpecialListingConditions"]
        data_json["originalListPrice"] = data["ListPrice"]
        data_json["address"] = {}
        data_json["address"]["crossStreet"] = data["CrossStreet"]
        data_json["address"]["state"] = data["StateOrProvince"]
        data_json["address"]["country"] = data["Country"]
        data_json["address"]["postalCode"] = data["PostalCode"]
        data_json["address"]["streetName"] = data["StreetName"]
        data_json["address"]["streetNumberText"] = data["StreetNumber"]
        data_json["address"]["city"] = data["City"]
        data_json["address"]["streetNumber"] = data["StreetNumber"]
        data_json["address"]["full"] = data["UnparsedAddress"]
        data_json["address"]["unit"] = "null"
        data_json["agreement"] = data["ListingAgreement"]
        data_json["listDate"] = data["ListingContractDate"]
        data_json["agent"] = {}
        data_json["agent"]["officeMlsId"] = data["ListAgentMlsId"]
        data_json["agent"]["lastName"] = data["ListAgentLastName"]
        data_json["agent"]["contact"] = {}
        data_json["agent"]["contact"]["email"] = data["ListAgentEmail"]
        data_json["agent"]["contact"]["office"] = data["ListAgentPreferredPhone"]
        data_json["agent"]["contact"]["cell"] = data["CoListAgentPreferredPhone"]
        data_json["agent"]["address"] = data["Location"]
        data_json["agent"]["modified"] = "null"
        data_json["agent"]["firstName"] = data["ListAgentFirstName"]
        data_json["agent"]["id"] = data["ListAgentKeyNumeric"]
        data_json["modified"] = "null"
        data_json["school"] = {}
        data_json["school"]["middleSchool"] = data["MiddleOrJuniorSchool"]
        data_json["school"]["highSchool"] = data["HighSchool"]
        data_json["school"]["elementarySchool"] = data["ElementarySchool"]
        data_json["school"]["district"] = data["ElementarySchoolDistrict"]
        data_json["listPrice"] = data["ListPrice"]
        data_json["internetAddressDisplay"] = data["InternetAddressDisplayYN"]
        data_json["listingId"] = data["ListingId"]
        data_json["mls"] = {}
        data_json["mls"]["status"] = data["MlsStatus"]
        data_json["mls"]["area"] = data["MLSAreaMajor"]
        data_json["mls"]["daysOnMarket"] = "null"
        data_json["mls"]["originalEntryTimestamp"] = data["MiddleOrJuniorSchool"]
        data_json["mls"]["originatingSystemName"] = data["OriginatingSystemName"]
        data_json["mls"]["statusText"] = data["StandardStatus"]
        data_json["mls"]["areaMinor"] = "null"
        data_json["internetEntireListingDisplay"] = data[
            "InternetEntireListingDisplayYN"
        ]
        data_json["geo"] = {}
        data_json["geo"]["county"] = data["CountyOrParish"]
        data_json["geo"]["lat"] = data["Latitude"]
        data_json["geo"]["lng"] = data["Longitude"]
        data_json["geo"]["marketArea"] = data["MLSAreaMajor"]
        data_json["geo"]["directions"] = data["Directions"]
        data_json["tax"] = {}
        data_json["tax"]["taxYear"] = "null"
        data_json["tax"]["taxAnnualAmount"] = data["TaxAnnualAmount"]
        data_json["tax"]["id"] = data["ListAgentPreferredPhone"]
        data_json["coAgent"] = {}
        data_json["coAgent"]["officeMlsId"] = data["CoBuyerOfficeMlsId"]
        data_json["coAgent"]["lastName"] = data["CoBuyerAgentLastName"]
        data_json["coAgent"]["contact"] = {}
        data_json["coAgent"]["contact"]["email"] = "null"
        data_json["coAgent"]["contact"]["office"] = data["CoBuyerOfficeKeyNumeric"]
        data_json["coAgent"]["contact"]["cell"] = "null"
        data_json["coAgent"]["address"] = data["CoBuyerOfficeName"]
        data_json["coAgent"]["modified"] = "null"
        data_json["coAgent"]["firstName"] = data["CoBuyerAgentFirstName"]
        data_json["coAgent"]["id"] = data["CoBuyerAgentMlsId"]
        data_json["sales"] = "...."
        data_json["ownership"] = data["Ownership"]
        data_json["leaseType"] = data["ExistingLeaseType"]
        data_json["virtualTourUrl"] = data["VirtualTourURLBranded"]
        data_json["openHouseSchedule"] = data["OpenHouseSchedule"]
        data_json["remarks"] = data["PublicRemarks"]
        data_json["closeDate"] = data["CloseDate"]
        data_json["closePrice"] = data["ClosePrice"]
        data_json["listing"] = "mls"
        

        try:
            get_photo_list = [photo["MediaURL"] for photo in data["Media"]]
            photo_list = get_photo_list

            # get_photo_list = [photo["MediaURL"] for photo in data["Media"]]
            # get_high_resolution = get_high_resolution_image(get_photo_list)
            # photo_list = get_high_resolution
        except Exception:
            photo_list = []
        data_json["photos"] = photo_list

        objects.append(data_json)
    return objects


def open_house_mls_data_formater(datas):
    objects = []
    for data in datas:
        data_json = {}
        data_json["property"] = {}
        data_json["property"]["roof"] = data["Roof"]
        data_json["property"]["cooling"] = data["Cooling"]
        data_json["property"]["style"] = data["ArchitecturalStyle"]
        data_json["property"]["area"] = data["LivingArea"]
        data_json["property"]["bathsFull"] = data["BathroomsFull"]
        data_json["property"]["bathsHalf"] = data["BathroomsHalf"]
        data_json["property"]["stories"] = data["StoriesTotal"]
        data_json["property"]["fireplaces"] = data["FireplacesTotal"]
        data_json["property"]["flooring"] = data["Flooring"]
        data_json["property"]["heating"] = data["Heating"]
        data_json["property"]["bathrooms"] = data["BathroomsFull"]
        data_json["property"]["foundation"] = data["FoundationDetails"]
        data_json["property"]["laundryFeatures"] = data["LaundryFeatures"]
        data_json["property"]["occupantName"] = data["BuilderName"]
        data_json["property"]["ownerName"] = data["Ownership"]
        data_json["property"]["lotDescription"] = data["LotDimensionsSource"]
        data_json["property"]["pool"] = data["PoolFeatures"]
        data_json["property"]["subType"] = data["PropertySubType"]
        data_json["property"]["bedrooms"] = data["BedroomsTotal"]
        data_json["property"]["interiorFeatures"] = data["InteriorFeatures"]
        data_json["property"]["lotSize"] = data["LotSizeSquareFeet"]
        data_json["property"]["areaSource"] = data["BuildingAreaSource"]
        data_json["property"]["maintenanceExpense"] = "null"
        data_json["property"]["additionalRooms"] = "null"
        data_json["property"]["exteriorFeatures"] = data["ExteriorFeatures"]
        data_json["property"]["water"] = data["WaterSource"]
        data_json["property"]["view"] = data["View"]
        data_json["property"]["lotSizeArea"] = data["LotSizeSquareFeet"]
        data_json["property"]["subdivision"] = data["SubdivisionName"]
        data_json["property"]["construction"] = data["ConstructionMaterials"]
        data_json["property"]["parking"] = {}
        data_json["property"]["parking"]["leased"] = data["NumberOfUnitsLeased"]
        data_json["property"]["parking"]["spaces"] = data["OpenParkingSpaces"]
        data_json["property"]["parking"]["description"] = data["OriginatingSystemName"]
        data_json["property"]["lotSizeAreaUnits"] = "SquareFeet"
        data_json["property"]["type"] = data["PropertyType"]
        data_json["property"]["garageSpaces"] = data["GarageSpaces"]
        data_json["property"]["bathsThreeQuarter"] = data["BathroomsThreeQuarter"]
        data_json["property"]["accessibility"] = data["AccessibilityFeatures"]
        data_json["property"]["acres"] = data["LotSizeAcres"]
        data_json["property"]["occupantType"] = data["OccupantType"]
        data_json["property"]["subTypeText"] = data["PropertySubType"]
        data_json["property"]["yearBuilt"] = data["YearBuilt"]
        data_json["property"]["horseYN"] = data["HorseYN"]
        data_json["property"]["sewer"] = data["Sewer"]
        data_json["property"]["zoning"] = data["Zoning"]
        data_json["property"]["utilities"] = data["Utilities"]
        data_json["mlsId"] = data["ListingKeyNumeric"]
        data_json["showingContactPhone"] = data["Telephone"]
        data_json["terms"] = data["ListingTerms"]
        data_json["showingInstructions"] = data["AlignedShowingURL"]
        data_json["office"] = {}
        data_json["office"]["contact"] = data["ListOfficeKeyNumeric"]
        data_json["office"]["name"] = data["ListOfficeName"]
        data_json["office"]["servingName"] = data["ListOfficeName"]
        data_json["office"]["brokerid"] = data["ListOfficeMlsId"]
        data_json["leaseTerm"] = data["LeaseTerm"]
        data_json["disclaimer"] = data["Disclaimer"]
        data_json["specialListingConditions"] = data["SpecialListingConditions"]
        data_json["originalListPrice"] = data["ListPrice"]
        data_json["address"] = {}
        data_json["address"]["crossStreet"] = data["CrossStreet"]
        data_json["address"]["state"] = data["StateOrProvince"]
        data_json["address"]["country"] = data["Country"]
        data_json["address"]["postalCode"] = data["PostalCode"]
        data_json["address"]["streetName"] = data["StreetName"]
        data_json["address"]["streetNumberText"] = data["StreetNumber"]
        data_json["address"]["city"] = data["City"]
        data_json["address"]["streetNumber"] = data["StreetNumber"]
        data_json["address"]["full"] = data["UnparsedAddress"]
        data_json["address"]["unit"] = "null"
        data_json["agreement"] = data["ListingAgreement"]
        data_json["listDate"] = data["ListingContractDate"]
        data_json["agent"] = {}
        data_json["agent"]["officeMlsId"] = data["ListAgentMlsId"]
        data_json["agent"]["lastName"] = data["ListAgentLastName"]
        data_json["agent"]["contact"] = {}
        data_json["agent"]["contact"]["email"] = data["ListAgentEmail"]
        data_json["agent"]["contact"]["office"] = data["ListAgentPreferredPhone"]
        data_json["agent"]["contact"]["cell"] = data["CoListAgentPreferredPhone"]
        data_json["agent"]["address"] = data["Location"]
        data_json["agent"]["modified"] = "null"
        data_json["agent"]["firstName"] = data["ListAgentFirstName"]
        data_json["agent"]["id"] = data["ListAgentKeyNumeric"]
        data_json["modified"] = "null"
        data_json["school"] = {}
        data_json["school"]["middleSchool"] = data["MiddleOrJuniorSchool"]
        data_json["school"]["highSchool"] = data["HighSchool"]
        data_json["school"]["elementarySchool"] = data["ElementarySchool"]
        data_json["school"]["district"] = data["ElementarySchoolDistrict"]
        data_json["listPrice"] = data["ListPrice"]
        data_json["internetAddressDisplay"] = data["InternetAddressDisplayYN"]
        data_json["listingId"] = data["ListingId"]
        data_json["mls"] = {}
        data_json["mls"]["status"] = data["MlsStatus"]
        data_json["mls"]["area"] = data["MLSAreaMajor"]
        data_json["mls"]["daysOnMarket"] = "null"
        data_json["mls"]["originalEntryTimestamp"] = data["MiddleOrJuniorSchool"]
        data_json["mls"]["originatingSystemName"] = data["OriginatingSystemName"]
        data_json["mls"]["statusText"] = data["StandardStatus"]
        data_json["mls"]["areaMinor"] = "null"
        data_json["internetEntireListingDisplay"] = data[
            "InternetEntireListingDisplayYN"
        ]
        data_json["geo"] = {}
        data_json["geo"]["county"] = data["CountyOrParish"]
        data_json["geo"]["lat"] = data["Latitude"]
        data_json["geo"]["lng"] = data["Longitude"]
        data_json["geo"]["marketArea"] = data["MLSAreaMajor"]
        data_json["geo"]["directions"] = data["Directions"]
        data_json["tax"] = {}
        data_json["tax"]["taxYear"] = "null"
        data_json["tax"]["taxAnnualAmount"] = data["TaxAnnualAmount"]
        data_json["tax"]["id"] = data["ListAgentPreferredPhone"]
        data_json["coAgent"] = {}
        data_json["coAgent"]["officeMlsId"] = data["CoBuyerOfficeMlsId"]
        data_json["coAgent"]["lastName"] = data["CoBuyerAgentLastName"]
        data_json["coAgent"]["contact"] = {}
        data_json["coAgent"]["contact"]["email"] = "null"
        data_json["coAgent"]["contact"]["office"] = data["CoBuyerOfficeKeyNumeric"]
        data_json["coAgent"]["contact"]["cell"] = "null"
        data_json["coAgent"]["address"] = data["CoBuyerOfficeName"]
        data_json["coAgent"]["modified"] = "null"
        data_json["coAgent"]["firstName"] = data["CoBuyerAgentFirstName"]
        data_json["coAgent"]["id"] = data["CoBuyerAgentMlsId"]
        data_json["sales"] = "...."
        data_json["ownership"] = data["Ownership"]
        data_json["leaseType"] = data["ExistingLeaseType"]
        data_json["virtualTourUrl"] = data["VirtualTourURLBranded"]
        data_json["openHouseSchedule"] = data["OpenHouseSchedule"]
        data_json["remarks"] = data["PublicRemarks"]
        data_json["listing"] = "mls"

        open_house = json.loads(data["OpenHouseSchedule"])

        data_json["openHouse"] = {}
        data_json["openHouse"]["date"] = open_house[0]["OHDateTime"]
        data_json["openHouse"]["hostedBy"] = open_house[0]["OHHostedBy"]
        data_json["openHouse"]["id"] = open_house[0]["OHOpenHouseID"]

        try:
            get_photo_list = [photo["MediaURL"] for photo in data["Media"]]
            photo_list = get_photo_list

            # get_photo_list = [photo["MediaURL"] for photo in data["Media"]]
            # get_high_resolution = get_high_resolution_image(get_photo_list)
            # photo_list = get_high_resolution
        except Exception:
            photo_list = []
        data_json["photos"] = photo_list

        objects.append(data_json)
    return objects


def crmls_data_formater(datas):
    objects = []
    for data in datas:
        data_json = {}
        data_json["property"] = {}
        data_json["property"]["roof"] = data["Roof"]
        data_json["property"]["cooling"] = data["Cooling"]
        data_json["property"]["style"] = data["ArchitecturalStyle"]
        data_json["property"]["area"] = data["LivingArea"]
        data_json["property"]["bathsFull"] = data["BathroomsFull"]
        data_json["property"]["bathsHalf"] = data["BathroomsHalf"]
        data_json["property"]["stories"] = data["StoriesTotal"]
        data_json["property"]["fireplaces"] = data["FireplacesTotal"]
        data_json["property"]["flooring"] = data["Flooring"]
        data_json["property"]["heating"] = data["Heating"]
        data_json["property"]["bathrooms"] = data["BathroomsFull"]
        data_json["property"]["foundation"] = data["FoundationDetails"]
        data_json["property"]["laundryFeatures"] = data["LaundryFeatures"]
        data_json["property"]["occupantName"] = data["BuilderName"]
        data_json["property"]["ownerName"] = data["Ownership"]
        data_json["property"]["lotDescription"] = data["LotDimensionsSource"]
        data_json["property"]["pool"] = data["PoolFeatures"]
        data_json["property"]["subType"] = data["PropertySubType"]
        data_json["property"]["bedrooms"] = data["BedroomsTotal"]
        data_json["property"]["interiorFeatures"] = data["InteriorFeatures"]
        data_json["property"]["lotSize"] = data["LotSizeSquareFeet"]
        data_json["property"]["areaSource"] = data["BuildingAreaSource"]
        data_json["property"]["maintenanceExpense"] = "null"
        data_json["property"]["additionalRooms"] = "null"
        data_json["property"]["exteriorFeatures"] = data["ExteriorFeatures"]
        data_json["property"]["water"] = data["WaterSource"]
        data_json["property"]["view"] = data["View"]
        data_json["property"]["lotSizeArea"] = data["LotSizeSquareFeet"]
        data_json["property"]["subdivision"] = data["SubdivisionName"]
        data_json["property"]["construction"] = data["ConstructionMaterials"]
        data_json["property"]["parking"] = {}
        data_json["property"]["parking"]["leased"] = data["NumberOfUnitsLeased"]
        data_json["property"]["parking"]["spaces"] = data["OpenParkingSpaces"]
        data_json["property"]["parking"]["description"] = data["OriginatingSystemName"]
        data_json["property"]["lotSizeAreaUnits"] = "SquareFeet"
        data_json["property"]["type"] = data["PropertyType"]
        data_json["property"]["garageSpaces"] = data["GarageSpaces"]
        data_json["property"]["bathsThreeQuarter"] = data["BathroomsThreeQuarter"]
        data_json["property"]["accessibility"] = data["AccessibilityFeatures"]
        data_json["property"]["acres"] = data["LotSizeAcres"]
        data_json["property"]["occupantType"] = data["OccupantType"]
        data_json["property"]["subTypeText"] = data["PropertySubType"]
        data_json["property"]["yearBuilt"] = data["YearBuilt"]
        data_json["property"]["horseYN"] = data["HorseYN"]
        data_json["property"]["sewer"] = data["Sewer"]
        data_json["property"]["zoning"] = data["Zoning"]
        data_json["property"]["utilities"] = data["Utilities"]

        data_json["mlsId"] = data["ListingKeyNumeric"]
        data_json["showingContactPhone"] = data["ShowingContactPhone"]
        data_json["terms"] = data["ListingTerms"]
        data_json["showingInstructions"] = data["ShowingInstructions"]
        data_json["office"] = {}
        data_json["office"]["contact"] = data["ListOfficeKeyNumeric"]
        data_json["office"]["name"] = data["ListOfficeName"]
        data_json["office"]["servingName"] = data["ListOfficeName"]
        data_json["office"]["brokerid"] = data["ListOfficeMlsId"]
        data_json["leaseTerm"] = data["LeaseTerm"]
        data_json["disclaimer"] = data["Disclaimer"]
        data_json["specialListingConditions"] = data["SpecialListingConditions"]
        data_json["originalListPrice"] = data["ListPrice"]
        data_json["address"] = {}
        data_json["address"]["crossStreet"] = data["CrossStreet"]
        data_json["address"]["state"] = data["StateOrProvince"]
        data_json["address"]["country"] = data["Country"]
        data_json["address"]["postalCode"] = data["PostalCode"]
        data_json["address"]["streetName"] = data["StreetName"]
        data_json["address"]["streetNumberText"] = data["StreetNumber"]
        data_json["address"]["city"] = data["City"]
        data_json["address"]["streetNumber"] = data["StreetNumber"]
        data_json["address"]["full"] = data["UnparsedAddress"]
        data_json["address"]["unit"] = "null"
        data_json["agreement"] = data["ListingAgreement"]
        data_json["listDate"] = data["ListingContractDate"]
        data_json["agent"] = {}
        data_json["agent"]["officeMlsId"] = data["ListAgentMlsId"]
        data_json["agent"]["lastName"] = data["ListAgentLastName"]
        data_json["agent"]["contact"] = {}
        data_json["agent"]["contact"]["email"] = data["ListAgentEmail"]
        data_json["agent"]["contact"]["office"] = data["ListAgentPreferredPhone"]
        data_json["agent"]["contact"]["cell"] = data["CoListAgentPreferredPhone"]
        data_json["agent"]["address"] = data["EntryLocation"]
        data_json["agent"]["modified"] = "null"
        data_json["agent"]["firstName"] = data["ListAgentFirstName"]
        data_json["agent"]["id"] = data["ListAgentKeyNumeric"]
        data_json["modified"] = "null"
        data_json["school"] = {}
        data_json["school"]["middleSchool"] = data["MiddleOrJuniorSchool"]
        data_json["school"]["highSchool"] = data["HighSchool"]
        data_json["school"]["elementarySchool"] = data["ElementarySchool"]
        data_json["school"]["district"] = data["ElementarySchoolDistrict"]
        data_json["listPrice"] = data["ListPrice"]
        data_json["internetAddressDisplay"] = data["InternetAddressDisplayYN"]
        data_json["listingId"] = data["ListingId"]
        data_json["mls"] = {}
        data_json["mls"]["status"] = data["MlsStatus"]
        data_json["mls"]["area"] = data["MLSAreaMajor"]
        data_json["mls"]["daysOnMarket"] = "null"
        data_json["mls"]["originalEntryTimestamp"] = data["MiddleOrJuniorSchool"]
        data_json["mls"]["originatingSystemName"] = data["OriginatingSystemName"]
        data_json["mls"]["statusText"] = data["StandardStatus"]
        data_json["mls"]["areaMinor"] = "null"
        data_json["internetEntireListingDisplay"] = data[
            "InternetEntireListingDisplayYN"
        ]
        data_json["geo"] = {}
        data_json["geo"]["county"] = data["CountyOrParish"]
        data_json["geo"]["lat"] = data["Latitude"]
        data_json["geo"]["lng"] = data["Longitude"]
        data_json["geo"]["marketArea"] = data["MLSAreaMajor"]
        data_json["geo"]["directions"] = data["Directions"]
        data_json["tax"] = {}
        data_json["tax"]["taxYear"] = "null"
        data_json["tax"]["taxAnnualAmount"] = data["TaxAnnualAmount"]
        data_json["tax"]["id"] = data["ListAgentPreferredPhone"]
        data_json["coAgent"] = {}
        data_json["coAgent"]["officeMlsId"] = data["CoBuyerOfficeMlsId"]
        data_json["coAgent"]["lastName"] = data["CoBuyerAgentLastName"]
        data_json["coAgent"]["contact"] = {}
        data_json["coAgent"]["contact"]["email"] = "null"
        data_json["coAgent"]["contact"]["office"] = data["CoBuyerOfficeKeyNumeric"]
        data_json["coAgent"]["contact"]["cell"] = "null"
        data_json["coAgent"]["address"] = data["CoBuyerOfficeName"]
        data_json["coAgent"]["modified"] = "null"
        data_json["coAgent"]["firstName"] = data["CoBuyerAgentFirstName"]
        data_json["coAgent"]["id"] = data["CoBuyerAgentMlsId"]
        data_json["sales"] = "...."
        data_json["ownership"] = data["Ownership"]
        data_json["leaseType"] = data["ExistingLeaseType"]
        data_json["virtualTourUrl"] = data["VirtualTourURLBranded"]
        data_json["remarks"] = data["PublicRemarks"]
        data_json["listing"] = "crmls"
        try:
            get_photo_list = [photo["MediaURL"] for photo in data["Media"]]
            photo_list = get_photo_list

            # get_photo_list = [photo["MediaURL"] for photo in data["Media"]]
            # get_high_resolution = get_high_resolution_image(get_photo_list)
            # photo_list = get_high_resolution
        except Exception:
            photo_list = []
        data_json["photos"] = photo_list

        objects.append(data_json)
    return objects


def open_house_crmls_data_formater(datas):
    objects = []
    for data in datas:
        data_json = {}
        data_json["property"] = {}
        data_json["property"]["roof"] = data["Property"][0]["Roof"]
        data_json["property"]["cooling"] = data["Property"][0]["Cooling"]
        data_json["property"]["style"] = data["Property"][0]["ArchitecturalStyle"]
        data_json["property"]["area"] = data["Property"][0]["LivingArea"]
        data_json["property"]["bathsFull"] = data["Property"][0]["BathroomsFull"]
        data_json["property"]["bathsHalf"] = data["Property"][0]["BathroomsHalf"]
        data_json["property"]["stories"] = data["Property"][0]["StoriesTotal"]
        data_json["property"]["fireplaces"] = data["Property"][0]["FireplacesTotal"]
        data_json["property"]["flooring"] = data["Property"][0]["Flooring"]
        data_json["property"]["heating"] = data["Property"][0]["Heating"]
        data_json["property"]["bathrooms"] = data["Property"][0]["BathroomsFull"]
        data_json["property"]["foundation"] = data["Property"][0][
            "FoundationDetails"
        ]
        data_json["property"]["laundryFeatures"] = data["Property"][0][
            "LaundryFeatures"
        ]
        data_json["property"]["occupantName"] = data["Property"][0]["BuilderName"]
        data_json["property"]["ownerName"] = data["Property"][0]["Ownership"]
        data_json["property"]["lotDescription"] = data["Property"][0][
            "LotDimensionsSource"
        ]
        data_json["property"]["pool"] = data["Property"][0]["PoolFeatures"]
        data_json["property"]["subType"] = data["Property"][0]["PropertySubType"]
        data_json["property"]["bedrooms"] = data["Property"][0]["BedroomsTotal"]
        data_json["property"]["interiorFeatures"] = data["Property"][0][
            "InteriorFeatures"
        ]
        data_json["property"]["lotSize"] = data["Property"][0]["LotSizeSquareFeet"]
        data_json["property"]["areaSource"] = data["Property"][0][
            "BuildingAreaSource"
        ]
        data_json["property"]["maintenanceExpense"] = "null"
        data_json["property"]["additionalRooms"] = "null"
        data_json["property"]["exteriorFeatures"] = data["Property"][0][
            "ExteriorFeatures"
        ]
        data_json["property"]["water"] = data["Property"][0]["WaterSource"]
        data_json["property"]["view"] = data["Property"][0]["View"]
        data_json["property"]["lotSizeArea"] = data["Property"][0][
            "LotSizeSquareFeet"
        ]
        data_json["property"]["subdivision"] = data["Property"][0][
            "SubdivisionName"
        ]
        data_json["property"]["construction"] = data["Property"][0][
            "ConstructionMaterials"
        ]
        data_json["property"]["parking"] = {}
        data_json["property"]["parking"]["leased"] = data["Property"][0][
            "NumberOfUnitsLeased"
        ]
        data_json["property"]["parking"]["spaces"] = data["Property"][0][
            "OpenParkingSpaces"
        ]
        data_json["property"]["parking"]["description"] = data["Property"][0][
            "OriginatingSystemName"
        ]
        data_json["property"]["lotSizeAreaUnits"] = "SquareFeet"
        data_json["property"]["type"] = data["Property"][0]["PropertyType"]
        data_json["property"]["garageSpaces"] = data["Property"][0]["GarageSpaces"]
        data_json["property"]["bathsThreeQuarter"] = data["Property"][0][
            "BathroomsThreeQuarter"
        ]
        data_json["property"]["accessibility"] = data["Property"][0][
            "AccessibilityFeatures"
        ]
        data_json["property"]["acres"] = data["Property"][0]["LotSizeAcres"]
        data_json["property"]["occupantType"] = data["Property"][0]["OccupantType"]
        data_json["property"]["subTypeText"] = data["Property"][0][
            "PropertySubType"
        ]
        data_json["property"]["yearBuilt"] = data["Property"][0]["YearBuilt"]
        data_json["property"]["horseYN"] = data["Property"][0]["HorseYN"]
        data_json["property"]["sewer"] = data["Property"][0]["Sewer"]
        data_json["property"]["zoning"] = data["Property"][0]["Zoning"]
        data_json["property"]["utilities"] = data["Property"][0]["Utilities"]

        data_json["mlsId"] = data["Property"][0]["ListingKeyNumeric"]
        data_json["showingContactPhone"] = data["Property"][0][
            "ShowingContactPhone"
        ]
        data_json["terms"] = data["Property"][0]["ListingTerms"]
        data_json["showingInstructions"] = data["Property"][0][
            "ShowingInstructions"
        ]
        data_json["office"] = {}
        data_json["office"]["contact"] = data["Property"][0]["ListOfficeKeyNumeric"]
        data_json["office"]["name"] = data["Property"][0]["ListOfficeName"]
        data_json["office"]["servingName"] = data["Property"][0]["ListOfficeName"]
        data_json["office"]["brokerid"] = data["Property"][0]["ListOfficeMlsId"]
        data_json["leaseTerm"] = data["Property"][0]["LeaseTerm"]
        data_json["disclaimer"] = data["Property"][0]["Disclaimer"]
        data_json["specialListingConditions"] = data["Property"][0][
            "SpecialListingConditions"
        ]
        data_json["originalListPrice"] = data["Property"][0]["ListPrice"]
        data_json["address"] = {}
        data_json["address"]["crossStreet"] = data["Property"][0]["CrossStreet"]
        data_json["address"]["state"] = data["Property"][0]["StateOrProvince"]
        data_json["address"]["country"] = data["Property"][0]["Country"]
        data_json["address"]["postalCode"] = data["Property"][0]["PostalCode"]
        data_json["address"]["streetName"] = data["Property"][0]["StreetName"]
        data_json["address"]["streetNumberText"] = data["Property"][0][
            "StreetNumber"
        ]
        data_json["address"]["city"] = data["Property"][0]["City"]
        data_json["address"]["streetNumber"] = data["Property"][0]["StreetNumber"]
        data_json["address"]["full"] = data["Property"][0]["UnparsedAddress"]
        data_json["address"]["unit"] = "null"
        data_json["agreement"] = data["Property"][0]["ListingAgreement"]
        data_json["listDate"] = data["Property"][0]["ListingContractDate"]
        data_json["agent"] = {}
        data_json["agent"]["officeMlsId"] = data["Property"][0]["ListAgentMlsId"]
        data_json["agent"]["lastName"] = data["Property"][0]["ListAgentLastName"]
        data_json["agent"]["contact"] = {}
        data_json["agent"]["contact"]["email"] = data["Property"][0][
            "ListAgentEmail"
        ]
        data_json["agent"]["contact"]["office"] = data["Property"][0][
            "ListAgentPreferredPhone"
        ]
        data_json["agent"]["contact"]["cell"] = data["Property"][0][
            "CoListAgentPreferredPhone"
        ]
        data_json["agent"]["address"] = data["Property"][0]["EntryLocation"]
        data_json["agent"]["modified"] = "null"
        data_json["agent"]["firstName"] = data["Property"][0]["ListAgentFirstName"]
        data_json["agent"]["id"] = data["Property"][0]["ListAgentKeyNumeric"]
        data_json["modified"] = "null"
        data_json["school"] = {}
        data_json["school"]["middleSchool"] = data["Property"][0][
            "MiddleOrJuniorSchool"
        ]
        data_json["school"]["highSchool"] = data["Property"][0]["HighSchool"]
        data_json["school"]["elementarySchool"] = data["Property"][0][
            "ElementarySchool"
        ]
        data_json["school"]["district"] = data["Property"][0][
            "ElementarySchoolDistrict"
        ]
        data_json["listPrice"] = data["Property"][0]["ListPrice"]
        data_json["internetAddressDisplay"] = data["Property"][0][
            "InternetAddressDisplayYN"
        ]
        data_json["listingId"] = data["Property"][0]["ListingId"]
        data_json["mls"] = {}
        data_json["mls"]["status"] = data["Property"][0]["MlsStatus"]
        data_json["mls"]["area"] = data["Property"][0]["MLSAreaMajor"]
        data_json["mls"]["daysOnMarket"] = "null"
        data_json["mls"]["originalEntryTimestamp"] = data["Property"][0][
            "MiddleOrJuniorSchool"
        ]
        data_json["mls"]["originatingSystemName"] = data["Property"][0][
            "OriginatingSystemName"
        ]
        data_json["mls"]["statusText"] = data["Property"][0]["StandardStatus"]
        data_json["mls"]["areaMinor"] = "null"
        data_json["internetEntireListingDisplay"] = data["Property"][0][
            "InternetEntireListingDisplayYN"
        ]
        data_json["geo"] = {}
        data_json["geo"]["county"] = data["Property"][0]["CountyOrParish"]
        data_json["geo"]["lat"] = data["Property"][0]["Latitude"]
        data_json["geo"]["lng"] = data["Property"][0]["Longitude"]
        data_json["geo"]["marketArea"] = data["Property"][0]["MLSAreaMajor"]
        data_json["geo"]["directions"] = data["Property"][0]["Directions"]
        data_json["tax"] = {}
        data_json["tax"]["taxYear"] = "null"
        data_json["tax"]["taxAnnualAmount"] = data["Property"][0]["TaxAnnualAmount"]
        data_json["tax"]["id"] = data["Property"][0]["ListAgentPreferredPhone"]
        data_json["coAgent"] = {}
        data_json["coAgent"]["officeMlsId"] = data["Property"][0][
            "CoBuyerOfficeMlsId"
        ]
        data_json["coAgent"]["lastName"] = data["Property"][0][
            "CoBuyerAgentLastName"
        ]
        data_json["coAgent"]["contact"] = {}
        data_json["coAgent"]["contact"]["email"] = "null"
        data_json["coAgent"]["contact"]["office"] = data["Property"][0][
            "CoBuyerOfficeKeyNumeric"
        ]
        data_json["coAgent"]["contact"]["cell"] = "null"
        data_json["coAgent"]["address"] = data["Property"][0]["CoBuyerOfficeName"]
        data_json["coAgent"]["modified"] = "null"
        data_json["coAgent"]["firstName"] = data["Property"][0][
            "CoBuyerAgentFirstName"
        ]
        data_json["coAgent"]["id"] = data["Property"][0]["CoBuyerAgentMlsId"]
        data_json["sales"] = "...."
        data_json["ownership"] = data["Property"][0]["Ownership"]
        data_json["leaseType"] = data["Property"][0]["ExistingLeaseType"]
        data_json["virtualTourUrl"] = data["Property"][0]["VirtualTourURLBranded"]
        data_json["remarks"] = data["Property"][0]["PublicRemarks"]
        data_json["listing"] = "crmls"
        data_json["openHouse"] = {}
        data_json["openHouse"]["startTime"] = data["OpenHouseStartTime"]
        data_json["openHouse"]["endTime"] = data["OpenHouseEndTime"]
        data_json["openHouse"]["date"] = data["OpenHouseDate"]
        data_json["openHouse"]["id"] = data["OpenHouseId"]

        try:
            get_photo_list = [photo["MediaURL"] for photo in data["Property"][0]["Media"]]
            photo_list = get_photo_list

            # get_photo_list = [photo["MediaURL"] for photo in data["Media"]]
            # get_high_resolution = get_high_resolution_image(get_photo_list)
            # photo_list = get_high_resolution
        except Exception:
            photo_list = []
        data_json["photos"] = photo_list

        objects.append(data_json)
    return objects
