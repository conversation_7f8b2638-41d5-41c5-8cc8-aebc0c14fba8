import json
import requests
from datetime import date, timedelta

import os
from django.core.cache import cache
from bpo_listings.utils.generics.generic_filters import (
    crmls_odata_filters,
    constant_values,
)
from bpo_listings.utils.generics.city_filters import city_filter
from bpo_listings import CRMLS_URL


def search_query(values):
    filter_query1 = "("
    for value in values:
        query = city_filter(value)
        filter_query1 += f"{crmls_odata_filters['city']} '{query}') or "
        filter_query1 += f"{crmls_odata_filters['address']} '{query}') or "
    return filter_query1


class CrmlsHandler:
    client_id = os.getenv("CRMLS_CLIENT_ID")
    client_secret = os.getenv("CRMLS_CLIENT_SECRET")

    def authenticate(self):
        endpoint_url = f"{CRMLS_URL}oidc/connect/token"
        payload = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "scope": "api",
            "grant_type": "client_credentials",
        }
        response = requests.post(
            endpoint_url,
            data=payload,
        )
        json_response = response.json()

        session_key = "crmls_access_token"
        value = json_response["access_token"]

        # Set the value in the session cache
        cache.set(session_key, value, 25200)

        return json_response["access_token"]

    def all(self, access_token, page_num=0, limit=10):
        headers = {"Authorization": f"Bearer {access_token}"}
        if page_num == 0:
            skip = 0
        else:
            skip = int(limit) * (int(page_num) - 1)
        exclude_filter = f"MlsStatus ne 'Closed' and {constant_values}"

        response = requests.get(
            f"{CRMLS_URL}odata/Property?"
            + "$filter="
            + exclude_filter
            + "&$skip="
            + str(skip)
            + "&$top="
            + str(limit)
            + "&$expand=Media"
            + "&$count=true",
            headers=headers,
        )

        response_json = response.json()
        try:
            response_json["statusCode"]
            access_token = self.authenticate()
            headers = {"Authorization": f"Bearer {access_token}"}

            response = requests.get(
                f"{CRMLS_URL}odata/Property?"
                + "$filter="
                + exclude_filter
                + "&$skip="
                + str(skip)
                + "&$top="
                + str(limit)
                + "&$expand=Media"
                + "&$count=true",
                headers=headers,
            )

        except Exception:
            pass

        return response.json()

    def all_open_house(self, access_token, page_num=0, limit=10, is_yesteday=False):
        if page_num == 0:
            skip = 0
        else:
            skip = int(limit) * (int(page_num) - 1)

        date_filter = ""

        if is_yesteday:
            yesterday = date.today() - timedelta(days=1)
            formatted_yesterday = yesterday.isoformat()
            date_filter = f"OpenHouseDate eq  {formatted_yesterday}"

        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(
            f"{CRMLS_URL}odata/OpenHouse?"
            + "$expand=Property($expand=Media)"
            + "&$skip="
            + str(skip)
            + "&$top="
            + str(limit)
            + "&$filter="
            + date_filter
            + "&$orderby=OpenHouseDate desc"
            + "&$count=true",
            headers=headers,
        )

        response_json = response.json()

        try:
            response_json["statusCode"]
            access_token = self.authenticate()
            headers = {"Authorization": f"Bearer {access_token}"}

            response = requests.get(
                f"{CRMLS_URL}odata/OpenHouse?"
                + "$expand=Property"
                + "&$skip="
                + str(skip)
                + "&$top="
                + str(limit)
                + "&$filter="
                + date_filter
                + "&$orderby=OpenHouseDate desc"
                + "&$count=true",
                headers=headers,
            )

        except Exception:
            pass
        
        return response.json()


    def all_data(self, access_token, page_num=0, limit=10, is_yesteday=False):
        headers = {"Authorization": f"Bearer {access_token}"}
        if page_num == 0:
            skip = 0
        else:
            skip = int(limit) * (int(page_num) - 1)
        exclude_filter = f"{constant_values}"

        if is_yesteday:
            yesterday = date.today() - timedelta(days=1)
            formatted_yesterday = yesterday.isoformat()
            exclude_filter = f"ListingContractDate eq {formatted_yesterday} and {constant_values}"

        response = requests.get(
            f"{CRMLS_URL}odata/Property?"
            + "$filter="
            + exclude_filter
            + "&$skip="
            + str(skip)
            + "&$top="
            + str(limit)
            + "&$expand=Media"
            + "&$orderby=ListingContractDate desc"
            + "&$count=true",
            headers=headers,
        )

        response_json = response.json()
        try:
            response_json["statusCode"]
            access_token = self.authenticate()
            headers = {"Authorization": f"Bearer {access_token}"}

            response = requests.get(
                f"{CRMLS_URL}odata/Property?"
                + "$filter="
                + exclude_filter
                + "&$skip="
                + str(skip)
                + "&$top="
                + str(limit)
                + "&$expand=Media"
                + "&$orderby=ListingContractDate desc"
                + "&$count=true",
                headers=headers,
            )

        except Exception:
            pass

        return response.json()


    def get_detail(self, access_token, id):
        filter_query = f"ListingKeyNumeric eq {id}"

        headers = {"Authorization": f"Bearer {access_token}"}

        response = requests.get(
            f"{CRMLS_URL}odata/Property?$filter=" + filter_query + "&$expand=Media",
            headers=headers,
        )

        response_json = response.json()
        try:
            response_json["statusCode"]
            access_token = self.authenticate()
            headers = {"Authorization": f"Bearer {access_token}"}

            response = requests.get(
                f"{CRMLS_URL}odata/Property?$filter=" + filter_query + "&$expand=Media",
                headers=headers,
            )

        except Exception:
            pass

        return response.json()
