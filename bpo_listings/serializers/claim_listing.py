from rest_framework import serializers
from bpo_listings.models import ClaimListing, ClaimUserRequest
from register.models.agents import Agent
from register.serializers.agents import AgentSerializer

class claimListingCreateSerializer(serializers.ModelSerializer):
    agent = serializers.SerializerMethodField(read_only=True)
    class Meta:
        model = ClaimListing
        fields = [
            "listing_id",
            "claimed",
            "offer_date_available",
            "give_authorization",
            "seller_view_offer",
            "display_offer_review_status",
            "hours_days",
            "request_review_duration",
            "display_duration_response",
            "commission_available",
            "email_commission_rate",
            "pay_buyer_broker",
            "amount_percentage",
            "total_commission_rate",
            "total_commission",
            "review_offer_date",
            "lock_box_available",
            "lock_box_type",
            "other_lock_box_type",
            "lock_box_location",
            "private_notes",
            "agent",
            "created",
            "updated",
            "listing_photo",
            "listing_url",
            "listing_address",
        ]

    def get_agent(self, obj):
        try:
            get_client = Agent.objects.get(id=obj.agent.id)
            serializer = AgentSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None

class claimListingRetrieveSerializer(serializers.ModelSerializer):
    agent = serializers.SerializerMethod<PERSON>ield(read_only=True)
    class Meta:
        model = ClaimListing
        fields = [
            "id",
            "listing_id",
            "claimed",
            "offer_date_available",
            "give_authorization",
            "seller_view_offer",
            "display_offer_review_status",
            "hours_days",
            "request_review_duration",
            "display_duration_response",
            "commission_available",
            "email_commission_rate",
            "pay_buyer_broker",
            "amount_percentage",
            "total_commission_rate",
            "total_commission",
            "review_offer_date",
            "lock_box_available",
            "lock_box_type",
            "other_lock_box_type",
            "lock_box_location",
            "private_notes",
            "agent",
            "created",
            "updated",
            "listing_photo",
            "listing_url",
            "listing_address",
        ]

    def get_agent(self, obj):
        try:
            get_client = Agent.objects.get(id=obj.agent.id)
            serializer = AgentSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None

class claimUserRequestSerializer(serializers.ModelSerializer):
    claim_listing = serializers.SerializerMethodField(read_only=True)
    agent = serializers.SerializerMethodField(read_only=True)
    class Meta:
        model = ClaimUserRequest
        fields = [
            "claim_listing",
            "first_name",
            "last_name",
            "email",
            "phone",
            "request_status",
            "created",
            "agent",
        ]
    def get_claim_listing(self, obj):
        try:
            get_client = ClaimListing.objects.get(id=obj.claim_listing.id)
            serializer = claimListingRetrieveSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None
    
    def get_agent(self, obj):
        try:
            get_client = Agent.objects.get(id=obj.agent.id)
            serializer = AgentSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None

class claimUserRetrieveSerializer(serializers.ModelSerializer):
    claim_listing = serializers.SerializerMethodField(read_only=True)
    agent = serializers.SerializerMethodField(read_only=True)
    class Meta:
        model = ClaimUserRequest
        fields = [
            "id",
            "claim_listing",
            "first_name",
            "last_name",
            "email",
            "phone",
            "request_status",
            "created",
            "agent",
        ]
    def get_claim_listing(self, obj):
        try:
            get_client = ClaimListing.objects.get(id=obj.claim_listing.id)
            serializer = claimListingRetrieveSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None
    
    def get_agent(self, obj):
        try:
            get_client = Agent.objects.get(id=obj.agent.id)
            serializer = AgentSerializer(get_client, many=False)
            return serializer.data
        except Exception:
            return None