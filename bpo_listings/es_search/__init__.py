import os
from elasticsearch import Elasticsearch
from bpohomes_backend import settings
"""_
will write a condition to handle this, 
when its called from the .env file and 
its pushed it brings an error
"""

host = settings.ELASTIC_HOST
port = settings.ELASTIC_PORT
username = settings.ELASTIC_USERNAME
password = settings.ELASTIC_PASSWORD


# Create an Elasticsearch instance with authentication
es = Elasticsearch(
    [f"{host}:{port}"],
    http_auth=(username, password),
    verify_certs=False,

)


def is_available(index, id):
    exists = es.exists(index=index, id=id)

    return exists
