from bpo_listings.es_search import es, is_available
from elasticsearch.helpers import bulk


def create_or_update_listings(formatted_data, index):
    actions = []

    for obj in formatted_data:
        doc_id = obj["mlsId"]

        # Check if document exists in Elasticsearch
        existing_doc = es.exists(index=index, id=doc_id)

        if existing_doc:
            # Prepare update action
            actions.append({
                "_op_type": "update",
                "_index": index,
                "_id": doc_id,
                "doc": obj
            })
        else:
            # Prepare index action
            actions.append({
                "_op_type": "index",
                "_index": index,
                "_id": doc_id,
                "_source": obj
            })

    if actions:
        try:
            bulk(es, actions)
            print(f"Successfully processed {len(actions)} documents.")
        except Exception as e:
            print(f"Error bulk processing: {e}")
    else:
        print("No documents to process.")

        

def create_listings_validator(formatted_data, index):
    actions = []
    for obj in formatted_data:
        action = {
            "_index": index,
            "_id": obj["mlsId"],
            "_source": obj
        }
        actions.append(action)
    
    if actions:
        try:
            bulk(es, actions)
            print(f"Successfully indexed {len(actions)} documents.")
        except Exception as e:
            print(f"Error bulk indexing: {e}")
    else:
        print("No documents to index.")



def update_listings_validator(formatted_data, index):
    for obj in formatted_data:
        document_id = obj["mlsId"]
        if is_available(index, document_id):
            update_body = {"doc": obj}
            es.update(index=index, id=document_id, body=update_body)
