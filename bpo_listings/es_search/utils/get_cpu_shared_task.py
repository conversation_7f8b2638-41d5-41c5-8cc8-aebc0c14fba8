import math


def cpu_shared_task(total, cpu):
    tasks_per_cpu = math.ceil(total / cpu)

    tasks_list = []
    start = 0

    for i in range(cpu):
        tasks = tasks_per_cpu  # Number of tasks for each CPU

        end = start + tasks
        tasks_list.append(
            [j for j in range(start, end)]
        )  # creating the inner list with tasks

        start = end  # updating the starting number

    return tasks_list
