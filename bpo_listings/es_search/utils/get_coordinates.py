import requests


def get_coordinates_from_address(address):
    try:
        response = requests.get("https://coordinates.bpohomes.com/search", params={
            "q": address,
            "format": "json",
            "limit": 1
        }, headers={"User-Agent": "YourAppName"})
        response.raise_for_status()
        data = response.json()
        if data:
            return {"lat": data[0]["lat"], "lon": data[0]["lon"]}
        else:
            return {}
    except Exception as e:
        print(f"Failed to get coordinates for {address}: {e}")
    return {}


# def add_missing_coordinates(listing):
#     geo = listing.get("geo") or {}
#     lat = geo.get("lat")
#     lng = geo.get("lng")
#     if listing.get("geo", {}).get("lat") is None or listing.get("geo", {}).get("lng") is None:
#         coordinates = get_coordinates_from_address(listing.get("address", {}).get("full"))
#         print("cordinates are", coordinates)
#         print(geo)
#         if coordinates:
#             geo["lat"] = float(coordinates.get("lat"))
#             geo["lng"] = float(coordinates.get("lon"))
#             listing["geo"] = geo  # ensure geo is updated in listing

#     return listing

def add_missing_coordinates(listing):
    if listing.get("geo", {}).get("lat") is None or listing.get("geo", {}).get("lng") is None:
        coordinates = get_coordinates_from_address(listing.get("address", {}).get("full"))
        if coordinates:
            listing["geo"]["lat"]= float(coordinates.get("lat"))
            listing["geo"]["lng"]= float(coordinates.get("lon"))

    return listing