def check_minimum_should_match(params, get_cities, should):
    if should > 0:
        if len(params) == 0:
            min = 0
        elif len(params) == 1:
            min = 0
        elif len(params) > 1:
            min = 1
            if (get_cities is not None) and (len(get_cities) == 1):
                min = 1
            if (get_cities is not None) and (len(get_cities) > 1):
                min = 1
        else:
            min = 2

        return min
    else:
        min = 0

    return min
