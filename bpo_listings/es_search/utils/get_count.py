import json
import math
from bpo_listings import ES_PAGE_LIMIT, ES_PAGE_UPDATE_LIMIT
from bpo_listings.utils.crmls_handler import Crmls<PERSON>andler
from bpo_listings.utils.mls_handler import <PERSON><PERSON><PERSON>andler
from django.core.cache import cache
from bpo_listings.es_search import es


mlshandler = MlsHandler()
crmlshandler = CrmlsHandler()
cached_value = cache.get("crmls_access_token")

"""
This function is to get count of listings
"""


def get_count(type, is_yesteday=False):
    if type == "mls":
        data = mlshandler.all(0, 1)
        data_count = data["@odata.count"]
    elif type == "all_mls":
        data = mlshandler.all_data(0, 1, is_yesteday=is_yesteday)
        data_count = data["@odata.count"]
    elif type == "all_crmls":
        authenticate = crmlshandler.authenticate()
        data = crmlshandler.all_data(authenticate, 0, 1, is_yesteday=is_yesteday)
        data_count = data["@odata.count"]

    elif type == "openhouse_mls":
        data = mlshandler.all_open_house(0, 1, is_yesteday=is_yesteday)
        data_count = data["@odata.count"]
    elif type == "crmls":
        authenticate = crmlshandler.authenticate()
        data = crmlshandler.all(authenticate, 0, 1)
        data_count = data["@odata.count"]
        
    elif type == "openhouse_crmls":
        authenticate = crmlshandler.authenticate()
        data = crmlshandler.all_open_house(authenticate, 0, 1, is_yesteday=is_yesteday)
        data_count = data["@odata.count"]
    elif type == "sold_crmls":
        authenticate = crmlshandler.authenticate()
        data = crmlshandler.all_open_house(authenticate, 0, 1)
        data_count = data["@odata.count"]

    total_pagination = math.ceil(data_count / ES_PAGE_LIMIT)

    return total_pagination


def es_get_count(type):
    if type == "mls":
        response = es.count(index="mls")
    elif type == "openhouse_mls":
        response = es.count(index="openhouse_mls")
    elif type == "sold_mls":
        response = es.count(index="sold_mls")
    elif type == "crmls":
        response = es.count(index="crmls")
    elif type == "openhouse_crmls":
        response = es.count(index="openhouse_crmls")
    elif type == "sold_crmls":
        response = es.count(index="sold_crmls")

    page_count = response["count"]
    total_pagination = math.ceil(page_count / ES_PAGE_UPDATE_LIMIT)

    return total_pagination
