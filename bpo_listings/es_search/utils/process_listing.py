from bpo_listings.models.status_list import DataSourceProgress


def get_last_processed_page(data_source):
    obj, created = DataSourceProgress.objects.get_or_create(data_source=data_source)
    return obj.last_processed_page

def update_last_processed_page(data_source, page_num):
    DataSourceProgress.objects.update_or_create(
        data_source=data_source,
        defaults={"last_processed_page": page_num}
    )