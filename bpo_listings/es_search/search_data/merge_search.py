import json
from bpo_listings.es_search.search_data.search import search_data
from bpo_listings.es_search.search_data.generate_query import generate_search_query, other_pagination_param, sort_query

"""
This function is used by the merged search to get both listings
"""


def merge_search(params, min, index, limit, page_num, type, results):

    
    query = generate_search_query(params, min, type)
    

    query = other_pagination_param(query, limit, page_num)

    data = search_data(index, query, limit, page_num, sort=None)
    objects = []
    # getting only the documents from elastic search
    documents = [
        hit.get("_source", {}) 
        for hit in data.get("hits", {}).get("hits", [])
    ]

    mls_values = {"data": documents}

    count_object = {"count": data["hits"]["total"]["value"]}

    objects.append(count_object)
    objects.append(mls_values)

    results.put(objects)



def backend_elastic_search(params, min, index, limit, page_num, type):

    
    query = {
            "query": {"bool": {
                "must_not": {
                    "exists": {
                    "field": "geo.lat"
                    }
                }
            }
            }
            }

    

    query = other_pagination_param(query, limit, page_num)

    data = search_data(index, query, limit, page_num, sort=None)

    # getting only the documents from elastic search
    documents = [
        hit.get("_source", {}) 
        for hit in data.get("hits", {}).get("hits", [])
    ]

    mls_values = {"data": documents}


    return mls_values