import requests
from bpo_listings.es_search import es
from PIL import Image
from io import BytesIO

def check_image_validity(url):
    try:
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            image = Image.open(BytesIO(response.content))
            image.verify()  # Verify the integrity of the image
            image = Image.open(BytesIO(response.content))
            image.load()  # Load the image to catch any potential issues
            return True
    except Exception:
        return False
    return False

def process_data_images(data, index):
    for item in data:
        photos = item["_source"].get("photos", [])
        if photos:
            valid_photos = [url for url in photos if check_image_validity(url)]
            if len(valid_photos) == 0:
                es.delete(index=index, id=(item["_source"]["mlsId"]))
            else:
                item["_source"]["photos"] = valid_photos
                update_body = {"doc": {"photos": valid_photos}}
                es.update(index=index, id=item["_source"]["mlsId"], body=update_body)
    return True
    
