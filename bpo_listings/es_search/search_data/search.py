from bpo_listings.es_search import es
from elasticsearch.exceptions import RequestError
"""
This function is to search in elastic search after generating search query
"""


def search_data(index, query, limit, page_num, sort=None):
    try:
        resp = es.search(index=index, body=query)
    except RequestError:
        resp = {}

    return resp


def search_data_update(index, limit, page_num):
    if page_num == 0:
        page = 0
    else:
        page = int(limit) * (int(page_num) - 1)
    resp = es.search(index=index, size=int(limit), from_=page)
    return resp