"""
This is to generate search query in elastic search
"""

import json
from bpo_listings.es_search.utils.helpers import check_minimum_should_match
from datetime import datetime, timedelta

from bpo_listings.utils.generics.city_filters import get_listing_city



def sort_query(params):
    get_status = params.get("status", None)
    sort_data = {"sort": []}

    if get_status == "sold":
        sorting = ({"closeDate": {"order": "desc"}},)
        sort_data["sort"].extend(sorting)
    else:
        sorting = ({"listDate": {"order": "desc"}},)
        sort_data["sort"].extend(sorting)

    return sort_data["sort"]



def generate_search_query(params, min, type):
    # variable to contain query for elastic search
    query = {
        "query": {"bool": {"minimum_should_match": min, "should": [], "must": []}},
    }

    get_cities = params.get("q", [])
    get_status = params.get("status", None)
    get_baths = params.get("baths", None)
    get_beds = params.get("beds", None)
    get_days = params.get("days", None)
    garage = params.get("garage", None)
    pool = params.get("pool", None)
    get_cooling = params.get("cooling", [])
    get_interior_features = params.get("interior_features", [])
    sub_type = params.get("property_sub_type", None)
    property_type = params.get("property_type", None)
    mls_residential = ["commercialsale", "residential", "residentialincome"]
    mls_rental = ["commerciallease", "residentiallease"]
    crmls_residential = [
        "commercialsale",
        "multifamily",
        "residential",
        "residentialincome",
    ]
    crmls_rental = ["commerciallease", "residentiallease"]

    # checks if get_cities is available to append list of cities(if they more than one)
    if get_cities:
        if get_cities[0][0].isdigit():
            for i in range(0, len(get_cities)):
                address = ({"match_phrase_prefix": {"address.full": get_cities[i]}},)
                # i then extend the "should" key
                query["query"]["bool"]["should"].extend(address)

                for i in range(0, len(get_cities)):
                    zipcode = (
                        {"match_phrase_prefix": {"address.postalCode": get_cities[i]}},
                    )
                    # i then extend the "should" key
                    query["query"]["bool"]["should"].extend(zipcode)
        else:
            for i in range(0, len(get_cities)):
                # previous search city = ({"match_phrase_prefix": {"address.city": get_cities[i]}},)

                city = (
                    {
                        "constant_score": {
                            "filter": {
                                "match_phrase_prefix": {"address.city": get_listing_city(get_cities[i], listing_type=type)}
                            },
                            "boost": 0.5,
                        }
                    },
                )
                # i then extend the "should" key
                query["query"]["bool"]["should"].extend(city)

    # checks if sub_type is available to append to the "should" key
    if sub_type:
        sub_query = ({"match": {"property.subTypeText": sub_type}},)
        query["query"]["bool"]["should"].extend(sub_query)

    # checks if get_status is available to append to the "should" key
    
        
    



    if garage:
        type_query = ({"match_phrase": {"property.garageSpaces": garage}},)
        query["query"]["bool"]["must"].extend(type_query)

    if pool:
        type_query = ({"match_phrase": {"property.pool": pool}},)
        query["query"]["bool"]["must"].extend(type_query)
        
    # checks if sub_type is available to append to the "should" key

    if property_type:
        if property_type == "residential" and type == "mls":
            type_query = ({"terms": {"property.type": mls_residential}},)
            query["query"]["bool"]["must"].extend(type_query)

        if property_type == "rental" and type == "mls":
            type_query = ({"terms": {"property.type": mls_rental}},)
            query["query"]["bool"]["must"].extend(type_query)

        if property_type == "residential" and type == "crmls":
            type_query = ({"terms": {"property.type": crmls_residential}},)
            query["query"]["bool"]["must"].extend(type_query)

        if property_type == "rental" and type == "crmls":
            type_query = ({"terms": {"property.type": crmls_rental}},)
            query["query"]["bool"]["must"].extend(type_query)

    if get_cooling:
        cooling_list = get_cooling.split(',')
        for cool in cooling_list:
            type_query = ({"match_phrase": {"property.cooling": cool}},)
            query["query"]["bool"]["must"].extend(type_query)


    if get_interior_features:
        interior_features_list = get_interior_features.split(',')
        for interior in interior_features_list:
            type_query = ({"match_phrase": {"property.interiorFeatures": interior}},)
            query["query"]["bool"]["must"].extend(type_query)
        
    # variable for the "must" keys
    must_queries = [
        {
            "range": {
                "listPrice": {
                    "gte": params.get("min_price", None),
                    "lte": params.get("max_price", None),
                }
            }
        },
        {
            "range": {
                "property.lotSize": {
                    "gte": params.get("min_lotsize", None),
                    "lte": params.get("max_lotsize", None),
                }
            }
        },
        {
            "range": {
                "property.lotSizeArea": {
                    "gte": params.get("min_area", None),
                    "lte": params.get("max_area", None),
                }
            }
        },
        {
            "range": {
                "property.yearBuilt": {
                    "gte": params.get("min_year", None),
                    "lte": params.get("max_year", None),
                }
            }
        },
    ]
    query["query"]["bool"]["must"].extend(must_queries)

    if get_status:
        status_list = get_status.split(",")
        # should_clauses = []

        # for status in status_list:
        #     if status == "pending" and type == "mls":
        #         status = "PendingDoNotShow"
        #     should_clauses.append({"match": {"mls.status": status}})
        
        type_query = ({"terms": {"mls.status": status_list}},)
        query["query"]["bool"]["must"].extend(type_query)
        # query["query"]["bool"]["should"].extend(should_clauses)

    if get_days:
        today = datetime.now()
        days_ago = today - timedelta(days=int(get_days))
        start_date = days_ago.strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")

        days_query = (
            {
                "range": {
                    "closeDate": {
                        "gte": start_date,
                        "lte": end_date,
                    }
                }
            },
        )

        query["query"]["bool"]["must"].extend(days_query)

    # checks if get_baths is available to append to the "must" key
    if get_baths:
        baths_query = ({"match": {"property.bathsFull": get_baths}},)
        query["query"]["bool"]["must"].extend(baths_query)

    # checks if get_beds is available to append to the "must" key
    if get_beds:
        beds_query = ({"match": {"property.bedrooms": get_beds}},)
        query["query"]["bool"]["must"].extend(beds_query)

    query_value = query["query"]
    # query["query"]["bool"]["minimum_should_match"]
    query["query"]["bool"]["minimum_should_match"] = check_minimum_should_match(
        params, get_cities, len(query["query"]["bool"]["should"])
    )


    query["sort"] = sort_query(params)

    return query



def other_pagination_param(params, limit, page_num):
    if page_num == 0:
        page = 0
    else:
        page = int(limit) * (int(page_num) - 1)
    
    params["from"] = page
    params["size"] = limit
    return params