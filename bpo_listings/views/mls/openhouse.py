from rest_framework import status, generics
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from bpo_listings import OPENHOUSE_MLS_INDEX_NAME
from bpo_listings.es_search.search_data.search import search_data
from bpo_listings.es_search.utils.get_data import get_data
from bpo_listings.utils.generics.get_request_query import request_query_elastic
from bpo_listings.utils.get_views import add_view_count


class OpenHouseMlsListings(APIView):
    permission_classes = (IsAuthenticatedOrReadOnly,)

    def get(self, request):
        page_num = self.request.GET.get("page_num", 0)
        limit = self.request.GET.get("limit", 10)

        # to filter query list and generate elastic queries
        query = request_query_elastic(self.request.GET, "none", OPENHOUSE_MLS_INDEX_NAME)

        # to search data in elastic search
        data = search_data(OPENHOUSE_MLS_INDEX_NAME, query, limit, page_num)

        try:
            data_count = data["hits"]["total"]["value"]
        except Exception:
            data_count = 0

        documents = [hit["_source"] for hit in data["hits"]["hits"]]
        return Response(
            {"count": data_count, "data": documents},
            status=status.HTTP_200_OK,
        )


class OpenHouseMlsDetail(generics.RetrieveAPIView):
    """
    An endpoint for retrieving and viewing mls detail.
    """

    permission_classes = (IsAuthenticatedOrReadOnly,)

    def get_object(self, pk):
        data = get_data(OPENHOUSE_MLS_INDEX_NAME, pk)
        data = add_view_count(data)
        return data

    def get(self, request, pk, format=None):
        try:
            get_doc = self.get_object(pk)
            return Response(
                {"data": get_doc},
                status=status.HTTP_200_OK,
            )
        except Exception:
            return Response(
                {"status": "Not Found"},
                status=status.HTTP_404_NOT_FOUND,
            )
