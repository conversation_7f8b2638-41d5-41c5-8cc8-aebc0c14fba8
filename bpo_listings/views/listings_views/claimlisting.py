from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from bpo_listings.models import ClaimListing, ClaimUserRequest
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from bpo_listings.serializers.claim_listing import (
    claimListingCreateSerializer, claimListingRetrieveSerializer, claimUserRequestSerializer, claimUserRetrieveSerializer
)
from django.core.mail import send_mail
from django.conf import settings
from register.models.agents import Agent
from register.serializers.agents import AgentSerializer
from bpo_listings.emails.agent_request_email import (
    send_agent_request_email)

class ClaimListingCreateView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, request):
        if request.user.agent.id != request.data["agent_id"]:
            return Response(
                {"error": "You cannot claim this listing. This listing belongs to another agent."},
            )
        else:
            if ClaimListing.objects.filter(listing_id=request.data["listing_id"]).exists():
                return Response(
                    {"error": "You have already claimed this listing."},
                    status=status.HTTP_200_OK,
                )
            else:
                request.data["claimed"] = True 
                serialiser = claimListingCreateSerializer(data=request.data)
                if serialiser.is_valid():
                    serialiser.save(agent=request.user.agent)
                    return Response(
                        serialiser.data,
                        status=status.HTTP_201_CREATED,
                    )
                else:
                    return Response(
                        {"error": serialiser.errors},
                    )
            
    def get(self, request):
        agent_id = request.user.agent.id

        if not agent_id:
            return Response(
                {"error": "agent_id must be provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        claim_listings = ClaimListing.objects.filter(agent_id=agent_id)
        listing_serializer = claimListingRetrieveSerializer(claim_listings, many=True)
        return Response(
            {"agent_claimed_listings": listing_serializer.data},
            status=status.HTTP_200_OK,
        )
    
class ClaimListingStatusView(APIView):

    def get(self, request):
        listing_id = request.GET.get("listing_id", None)

        if not listing_id:
            return Response(
                {"error": "listing_id must be provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            claim_listing = ClaimListing.objects.get(
                listing_id=listing_id,
            )
            return Response(
                {"listing_id": claim_listing.listing_id, 
                 "claimed": claim_listing.claimed,
                 "review_offer_date": claim_listing.review_offer_date,
                 "request_review_duration": claim_listing.request_review_duration,
                 "hours_days": claim_listing.hours_days},
                status=status.HTTP_200_OK,
            )
        except ClaimListing.DoesNotExist:
            return Response(
                {"claimed": False},
                status=status.HTTP_200_OK,
            )

class ClaimListingSingleView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, request):
        listing_id = request.GET.get("listing_id", None)
        if not listing_id:
            return Response(
                {"error": "listing_id must be provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            claim_listing = ClaimListing.objects.get(
                listing_id=listing_id,
            )
            listing_serializer = claimListingRetrieveSerializer(claim_listing)
            return Response(
                {"claimed_listing": listing_serializer.data},
                status=status.HTTP_200_OK,
            )
        except ClaimListing.DoesNotExist:
            return Response(
                {"claimed_listing": None},
            )
    
    def patch(self, request, *args, **kwargs):
        listing_id = request.data.get("claim_listing_id", None)
        if not listing_id:
            return Response(
                {"error": "Claim not found."},
                status=status.HTTP_200_OK,
            )
        try:
            listings = ClaimListing.objects.get(id=listing_id)
        except ClaimListing.DoesNotExist:
            return Response(
                {"error": "Claim listing not found in database."},
                status=status.HTTP_404_NOT_FOUND,
            )
        serializer = claimListingCreateSerializer(listings, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ClaimUserRequestView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, request):
        claim_listing_id = request.GET.get("claim_listing_id", None)
        if not claim_listing_id:
            return Response(
                {"error": "claim_listing must be provided."},
                status=status.HTTP_200_OK,
            )

        try:
            claim_user_request = ClaimUserRequest.objects.filter(
                claim_listing_id=claim_listing_id,
            )
            listing_serializer = claimUserRetrieveSerializer(claim_user_request, many=True)
            return Response(
                {"claim_user_request": listing_serializer.data},
                status=status.HTTP_200_OK,
            )
        except ClaimUserRequest.DoesNotExist:
            return Response(
                {"claim_user_request": None},
            )
    def post(self, request):
        claim_listing_id = request.data.get("claim_listing_id", None)
        if not claim_listing_id:
            return Response(
                {"error": "claim_listing_id must be provided."},
                status=status.HTTP_200_OK,
            )

        try:
            if ClaimUserRequest.objects.filter(
                claim_listing_id=claim_listing_id,
                email=request.data["email"],
                ).exists():
                    return Response(
                        {"error": "You have already requested for this listing."},
                        status=status.HTTP_200_OK,
                    )
            else:
                claim_listing = ClaimListing.objects.get(
                    id=claim_listing_id,
                )
                serializer = claimUserRequestSerializer(data=request.data, many=False)
                if serializer.is_valid():
                    serializer.save(claim_listing=claim_listing, agent=request.user.agent)
                    return Response(
                        serializer.data,
                        status=status.HTTP_201_CREATED,
                    )
                else:
                    return Response(
                        {"error": serializer.errors},
                    )
        except ClaimListing.DoesNotExist:
            return Response(
                {"error": "Claim listing not found."},
                status=status.HTTP_200_OK,
            )
        
    def patch(self, request, *args, **kwargs):
        claim_user_request_id = request.data.get("claim_user_request_id", None)
        if not claim_user_request_id:
            return Response(
                {"error": "Claim not found."},
                status=status.HTTP_200_OK,
            )
        user_request = ClaimUserRequest.objects.get(
            id=claim_user_request_id
        )
        serializer = claimUserRequestSerializer(user_request, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ClaimUserRequestEmailView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def post(self, request):
        user_request_id = request.data.get("claim_user_request_id", None)
        if not user_request_id:
            return Response(
                {"error": "claim_user_request_id must be provided."},
                status=status.HTTP_200_OK,
            )
        try:
            claim_user_request = ClaimUserRequest.objects.get(
                id=user_request_id,
            )
            #send a claim request email to the buyer
            # Compose email
            name = claim_user_request.first_name + " " + claim_user_request.last_name
            email = claim_user_request.email
            subject = "Claim Request Status"
            claim_listing_id = claim_user_request.claim_listing.id
            from_email = settings.DEFAULT_FROM_EMAIL
            # Send email
            send_mail = send_agent_request_email(
                claim_listing_id,
                name,
                email,
                subject,
                from_email,
            )
            # Check if the email was sent successfully
            if send_mail:
                # Update the request status
                claim_user_request.request_status = True
                claim_user_request.save()
                return Response(
                    {"claim_user_request": "Buyer Agent Email Sent and User request status updated."},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": "Failed to send email."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            
        except ClaimUserRequest.DoesNotExist:
            return Response(
                {"claim_user_request": None},
            )

class ClaimAgentRequestView(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, request):
        # agent_id = request.GET.get("agent_id", None)
        agent_id = request.user.agent.id
        if not agent_id:
            return Response(
                {"error": "agent_id must be provided."},
                status=status.HTTP_200_OK,
            )

        try:
            claim_user_request = ClaimUserRequest.objects.filter(
                agent_id=agent_id,
            )
            listing_serializer = claimUserRetrieveSerializer(claim_user_request, many=True)
            return Response(
                {"claim_user_request": listing_serializer.data},
                status=status.HTTP_200_OK,
            )
        except ClaimUserRequest.DoesNotExist:
            return Response(
                {"claim_user_request": None},
            )
        
    def delete(self, request):
        claim_user_request_id = request.GET.get("claim_user_request_id", None)
        if not claim_user_request_id:
            return Response(
                {"error": "claim_user_request_id must be provided."},
                status=status.HTTP_200_OK,
            )

        try:
            claim_user_request = ClaimUserRequest.objects.get(
                id=claim_user_request_id,
            )
            claim_user_request.delete()
            return Response(
                {"claim_user_request": "Claim User Request Deleted."},
                status=status.HTTP_200_OK,
            )
        except ClaimUserRequest.DoesNotExist:
            return Response(
                {"claim_user_request": None},
            )