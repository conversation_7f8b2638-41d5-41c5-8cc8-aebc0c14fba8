from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from bpo_listings.models import ListingViewCount


class ListingView(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        id = request.data.get("id", None)
        category = request.data.get("category", None)

        if ListingViewCount.objects.filter(listing_id=id, category=category).exists():
            view = ListingViewCount.objects.filter(
                listing_id=id, category=category
            ).first()
            view.count += 1
            view.save()
        else:
            ListingViewCount.objects.create(listing_id=id, category=category, count=1)

        return Response(
            {"done"},
            status=status.HTTP_200_OK,
        )
