from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from bpo_listings.models import searchQuery
from bpo_listings.utils.is_city_available import is_city_available
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut


class SearchUpload(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        search = request.data.get("search", None)

        if is_city_available(search.title()):
            if not searchQuery.objects.filter(search=search.title()).exists():
                searchQuery.objects.create(search=search.title())

        return Response(
            {"done"},
            status=status.HTTP_200_OK,
        )



class ReverseGeocodeView(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        latitude = request.data.get("latitude", None)
        longitude = request.data.get("longitude", None)

        if latitude is None or longitude is None:
            return Response(
                {"error": "Latitude and longitude must be provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            geolocator = Nominatim(user_agent="your_app_name")
            location = geolocator.reverse((latitude, longitude), timeout=10)

            if location:
                city = location.raw.get('address', {}).get('city', '')
                address = location.address
                return Response(
                    {"city": city, "address": address},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": "Location not found."},
                    status=status.HTTP_404_NOT_FOUND,
                )

        except GeocoderTimedOut:
            return Response(
                {"error": "Geocoding service timed out."},
                status=status.HTTP_504_GATEWAY_TIMEOUT,
            )

        except Exception as e:
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )