from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from bpo_listings import (
    CRMLS_SOLD_INDEX_NAME,
    SOLD_MLS_INDEX_NAME,
)
from bpo_listings.es_search.search_data.merge_search import merge_search
import threading
import queue
from bpo_listings.utils.generics.city_or_address import is_city_or_address
from bpo_listings.utils.generics.get_request_query import request_query_merge_elastic


class MergeSoldListings(APIView):
    permission_classes = (IsAuthenticatedOrReadOnly,)

    def get(self, request):
        page_num = self.request.GET.get("page_num", 0)
        limit = self.request.GET.get("limit", 10)

        # to filter query list and generate elastic queries
        new_query_list, minimum_should_match = request_query_merge_elastic(
            self.request.GET, "none"
        )

        # Used threading and queuing to run both listings parallel to each other
        q1 = queue.Queue()
        q2 = queue.Queue()

        thread1 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                SOLD_MLS_INDEX_NAME,
                limit,
                page_num,
                "none",
                q1,
            ),
        )
        thread2 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                CRMLS_SOLD_INDEX_NAME,
                limit,
                page_num,
                "none",
                q2,
            ),
        )

        thread1.start()
        thread2.start()

        # Wait for both threads to finish
        thread1.join()
        thread2.join()

        # print(q1.get())
        # print(q2.get())
        mls_results = q1.get()
        crmls_results = q2.get()

        get_crmls_count = crmls_results[0]["count"]
        get_mls_count = mls_results[0]["count"]
        total_count = get_crmls_count + get_mls_count
        try:
            mls_value = mls_results[1]["data"]
        except Exception:
            mls_value = []
        try:
            crmls_value = crmls_results[1]["data"]
        except Exception:
            crmls_value = []
        all_listings = mls_value + crmls_value
        
        q_query = self.request.GET.get("q", "None")
        query_name = is_city_or_address(q_query)

        return Response(
            {"count": total_count, "name": query_name, "data": all_listings}, status=status.HTTP_200_OK
        )


class MergeResidentialSoldListings(APIView):
    permission_classes = (IsAuthenticatedOrReadOnly,)

    def get(self, request):
        page_num = self.request.GET.get("page_num", 0)
        limit = self.request.GET.get("limit", 10)

        # to filter query list and generate elastic queries
        new_query_list, minimum_should_match = request_query_merge_elastic(
            self.request.GET, "residential"
        )

        # Used threading and queuing to run both listings parallel to each other
        q1 = queue.Queue()
        q2 = queue.Queue()

        print(new_query_list)

        thread1 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                SOLD_MLS_INDEX_NAME,
                limit,
                page_num,
                "mls",
                q1,
            ),
        )
        thread2 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                CRMLS_SOLD_INDEX_NAME,
                limit,
                page_num,
                "crmls",
                q2,
            ),
        )

        thread1.start()
        thread2.start()

        # Wait for both threads to finish
        thread1.join()
        thread2.join()

        mls_results = q1.get()
        crmls_results = q2.get()

        get_crmls_count = crmls_results[0]["count"]
        get_mls_count = mls_results[0]["count"]
        total_count = get_crmls_count + get_mls_count

        try:
            mls_value = mls_results[1]["data"]
        except Exception:
            mls_value = []
        try:
            crmls_value = crmls_results[1]["data"]
        except Exception:
            crmls_value = []
        all_listings = mls_value + crmls_value

        q_query = self.request.GET.get("q", "None")
        query_name = is_city_or_address(q_query)

        return Response(
            {"count": total_count, "name": query_name, "data": all_listings}, status=status.HTTP_200_OK
        )


class MergeRentalSoldListings(APIView):
    permission_classes = (IsAuthenticatedOrReadOnly,)

    def get(self, request):
        page_num = self.request.GET.get("page_num", 0)
        limit = self.request.GET.get("limit", 10)

        # to filter query list and generate elastic queries
        new_query_list, minimum_should_match = request_query_merge_elastic(
            self.request.GET, "rental"
        )

        # Used threading and queuing to run both listings parallel to each other
        q1 = queue.Queue()
        q2 = queue.Queue()

        print(new_query_list)

        thread1 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                SOLD_MLS_INDEX_NAME,
                limit,
                page_num,
                "mls",
                q1,
            ),
        )
        thread2 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                CRMLS_SOLD_INDEX_NAME,
                limit,
                page_num,
                "crmls",
                q2,
            ),
        )

        thread1.start()
        thread2.start()

        # Wait for both threads to finish
        thread1.join()
        thread2.join()

        mls_results = q1.get()
        crmls_results = q2.get()

        get_crmls_count = crmls_results[0]["count"]
        get_mls_count = mls_results[0]["count"]
        total_count = get_crmls_count + get_mls_count

        try:
            mls_value = mls_results[1]["data"]
        except Exception:
            mls_value = []
        try:
            crmls_value = crmls_results[1]["data"]
        except Exception:
            crmls_value = []
        all_listings = mls_value + crmls_value

        q_query = self.request.GET.get("q", "None")
        query_name = is_city_or_address(q_query)

        return Response(
            {"count": total_count, "name": query_name, "data": all_listings}, status=status.HTTP_200_OK
        )
