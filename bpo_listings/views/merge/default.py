from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from bpo_listings import CRMLS_INDEX_NAME, MLS_INDEX_NAME
from bpo_listings.es_search.search_data.merge_search import merge_search
import threading
import queue
from bpo_listings.models import searchQuery
from bpo_listings.serializers.search_query import searchQuerySerializer
from bpo_listings.utils.generics.city_filters import city_filter_recent
from bpo_listings.utils.generics.city_or_address import is_city_or_address
from bpo_listings.utils.generics.get_request_query import request_query_merge_elastic
from register.models.agents import Agent
from register.serializers.agents import AgentSerializer


class MergeListings(APIView):
    permission_classes = (IsAuthenticatedOrReadOnly,)

    def get(self, request):
        page_num = self.request.GET.get("page_num", 0)
        limit = self.request.GET.get("limit", 10)

        # to filter query list and generate elastic queries
        new_query_list, minimum_should_match = request_query_merge_elastic(
            self.request.GET, "none"
        )

        # Used threading and queuing to run both listings parallel to each other
        q1 = queue.Queue()
        q2 = queue.Queue()


        thread1 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                MLS_INDEX_NAME,
                limit,
                page_num,
                "mls",
                q1,
            ),
        )
        thread2 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                CRMLS_INDEX_NAME,
                limit,
                page_num,
                "crmls",
                q2,
            ),
        )

        thread1.start()
        thread2.start()

        # Wait for both threads to finish
        thread1.join()
        thread2.join()

        mls_results = q1.get()
        crmls_results = q2.get()

        get_crmls_count = crmls_results[0]["count"]
        get_mls_count = mls_results[0]["count"]
        total_count = get_crmls_count + get_mls_count

        try:
            mls_value = mls_results[1]["data"]
        except Exception:
            mls_value = []
        try:
            crmls_value = crmls_results[1]["data"]
        except Exception:
            crmls_value = []
        all_listings = mls_value + crmls_value

        q_query = self.request.GET.get("q", "None")
        query_name = is_city_or_address(q_query)

        return Response(
            {"count": total_count, "name": query_name, "data": all_listings},
            status=status.HTTP_200_OK,
        )


class MergeResidentialListings(APIView):
    permission_classes = (IsAuthenticatedOrReadOnly,)

    def get(self, request):
        page_num = self.request.GET.get("page_num", 0)
        limit = self.request.GET.get("limit", 10)
        city = self.request.GET.get("city", "")

        # to filter query list and generate elastic queries
        new_query_list, minimum_should_match = request_query_merge_elastic(
            self.request.GET, "residential"
        )

        # Used threading and queuing to run both listings parallel to each other
        q1 = queue.Queue()
        q2 = queue.Queue()


        thread1 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                MLS_INDEX_NAME,
                limit,
                page_num,
                "mls",
                q1,
            ),
        )
        thread2 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                CRMLS_INDEX_NAME,
                limit,
                page_num,
                "crmls",
                q2,
            ),
        )

        thread1.start()
        thread2.start()

        # Wait for both threads to finish
        thread1.join()
        thread2.join()

        mls_results = q1.get()
        crmls_results = q2.get()

        get_crmls_count = crmls_results[0]["count"]
        get_mls_count = mls_results[0]["count"]
        total_count = get_crmls_count + get_mls_count

        try:
            mls_value = mls_results[1]["data"]
        except Exception:
            mls_value = []
        try:
            crmls_value = crmls_results[1]["data"]
        except Exception:
            crmls_value = []
        all_listings = mls_value + crmls_value

        q_query = self.request.GET.get("q", "None")
        query_name = is_city_or_address(q_query)
        
        get_q = self.request.GET.get("q", "None")
        
        # search_query = searchQuery.objects.filter(search__icontains=city_filter_recent(get_q))
        # search_serializer = searchQuerySerializer(search_query, many=True)

        search_query = searchQuery.objects.filter(search__icontains=city_filter_recent(get_q)).order_by('-created')[:5]
        search_serializer = searchQuerySerializer(search_query, many=True)

        # Get agents with the same city (in the same service area)
        agent_query = Agent.objects.filter(service_areas__location__icontains=city_filter_recent(get_q)).distinct()[:5]
        agent_serializer = AgentSerializer(agent_query, many=True)

        return Response(
            {
                "count": total_count,
                "name": query_name,
                "data": all_listings,
                "resent_search": search_serializer.data,
                "agents": agent_serializer.data,
            },
            status=status.HTTP_200_OK,
        )


class MergeRentalListings(APIView):
    permission_classes = (IsAuthenticatedOrReadOnly,)

    def get(self, request):
        page_num = self.request.GET.get("page_num", 0)
        limit = self.request.GET.get("limit", 10)

        # to filter query list and generate elastic queries
        new_query_list, minimum_should_match = request_query_merge_elastic(
            self.request.GET, "rental"
        )

        # Used threading and queuing to run both listings parallel to each other
        q1 = queue.Queue()
        q2 = queue.Queue()


        thread1 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                MLS_INDEX_NAME,
                limit,
                page_num,
                "mls",
                q1,
            ),
        )
        thread2 = threading.Thread(
            target=merge_search,
            args=(
                new_query_list,
                minimum_should_match,
                CRMLS_INDEX_NAME,
                limit,
                page_num,
                "crmls",
                q2,
            ),
        )

        thread1.start()
        thread2.start()

        # Wait for both threads to finish
        thread1.join()
        thread2.join()

        mls_results = q1.get()
        crmls_results = q2.get()

        get_crmls_count = crmls_results[0]["count"]
        get_mls_count = mls_results[0]["count"]
        total_count = get_crmls_count + get_mls_count

        try:
            mls_value = mls_results[1]["data"]
        except Exception:
            mls_value = []
        try:
            crmls_value = crmls_results[1]["data"]
        except Exception:
            crmls_value = []
        all_listings = mls_value + crmls_value

        q_query = self.request.GET.get("q", "None")
        query_name = is_city_or_address(q_query)

        return Response(
            {"count": total_count, "name": query_name, "data": all_listings},
            status=status.HTTP_200_OK,
        )
