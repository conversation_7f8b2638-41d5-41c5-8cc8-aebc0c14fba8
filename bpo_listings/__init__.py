import os
CRMLS_URL = "https://api-prod.corelogic.com/trestle/"
MLS_URL = "https://data.api.mlslistings.com/"
CRMLS_INDEX_NAME = "all_crmls"
ALL_CRMLS_INDEX_NAME = "all_crmls"
CRMLS_OPENHOUSE_INDEX_NAME = os.getenv("CRMLS_OPENHOUSE_INDEX_NAME", "openhouse_crmls")
CRMLS_SOLD_INDEX_NAME = os.getenv("CRMLS_SOLD_INDEX_NAME", "sold_crmls")
MLS_INDEX_NAME = "all_mls"
ALL_MLS_INDEX_NAME = os.getenv("ALL_MLS_INDEX_NAME", "all_mls")
OPENHOUSE_MLS_INDEX_NAME = os.getenv("OPENHOUSE_MLS_INDEX_NAME", "openhouse_mls")
SOLD_MLS_INDEX_NAME = os.getenv("SOLD_MLS_INDEX_NAME", "sold_mls")
ES_PAGE_LIMIT = 10
ES_PAGE_UPDATE_LIMIT = 20
TEST=""
