from django.urls import path
from bpo_listings.views.listings_views.claimlisting import (
    ClaimListingCreateView, ClaimListingSingleView, ClaimListingStatusView, ClaimUserRequestView, 
    ClaimUserRequestEmailView, ClaimAgentRequestView
)


urlpatterns = [
    path("claims/", ClaimListingCreateView.as_view()),
    path("claim-single/", ClaimListingSingleView.as_view()),
    path("claim-status/", ClaimListingStatusView.as_view()),
    path("claim-user-request/", ClaimUserRequestView.as_view()),
    path("claim-agent-myrequest/", ClaimAgentRequestView.as_view()),
    path("claim-user-request-email/", ClaimUserRequestEmailView.as_view()),
]