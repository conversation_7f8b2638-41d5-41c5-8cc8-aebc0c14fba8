from django.views.generic import TemplateView
from django.urls import path

from core.utils.subdomain_views import user_sub_domain
from core.views.otp import ResendOTP, VerifyEmail
from core.views.refresh_token import CustomTokenRefreshView
from .views import *


urlpatterns = [
    path("", TemplateView.as_view(template_name="core/index.html")),
    path("dj-rest-auth/google/", GoogleLogin.as_view(), name="google_login"),
    path("dj-rest-auth/facebook/", FacebookLogin.as_view(), name="fb_login"),
    path("dj-rest-auth/apple/", AppleLogin.as_view(), name="apple_login"),
    path("delete_facebook_data/", delete_facebook_data, name="delete_facebook_data"),
    path("reset-password/", ResetPassword.as_view(), name="core-reset-password"),
    path("one-tap-login/", GoogleLoginView.as_view(), name="one_tap_login"),
    path("prof-one-tap-login/", ProfGoogleLoginView.as_view(), name="one_tap_login"),
    path("resend-otp/", ResendOTP.as_view()),
    path("verify-email/", VerifyEmail.as_view()),
    path(
        "change-password/<int:pk>/",
        ChangePasswordView.as_view(),
        name="ChangePasswordView",
    ),
    path(
        "subdomain/",
        user_sub_domain,
        name="get_subdomain",
    ),
    path("auth/prof/google/", GoogleProfLoginView.as_view(), name="login_google"),
    path("refresh-token/", CustomTokenRefreshView.as_view()),


]
