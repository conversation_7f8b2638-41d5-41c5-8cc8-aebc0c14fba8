from allauth.socialaccount.models import SocialAccount
from django.db.models.signals import post_save
from django.dispatch import receiver
from allauth.socialaccount.signals import social_account_added
from core.models import User, UserDomain
from register.models import Client

from django.utils.crypto import get_random_string
@receiver(post_save, sender=SocialAccount)
def agent_created_handler(sender, instance, created, *args, **kwargs):
    if created:
        client = Client.objects.create(user_id=instance.user.id)
        client.save()


@receiver(post_save, sender=User)
def create_user_subdomain(sender, instance, created, *args, **kwargs):
    if created:
        # Generate a unique subdomain for the user
        base_subdomain = instance.email.split("@")[0]
        subdomain = base_subdomain

        # Check if the subdomain is already in use
        suffix = get_random_string(length=2)
        while UserDomain.objects.filter(subdomain=subdomain).exists():
            subdomain = f"{base_subdomain}{suffix}"
            suffix = get_random_string(length=2)

        # Assign the unique subdomain to the user
        UserDomain.objects.create(user=instance, subdomain=subdomain)



@receiver(social_account_added)
def update_user_is_verified(request, sociallogin, **kwargs):
    user = sociallogin.user
    if user:
        user.is_email_verified = True
        user.save()