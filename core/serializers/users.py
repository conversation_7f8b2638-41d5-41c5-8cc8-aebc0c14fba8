from django.contrib.auth import authenticate
from djoser.conf import settings
from djos<PERSON>.serializers import (
    UserSerializer as BaseUserSerializer,
    UserCreateSerializer as BaseUserCreateSerializer,
    TokenCreateSerializer,
)
from rest_framework import serializers
from core.models import User


class UserCreateSerializer(BaseUserCreateSerializer):
    first_name = serializers.CharField(max_length=255)
    last_name = serializers.CharField(max_length=255)

    class Meta(BaseUserCreateSerializer.Meta):
        ref_name = "core create"
        fields = ["id", "email", "password", "first_name", "last_name", "is_email_verified"]


class UserSerializer(BaseUserSerializer):
    class Meta(BaseUserSerializer.Meta):
        ref_name = "core user"
        fields = ["id", "email", "first_name", "last_name", "role", "is_email_verified"]
        read_only_fields = ["id", "email", "role"]


class CustomTokenCreateSerializer(TokenCreateSerializer):
    def validate(self, attrs):
        password = attrs.get("password")
        params = {settings.LOGIN_FIELD: attrs.get(settings.LOGIN_FIELD)}
        self.user = authenticate(
            request=self.context.get("request"), **params, password=password
        )
        if not self.user:
            self.user = User.objects.filter(**params).first()
            if self.user and not self.user.check_password(password):
                self.fail("invalid_credentials")
        # We changed only below line
        if self.user:  # and self.user.is_active:
            return attrs
        self.fail("invalid_credentials")
