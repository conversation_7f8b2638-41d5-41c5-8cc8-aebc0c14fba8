import json
import logging
import os

import requests


logger = logging.getLogger("request_logger")

GOOGLE_CLIENT_ID = os.getenv("SOCIAL_AUTH_GOOGLE_MYBUSINESS_KEY", None)
GOOGLE_CLIENT_ID_SECRET = os.getenv("SOCIAL_AUTH_GOOGLE_MYBUSINESS_SECRET", None)

TIKTOK_CLIENT_ID = os.getenv("TIKTOK_CLIENT_ID", None)
TIKTOK_CLIENT_ID_SECRET = os.getenv("TIKTOK_CLIENT_ID_SECRET", None)


def verify_oauth2_access_token(access_token):
    response = requests.get(
        f"https://oauth2.googleapis.com/tokeninfo?access_token={access_token}"
    )
    data = json.loads(response.text)
    return data


def google_user_profile(access_token):
    response = requests.get(
        "https://www.googleapis.com/oauth2/v3/userinfo",
        headers={"Authorization": f"Bearer {access_token}"},
    )
    data = json.loads(response.text)
    return data



def get_user_token(code, redirect_uri):
    token_url = 'https://oauth2.googleapis.com/token'
    # client_id = os.getenv("SOCIAL_AUTH_GOOGLE_OAUTH2_KEY_PROF")
    # client_secret = os.getenv("SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET_PROF")
    token_data = {
        'code': code,
        'client_id': "287753153334-132oenepiqgtbtig2kepvm0ccfmukomd.apps.googleusercontent.com",
        'client_secret': "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",
        'redirect_uri': redirect_uri,
        'grant_type': 'authorization_code',
    }
    token_response = requests.post(token_url, data=token_data)
    token_json = token_response.json()
    print("\n\n\n\n\n\n\n")
    print(token_json)

    access_token = token_json.get('access_token')

    return access_token