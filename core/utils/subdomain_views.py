from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response

from core.models import User
from register.models import Agent


def user_sub_domain(request, subdomain):
    """Get available subdomain"""
    try:
        user = User.objects.get(userdomain__subdomain=subdomain)
        agents = Agent.objects.filter(user=user.id).values()

    except Exception:
        return False

    response = Response(
        data={
            "agents": agents,
            "host": request.get_host(),
            "subdomain": subdomain
        },
        status=status.HTTP_200_OK
    )
    response.accepted_renderer = JSONRenderer()
    response.accepted_media_type = "application/json"
    response.renderer_context = {}
    response.render()
    return response
