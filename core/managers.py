from django.contrib.auth.base_user import BaseUserManager
from django.utils.translation import gettext as _


class UserManager(BaseUserManager):

    def create_user(self, email, password, **extra_fields):
        extra_fields.setdefault('is_active', True)

        # if extra_fields.get('is_active') is not True:
        #     raise ValueError(_('User must have is_active=True.'))

        if not email:
            raise ValueError(_('The Email must be set'))
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_admin_user(self, email, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        # extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Admin user must have is_staff=True.'))
        return self.create_user(email, password, **extra_fields)

    def create_superuser(self, email, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        # extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))

        super_user = self.create_user(email, password, **extra_fields)
        super_user.role == 'admin'
        super_user.save()
        return super_user

    def training_center_users(self):
        from core.models import User
        USER_ROLES = [User.AGENT, User.CLIENT, User.REALTY_AGENT, User.ADMIN]
        return self.filter(role__in=USER_ROLES, is_active=True)
