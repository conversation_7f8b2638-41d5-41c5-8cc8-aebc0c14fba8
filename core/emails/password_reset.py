from djoser import utils
from templated_mail.mail import BaseEmailMessage
from django.contrib.auth.tokens import default_token_generator


def send_reset_password_email(get_url, user):
    operations_reset_confirm_url = (
        "https://operations.bpohomes.com/reset_password/{}/{}"
    )
    default_reset_confirm_url = "/reset_password/{}/{}"
    uid = utils.encode_uid(user.pk)
    token = default_token_generator.make_token(user)

    if user.role in ["admin", "marketer", "snr_marketer"]:
        url = operations_reset_confirm_url
    else:
        url = get_url + default_reset_confirm_url

    header = "Reset Password"

    context = {
        "header": header,
        "uid": uid,
        "token": token,
        "url": url.format(uid, token),
    }
    recipient = user.email
    try:
        message = BaseEmailMessage(
            template_name="core/password_reset.html", context=context
        )
        message.send([recipient])
        return "good"
    except Exception as e:
        return e
