from djoser import utils
from templated_mail.mail import BaseEmailMessage


def send_otp(context, recipient, subject):
    header = subject

    context = {
        "message": context,
        "header": header,
    }

    recipient = recipient
    try:
        message = BaseEmailMessage(
            template_name="core/send_otp.html", context=context
        )
        message.send([recipient])
        return "good"
    except Exception as e:
        return e
