from __future__ import absolute_import, unicode_literals
from celery import shared_task
from celery.utils.log import get_task_logger
from compensation.emails import send_operations_email
from core.emails.send_otp import send_otp


logger = get_task_logger(__name__)


@shared_task()
def send_otp_task(otp, recipient):
    context = f"This is your OTP Code For Email Verification: {otp}"
    return send_otp(context, recipient, "Email Verification")
