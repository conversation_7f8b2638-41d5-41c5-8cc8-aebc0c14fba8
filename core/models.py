from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext as _

from .managers import UserManager
import typing
if typing.TYPE_CHECKING:
    from training_center.models.user import TrainingCenterUser
    from register.models import Agent

import uuid


class BaseModel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


class User(AbstractUser):
    trainingcenteruser: models.QuerySet['TrainingCenterUser']
    agent: models.QuerySet['Agent']
    SNR_MARKETER = "snr_marketer"
    MARKETER = "marketer"
    AGENT = "agent"
    REALTY_AGENT = "realty_agent"
    CLIENT = "client"
    ADMIN = "admin"
    INDUSTRY_PROFESSIONAL = "ind_prof"

    USER_ROLE_CHOICES = (
        (SNR_MARKETER, "Snr. Marketer"),
        (MARKETER, "Marketer"),
        (AGENT, "Agent"),
        (REALTY_AGENT, "Realty_Agent"),
        (CLIENT, "Client"),
        (ADMIN, "Admin"),
        (INDUSTRY_PROFESSIONAL, "Industry Professional"),
    )

    username = None
    email = models.EmailField(_("email address"), unique=True)
    role = models.CharField(max_length=255, choices=USER_ROLE_CHOICES, default=CLIENT)
    is_email_verified = models.BooleanField(default=False)
    otp = models.CharField(max_length=255, null=True, blank=True)
    last_active = models.DateTimeField(null=True, blank=True)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    objects: UserManager = UserManager()

    def __str__(self):
        return self.email

    def set_role(self, role):
        self.role = role

    @property
    def name(self):
        return f"{self.first_name} {self.last_name}"


class UserDomain(models.Model):
    user = models.OneToOneField(User, on_delete=models.SET_NULL, null=True)
    subdomain = models.CharField(max_length=50, unique=True)
