from rest_framework.response import Response
from rest_framework import status, generics
from core.serializers import CustomSocialLoginSerializer
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.facebook.views import Facebook<PERSON><PERSON>2Adapter
from allauth.socialaccount.providers.apple.views import AppleOAuth2Adapter
from facepy import SignedRequest
from dj_rest_auth.views import LoginView
from rest_framework.views import APIView

import os
from core.utils.client import CustomOAuth2Client, CustomAppleOAuth2Client
from core.models import User
from rest_framework.decorators import api_view
from rest_framework_simplejwt.tokens import RefreshToken
from google.oauth2.id_token import verify_oauth2_token
from google.auth.transport import requests
from allauth.socialaccount.models import SocialAccount
from allauth.account.adapter import get_adapter

from core.utils.google_auth import get_user_token, google_user_profile, verify_oauth2_access_token


class CustomSocialLoginView(LoginView):
    serializer_class = CustomSocialLoginSerializer

    def process_login(self):
        get_adapter(self.request).login(self.request, self.user)


class GoogleLogin(CustomSocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    client_class = CustomOAuth2Client


class GoogleLoginView(LoginView):
    def post(self, request, *args, **kwargs):
        id_token = request.data.get("id_token")
        if id_token:
            try:
                get_client_key = (os.getenv("SOCIAL_AUTH_GOOGLE_OAUTH2_KEY"),)
                request = requests.Request()
                payload = verify_oauth2_token(id_token, request, get_client_key)
                email = payload["email"]
                print("\n\n\n\n\n\n\n")
                print(payload)
                if User.objects.filter(email=email).exists():
                    user = User.objects.get(email=email)
                else:
                    user = User.objects.create(
                        first_name=payload["given_name"],
                        last_name=payload["family_name"],
                        email=payload["email"],
                        is_email_verified=True
                    )

                    SocialAccount.objects.create(
                        user=user,
                        uid=payload["sub"],
                        provider="google",
                        extra_data=payload,
                    )

                if user:
                    refresh = RefreshToken.for_user(user)
                    token = {
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    }
                    return Response(token)
                else:
                    return Response({"status": "error", "message": "User not found"})
            except ValueError as e:
                return Response({"status": f"error {e}", "message": "User not found"})
        else:
            return Response({"status": "error", "message": "id_token not provided"})


class FacebookLogin(CustomSocialLoginView):
    callback = os.getenv("BPOHOMESREALTY_URL")
    adapter_class = FacebookOAuth2Adapter
    client_class = CustomOAuth2Client


class AppleLogin(CustomSocialLoginView):
    callback = os.getenv("BPOHOMESREALTY_URL")
    adapter_class = AppleOAuth2Adapter
    callback_url = callback
    client_class = CustomAppleOAuth2Client


def parse_signed_request(signed_request):
    FB_APP_SECRET = os.getenv("SOCIAL_AUTH_FACEBOOK_SECRET")
    signed_data = SignedRequest.parse(signed_request, FB_APP_SECRET)
    return signed_data


@api_view(["POST"])
def delete_facebook_data(request, data):
    get_url = os.getenv("BPOHOMESTECH_URL")
    try:
        signed_request = request.POST["signed_request"]
        signed_data = parse_signed_request(signed_request)

        # Do User Data Deletion here

        user_obj = User.objects.filter(id=signed_data["user_id"]).first()
        user_obj.delete()
        confirmation_code = 200
    except Exception:
        confirmation_code = 403
    return_data = {
        "url": f"{get_url}user_deletion_status/{confirmation_code}",
        "confirmation_code": confirmation_code,
    }
    return return_data




class ProfGoogleLoginView(LoginView):
    def post(self, request, *args, **kwargs):
        id_token = request.data.get("id_token")
        if id_token:
            try:
                get_client_key = (os.getenv("SOCIAL_AUTH_GOOGLE_OAUTH2_KEY"),)
                request = requests.Request()
                payload = verify_oauth2_token(id_token, request, get_client_key)
                email = payload["email"]
                print("\n\n\n\n\n\n\n")
                print(payload)
                if User.objects.filter(email=email).exists():
                    user = User.objects.get(email=email)
                else:
                    user = User.objects.create(
                        first_name=payload["given_name"],
                        last_name=payload["family_name"],
                        email=payload["email"],
                        is_email_verified=True,
                        role="ind_prof"
                    )

                    SocialAccount.objects.create(
                        user=user,
                        uid=payload["sub"],
                        provider="google",
                        extra_data=payload,
                    )

                if user:
                    refresh = RefreshToken.for_user(user)
                    token = {
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    }
                    return Response(token)
                else:
                    return Response({"status": "error", "message": "User not found"})
            except ValueError as e:
                return Response({"status": f"error {e}", "message": "User not found"})
        else:
            return Response({"status": "error", "message": "id_token not provided"})




class GoogleProfLoginView(APIView):
    def post(self, request, *args, **kwargs):
        code = request.data.get("code")
        redirect_uri = request.data.get("redirect_uri")
        role = request.data.get("role")
        
        if not code:
            return Response({"status": "error", "message": "code not provided"})

        access_token = get_user_token(code, redirect_uri)
        print(access_token)
        get_profile = google_user_profile(access_token)
        print(get_profile)

        try:
            email = get_profile["email"]

            if User.objects.filter(email=email).exists():
                user = User.objects.get(email=email)
            else:
                user = User.objects.create(
                    first_name=get_profile.get("given_name", ""),
                    last_name=get_profile.get("family_name", ""),
                    email=email,
                    is_email_verified=True,
                    role=role
                )

                SocialAccount.objects.create(
                    user=user,
                    uid=get_profile["sub"],
                    provider="google",
                    extra_data=get_profile,
                )

            refresh = RefreshToken.for_user(user)
            token = {
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "is_email_verified":  user.is_email_verified,
            }
            return Response(token, status=status.HTTP_200_OK)
        except Exception as e:

            return Response({"status": "error", "message": f"Authentication failed: {e} -----{get_profile} ------ {access_token}"},
                            status=status.HTTP_401_UNAUTHORIZED)

