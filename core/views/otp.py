from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from core.models import User

from core.tasks.otp import send_otp_task
from investors.utils.errors import Invalid
from register.utils.random_otp import generate_6_digit_code


class ResendOTP(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        user_id = request.data.get("user_id", None)
        try:
            user = User.objects.get(id=user_id)
            user.otp = generate_6_digit_code()
            user.save()
            send_otp_task.delay(user.otp, user.email)

            return Response(
                {
                    "status": "success",
                    "message": "OTP resent",
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {
                    "status": "success",
                    "message": f"something went wrong: {e}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class VerifyEmail(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        user_id = request.data.get("user_id", None)
        otp = request.data.get("otp", None)

        try:
            user = User.objects.get(id=user_id)
            print("-----------------------------------------")
            print(user.otp)
            print(otp)

            if int(user.otp) == otp:
                user.is_email_verified = True
                user.otp = None
            else:
                raise Invalid(detail="Wrong OTP")

            user.save()

            return Response(
                {
                    "status": "success",
                    "message": "Email verified",
                },
                status=status.HTTP_200_OK,
            )
        except User.DoesNotExist:
            return Response(
                {
                    "status": "failed",
                    "message": "something went wrong",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "message": "something went wrong",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
