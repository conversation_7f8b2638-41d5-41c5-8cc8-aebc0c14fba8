from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.serializers import TokenRefreshSerializer
from rest_framework.response import Response

class CustomTokenRefreshView(TokenRefreshView):
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Get the new tokens
        refresh = serializer.validated_data['refresh']
        access = serializer.validated_data['access']

        # Optional: Add any additional data if needed
        return Response({
            'refresh': refresh,
            'access': access,
        })