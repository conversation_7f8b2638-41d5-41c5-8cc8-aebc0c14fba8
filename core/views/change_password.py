from rest_framework import status
from rest_framework.response import Response
from core.emails.password_reset import send_reset_password_email
from core.serializers import ChangePasswordSerializer
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from core.models import User
from rest_framework import generics


class ChangePasswordView(generics.UpdateAPIView):
    queryset = User.objects.all()
    permission_classes = (IsAuthenticated,)
    serializer_class = ChangePasswordSerializer


class ResetPassword(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        get_email = request.data.get("email", None)
        get_url = request.data.get("url", None)

        try:
            user = User.objects.get(email=get_email)
            send_reset_password_email(get_url, user)

            return Response(
                {"data": "Email has been sent"},
                status=status.HTTP_200_OK,
            )
        except User.DoesNotExist:
            return Response(
                {"data": "Account Cannot be found"},
                status=status.HTTP_404_NOT_FOUND,
            )
