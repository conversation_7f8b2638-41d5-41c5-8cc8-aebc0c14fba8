import os
from urllib import parse
from pathlib import Path
from datetime import timed<PERSON>ta
from dotenv import load_dotenv, find_dotenv
# import boto3
# import platform
from celery.schedules import crontab
from bpohomes_backend.import_export_models import IMPORT_EXPORT_CELERY_MODELS
load_dotenv(find_dotenv())

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Get environment
LOCAL_ENV = "DEV"

ENVIRONMENT = os.getenv("ENVIRONMENT")

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = ENVIRONMENT == LOCAL_ENV

ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    # django apps
    "django.contrib.admin",
    "django.contrib.sessions",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.gis",
    "corsheaders",
    "django_extensions",
    "django_filters",
    # third party apps
    "storages",
    "rest_framework",
    "rest_framework_gis",
    "drf_yasg",
    "djoser",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    "social_django",
    "import_export",
    "import_export_celery",
    "django_elasticsearch_dsl",
    "rest_framework.authtoken",
    "dj_rest_auth",
    "django.contrib.sites",
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "dj_rest_auth.registration",
    "allauth.socialaccount.providers.facebook",
    "allauth.socialaccount.providers.twitter",
    "allauth.socialaccount.providers.google",
    "allauth.socialaccount.providers.apple",
    # bpo home apps
    "operations",
    "register",
    "reports",
    "farm",
    "premiersite",
    "core",
    "deal_analyzer",
    "leads",
    "contract",
    "favorite_activities",
    "compensation",
    "testimonials",
    "professionals",
    "bpo_listings",
    "investors",
    "literals",
    "training_center",
]


ACCOUNT_AUTHENTICATION_METHOD = "email"
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_USER_MODEL_USERNAME_FIELD = None
ACCOUNT_USERNAME_REQUIRED = False
SITE_ID = 1


# IMPORT EXPORT CELERY
IMPORT_EXPORT_CELERY_INIT_MODULE = "bpohomes_backend.celery"
IMPORT_EXPORT_CELERY_MODELS = IMPORT_EXPORT_CELERY_MODELS
IMPORT_DRY_RUN_FIRST_TIME = True
IMPORT_EXPORT_USE_TRANSACTIONS = True

if not DEBUG:
    IMPORT_EXPORT_CELERY_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"


SOCIALACCOUNT_PROVIDERS = {
    "apple": {
        "APP": {
            # Your service identifier.
            "client_id": "com.bpohomes.bpohomes",
            # The Key ID (visible in the "View Key Details" page).
            "secret": "WHZN2QTSBF",
            # Member ID/App ID Prefix -- you can find it below your name
            # at the top right corner of the page, or it’s your App ID
            # Prefix in your App ID.
            "key": "WHZN2QTSBF",
            # The certificate you downloaded when generating the key.
            "certificate_key": os.getenv("APPLE_CERT"),
        }
    },
    "google": {
        # For each OAuth based provider, either add a ``SocialApp``
        # (``socialaccount`` app) containing the required client
        # credentials, or list them here:
        "APP": {
            "client_id": os.getenv("SOCIAL_AUTH_GOOGLE_OAUTH2_KEY"),
            "secret": os.getenv("SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET"),
            "key": "",
        },
        # These are provider-specific settings that can only be
        # listed here:
        "SCOPE": [
            "profile",
            "email",
        ],
        "AUTH_PARAMS": {
            "access_type": "online",
        },
        "CALLBACK_URL": "https://testing.bpotech.io/",
        "OAUTH_PKCE_ENABLED": True,
    },
    "facebook": {
        "SCOPE": ["email", "profile", "first_name", "last_name"],
        "APP": {
            "client_id": os.getenv("SOCIAL_AUTH_FACEBOOK_KEY"),
            "secret": os.getenv("SOCIAL_AUTH_FACEBOOK_SECRET"),
            "key": "",
        },
        "AUTH_PARAMS": {"auth_type": "reauthenticate"},
        "EXCHANGE_TOKEN": True,
    },
}

# Elasticsearch
ELASTIC_HOST = os.getenv("ELASTIC_HOST", "localhost")
ELASTIC_PORT = os.getenv("ELASTIC_PORT", 9200)
ELASTIC_USERNAME = os.getenv("ELASTIC_USERNAME", None)
ELASTIC_PASSWORD = os.getenv("ELASTIC_PASSWORD", None)
ELASTIC = parse.urlparse(os.getenv("ELASTIC_URL"))

ELASTIC_URL = f"https://{ELASTIC_USERNAME}:{ELASTIC_PASSWORD}@{ELASTIC.hostname}:9200"

ELASTICSEARCH_DSL = {
    "default": {"hosts": ELASTIC_URL, "verify_certs": False},
}

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "social_django.middleware.SocialAuthExceptionMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "core.middleware.last_login.UpdateLastActivityMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "author.middlewares.AuthorDefaultBackendMiddleware",

    # Sub Domain Middleware
    "bpohomes_backend.middleware.SubDomainMiddleware",

]

INTERNAL_IPS = [
    # ...
    "127.0.0.1",
    # ...
]

CORS_ALLOW_ALL_ORIGINS = True
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_WHITELIST = (
    "http://localhost:3000",
    "https://testing.bpotech.io",
    "https://bpotech.io",
)
CORS_ALLOWED_ORIGINS = [
    "https://testing.bpotech.io",
    "https://testing.bpohomes.com",
    "https://bpohomes.com",
    "https://bpotech.io",
    "http://localhost:3000",
]

ROOT_URLCONF = "bpohomes_backend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "social_django.context_processors.backends",
                "social_django.context_processors.login_redirect",
            ],
        },
    },
]

WSGI_APPLICATION = "bpohomes_backend.wsgi.application"

DATABASE_URL = parse.urlparse(os.getenv("DATABASE_URL"))


DATABASES = {
    "default": {
        "ENGINE": "django.contrib.gis.db.backends.postgis",
        "NAME": DATABASE_URL.path[1:],
        "USER": DATABASE_URL.username,
        "PASSWORD": DATABASE_URL.password,
        "HOST": DATABASE_URL.hostname,
        "PORT": DATABASE_URL.port,
    }
}

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/" if ENVIRONMENT == LOCAL_ENV else os.getenv("STATIC_URL")

# Add these new lines
STATICFILES_DIRS = (os.path.join(BASE_DIR, "staticfiles/"),)

STATIC_ROOT = os.path.join(BASE_DIR, "static")

MEDIA_URL = "/media/" if ENVIRONMENT == LOCAL_ENV else os.getenv("MEDIA_URL")
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    "COERCE_DECIMAL_TO_STRING": False,
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.TokenAuthentication",
        "dj_rest_auth.jwt_auth.JWTCookieAuthentication",
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
    "DEFAULT_FILTER_BACKENDS": ["django_filters.rest_framework.DjangoFilterBackend"],
}
REST_USE_JWT = True
JWT_AUTH_COOKIE = "BPOHomes"
AUTH_USER_MODEL = "core.User"

AUTHENTICATION_BACKENDS = (
    "social_core.backends.google.GoogleOAuth2",
    "social_core.backends.google.GooglePlusAuth",
    # 'django.contrib.auth.backends.AllowAllUserModelBackend',
    "django.contrib.auth.backends.ModelBackend",
)

SIMPLE_JWT = {
    "AUTH_HEADER_TYPES": ("JWT",),
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=8),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    'ROTATE_REFRESH_TOKENS': True,  # This option allows generating a new refresh token
    'BLACKLIST_AFTER_ROTATION': True,  # Blacklists old refresh tokens after rotation
    'USER_ID_FIELD': 'email',

}
# reset confirm urls


DJOSER = {
    "USER_ID_FIELD": "email",
    "LOGIN_FIELD": "email",
    # 'SEND_ACTIVATION_EMAIL': True,
    # 'ACTIVATION_URL': 'activate/{uid}/{token}',
    # 'PASSWORD_RESET_CONFIRM_URL': 'https://bpohomes.com/reset_password/{uid}/{token}',
    "SOCIAL_AUTH_TOKEN_STRATEGY": "djoser.social.token.jwt.TokenStrategy",
    "SOCIAL_AUTH_ALLOWED_REDIRECT_URIS": [os.getenv("CALLBACK_URL")],
    "SERIALIZERS": {
        "user_create": "core.serializers.UserCreateSerializer",
        "current_user": "core.serializers.UserSerializer",
        "user_delete": "djoser.serializers.UserDeleteSerializer",
        "token_create": "core.serializers.CustomTokenCreateSerializer",
        "password_reset": "core.serializers.CustomSendEmailResetSerializer",
    },
    "EMAIL": {"password_reset": "core.emails.PasswordResetEmail"},
}

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
SITE_NAME = "BPO Homes"
EMAIL_HOST = os.getenv("SENDGRID_SMTP_ENDPOINT")
EMAIL_HOST_USER = "apikey"
EMAIL_HOST_PASSWORD = os.getenv("SENDGRID_API_KEY")
EMAIL_PORT = 587
EMAIL_USE_TLS = True
DEFAULT_FROM_EMAIL = os.getenv("AWS_SMTP_SENDER_EMAIL")


ADMINS = [("Francis", "<EMAIL>")]

SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = os.getenv("SOCIAL_AUTH_GOOGLE_OAUTH2_KEY")
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = os.getenv("SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET")
SOCIAL_AUTH_FACEBOOK_KEY = os.getenv("SOCIAL_AUTH_FACEBOOK_KEY")
SOCIAL_AUTH_FACEBOOK_SECRET = os.getenv("SOCIAL_AUTH_FACEBOOK_SECRET")
SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE = [
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
    "openid",
]
SOCIAL_AUTH_GOOGLE_OAUTH2_EXTRA_DATA = ["first_name", "last_name"]

SOCIAL_AUTH_GOOGLE_OAUTH2_CALLBACK_URL = "https://testing.bpotech.io/"
SOCIAL_AUTH_GOOGLE_OAUTH2_CALLBACK_URL_1 = "https://testing.bpotech.io/"
SOCIAL_AUTH_GOOGLE_OAUTH2_CALLBACK_URL_2 = "https://bpotech.io/"
SOCIAL_AUTH_GOOGLE_OAUTH2_CALLBACK_URL_3 = "https://bpdotech.io/"


"""
Define SOCIAL_AUTH_FACEBOOK_SCOPE to get extra permissions from facebook. 
    Email is not sent by default, to get it, you must request the email permission: """
SOCIAL_AUTH_FACEBOOK_SCOPE = ["email"]
SOCIAL_AUTH_FACEBOOK_PROFILE_EXTRA_PARAMS = {
    "fields": "id, name, email",
}
FACEBOOK_EXTENDED_PERMISSIONS = ["email"]
SOCIAL_AUTH_ADMIN_USER_SEARCH_FIELDS = ["first_name", "email"]
SOCIAL_AUTH_USERNAME_IS_FULL_EMAIL = True
SOCIAL_AUTH_FIELD_SELECTORS = [
    "email-address",
]


PROD = "production"
STRIPE_LIVE_MODE = ENVIRONMENT == PROD
STRIPE_PUBLISHABLE_KEY = (
    os.getenv("STRIPE_LIVE_PUBLISHABLE_KEY")
    if STRIPE_LIVE_MODE
    else os.getenv("STRIPE_TEST_PUBLISHABLE_KEY")
)
STRIPE_SECRET_KEY = (
    os.getenv("STRIPE_LIVE_SECRET_KEY")
    if STRIPE_LIVE_MODE
    else os.getenv("STRIPE_TEST_SECRET_KEY")
)
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET")
STRIPE_API_VERSION = os.getenv("STRIPE_API_VERSION")


UPLOAD_STATIC_AND_MEDIA_FILES_TO_S3 = os.getenv("UPLOAD_STATIC_AND_MEDIA_FILES_TO_S3", "True") == "True"


if UPLOAD_STATIC_AND_MEDIA_FILES_TO_S3:
    AWS_SES_REGION_NAME = os.getenv("AWS_SES_REGION_NAME")
    AWS_SES_REGION_ENDPOINT = os.getenv("AWS_SES_REGION_ENDPOINT")
    AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME")

    AWS_S3_FILE_OVERWRITE = False
    AWS_DEFAULT_ACL = None
    DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"
    STATICFILES_STORAGE = "storages.backends.s3boto3.S3StaticStorage"
    AWS_QUERYSTRING_AUTH = False

UNIT_COMPENSATION = os.getenv("UNIT_COMPENSATION")

# Celery config
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL")
CELERY_IMPORTS = (
    "farm.tasks",
    "leads.tasks",
    "operations.tasks",
    "register.tasks",
    "compensation.tasks",
    "contract.tasks",
    "bpo_listings.tasks",
)

CELERY_BEAT_SCHEDULE = {
    # Executes every Friday at 4pm
    "check-active-agent-an-compensate-marketer-on-fifteen": {
        "task": "compensation.tasks.check_monthly_marketer_payment",
        "schedule": crontab(hour=1, day_of_month=15),
    },
    "reset-bpo-number-of-report-yearly": {
        "task": "leads.tasks.tasks.reset_bpo_number_of_report_yearly",
        "schedule": crontab(hour=1, minute=0),
    },
    "delete_sold_listings_in_crmls_task": {
        "task": "bpo_listings.tasks.delete_sold_listings_in_crmls",
        "schedule": crontab(hour=1, minute=0),
    },
    "delete_active_listings_in_sold_crmls_task": {
        "task": "bpo_listings.tasks.delete_active_listings_in_sold_crmls",
        "schedule": crontab(hour=1, minute=0),
    },
    "delete_sold_listings_in_mls_task": {
        "task": "bpo_listings.tasks.delete_sold_listings_in_mls",
        "schedule": crontab(hour=1, minute=0),
    },
    "delete_active_listings_in_sold_mls_task": {
        "task": "bpo_listings.tasks.delete_active_listings_in_sold_mls",
        "schedule": crontab(hour=1, minute=0),
    },
}


CELERY_ACKS_LATE = True
# CELERY_ACCEPT_CONTENT = os.getenv("CELERY_ACCEPT_CONTENT")
# CELERY_TASK_SERIALIZER = os.getenv("CELERY_TASK_SERIALIZER")
# CELERY_BEAT_SCHEDULE = {
#     '':{}
# }

# ipython config for remote shell
NOTEBOOK_ARGUMENTS = [
    "--allow-root",
    "--port=8888",
    "--ip=0.0.0.0",
    "--NotebookApp.token=''",
    "--NotebookApp.password=''",
]

#  LOGGER config
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "DEBUG",
    },
}

# config for my pc: has no effect on prod - mosogbe
if ENVIRONMENT == LOCAL_ENV:
    if os.getenv('PC_NAME') == 'mosogbe':
        GDAL_LIBRARY_PATH = r'C:\OSGeo4W\bin\gdal300'
        GDAL_LIBRARY_PATH = r'D:\python_projects\BPO\bpohomes_backend\.bpohomes\Lib\site-packages\osgeo\gdal303.dll'
        GEOS_LIBRARY_PATH = r'D:\python_projects\BPO\bpohomes_backend\.bpohomes\Lib\site-packages\osgeo\geos_c.dll'

        VENV_BASE = os.environ['VIRTUAL_ENV']
        os.environ['PATH'] = os.path.join(VENV_BASE, 'Lib\\site-packages\\osgeo') + ';' + os.environ['PATH']
        os.environ['PROJ_LIB'] = os.path.join(
            VENV_BASE, 'Lib\\site-packages\\osgeo\\data\\proj') + ';' + os.environ['PATH']
    elif os.getenv('PC_NAME') == 'bcarthur':
        GDAL_LIBRARY_PATH = '/opt/homebrew/opt/gdal/lib/libgdal.dylib'
        GEOS_LIBRARY_PATH = '/opt/homebrew/opt/geos/lib/libgeos_c.dylib'

# LEAD_MANAGEMENT_SYSTEM
LEAD_MANAGEMENT_SYSTEM_URI = os.getenv("LEAD_MANAGEMENT_SYSTEM_URI")
LEAD_AUTH_WEBHOOK = f"{LEAD_MANAGEMENT_SYSTEM_URI}/external-leads/fetch-data-from-bpohomes"
LEAD_AUTH_ENDPOINT = f"{LEAD_MANAGEMENT_SYSTEM_URI}/accounts/login/"
LEAD_AUTH_EMAIL = os.getenv("LEAD_AUTH_EMAIL")
LEAD_AUTH_PASS = os.getenv("LEAD_AUTH_PASS")


SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'JWT': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header',
            'description': 'Type "Bearer " followed by your token',
        },
    },
    'USE_SESSION_AUTH': True,
    'SHOW_REQUEST_HEADERS': True,
    'JSON_EDITOR': True,
}


DATA_UPLOAD_MAX_MEMORY_SIZE = int(os.getenv("DATA_UPLOAD_MAX_MEMORY_SIZE", ********))   # 10 MB
FILE_UPLOAD_MAX_MEMORY_SIZE = int(os.getenv("FILE_UPLOAD_MAX_MEMORY_SIZE", ********))    # 15 MB
