"""bpohomes_backend URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

from register.views.export_agent import ExportAgent1


admin.site.site_title = 'BPOHomes Admin'
admin.site.site_header = 'BPOHomes Admin'
admin.site.index_title = 'Admin'


# schema_view = get_schema_view(
#     openapi.Info(
#         title="BPO Homes API",
#         default_version='v1',
#         description="Dev Server API",
#         terms_of_service="https://www.bpohomes.com/policies/terms/",
#         contact=openapi.Contact(email="<EMAIL>"),
#         license=openapi.License(name="BSD License"),
#     ),
#     public=True,
#     permission_classes=[permissions.AllowAny],
# )


urlpatterns1 = [
    path('admin/', admin.site.urls),
    path('operations/', include('operations.urls')),
    path('leads/', include('leads.urls')),
    path('professionals/', include('professionals.urls')),
    path('compensations/', include('compensation.urls')),
    path('favorite/', include('favorite_activities.urls')),
    path('register/', include('register.urls')),
    path('reports/', include('reports.urls')),
    path('premiersite/', include('premiersite.urls')),
    path('farm/', include('farm.urls')),
    path('core/', include('core.urls')),
    path('auth/', include('djoser.urls')),
    path('auth/', include('djoser.urls.jwt')),
    path('deal_analyzer/', include('deal_analyzer.urls')),
    path('contract/', include('contract.urls')),
    path('testimonial/', include('testimonials.urls')),
    path('bpo_listings/', include('bpo_listings.urls')),
    path('investors/', include('investors.urls')),
    path('literals/', include('literals.urls')),
    path('claim_listing/', include('bpo_listings.auth_urls')),
    path('training-centers/', include('training_center.urls')),










]


schema_view = get_schema_view(
    openapi.Info(
        title="BPO Homes API",
        default_version='v1',
        description="Dev Server API",
        terms_of_service="https://www.bpohomes.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],

    patterns=urlpatterns1)

urlpatterns = urlpatterns1 + [
    path('auth/social/', include('dj_rest_auth.urls'), name='dj_social_auth'),
    path('', schema_view.with_ui('swagger',
                                 cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc',
                                       cache_timeout=0), name='schema-redoc'),
    path('accounts/', include('allauth.urls'), name='socialaccount_signup'),
    path("not-to-find/", ExportAgent1.as_view()),

]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
