
def agent_list_csv_resource():
    from investors.utils.resourse import AgentListCSVResource
    return AgentListCSVResource


IMPORT_EXPORT_CELERY_MODELS = {
    "Leads": {
        "app_label": "leads",
        "model_name": "Leads",
    },
    "Probate": {
        "app_label": "leads",
        "model_name": "Probate",
    },
    "Divorce": {
        "app_label": "leads",
        "model_name": "Divorce",
    },

    "Expired": {
        "app_label": "leads",
        "model_name": "Expired",
    },
    "LeadMagnet": {
        "app_label": "leads",
        "model_name": "LeadMagnet",
    },
    "AgentListCSV": {
        "app_label": "investors",
        "model_name": "AgentListCSV",
        "resource": agent_list_csv_resource,
    },

}
