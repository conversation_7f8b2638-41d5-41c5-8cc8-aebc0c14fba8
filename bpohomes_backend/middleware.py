import re

from core.utils.subdomain_views import user_sub_domain


class SubDomainMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        request_host = request.get_host()
        domain_parts = request_host.split('.')

        subdomain = domain_parts[0] if len(domain_parts) > 2 and not re.match(
            r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d+)?$', request_host) else None

        request.subdomain = subdomain

        if subdomain:
            response = user_sub_domain(request, subdomain)
            if response:
                return response
            elif response is False:
                pass  # Proceed out of the if statement

        response = self.get_response(request)

        # Code to be executed for each request/response after
        # the view is called.

        return response
