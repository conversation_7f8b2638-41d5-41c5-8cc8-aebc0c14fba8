from random import randint

from django.contrib.gis.db import models

from register.models import Agent, StripeSubscriptionItem


class TerritoryManager(models.Manager):
    def available(self):
        qs = super(TerritoryManager, self).get_queryset()
        return qs.filter(models.Q(agent_id=None)).order_by('id')


class Territory(models.Model):
    name = models.CharField(max_length=255, null=True)
    state = models.CharField(max_length=255, null=True)
    county = models.CharField(max_length=255, null=True)
    city = models.CharField(max_length=255, null=True)
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, null=True)
    subscription_item = models.ForeignKey(
        StripeSubscriptionItem, on_delete=models.SET_NULL, null=True, related_name="territory")
    objects = TerritoryManager()

    def __str__(self) -> str:
        return f"{self.name} - {self.city}"


class Parcel(models.Model):
    territory = models.ForeignKey(Territory, on_delete=models.SET_NULL, null=True, related_name='parcels')
    fips_code = models.CharField(max_length=5, null=True)
    parcel_apn = models.CharField(max_length=50, null=True)
    taxapn = models.CharField(max_length=40, null=True)
    site_addr = models.CharField(max_length=200, null=True)
    site_house_number = models.CharField(max_length=13, null=True)
    site_direction = models.CharField(max_length=2, null=True)
    site_street_name = models.CharField(max_length=30, null=True)
    site_mode = models.CharField(max_length=5, null=True)
    site_carrier_code = models.CharField(max_length=4, null=True)
    site_quadrant = models.CharField(max_length=2, null=True)
    site_unit_prefix = models.CharField(max_length=10, null=True)
    site_unit_number = models.CharField(max_length=8, null=True)
    site_city = models.CharField(max_length=40, null=True)
    site_county = models.CharField(max_length=40, null=True)
    site_state = models.CharField(max_length=2, null=True)
    site_zip = models.CharField(max_length=5, null=True, db_index=True)
    site_plus_4 = models.CharField(max_length=4, null=True)
    _x_coord = models.FloatField(null=True)
    _y_coord = models.FloatField(null=True)
    addr_score = models.IntegerField(null=True)
    owner_name = models.CharField(max_length=166, null=True)
    owner_name_1 = models.CharField(max_length=166, null=True)
    owner_name_2 = models.CharField(max_length=166, null=True)
    owner_1_first = models.CharField(max_length=30, null=True)
    owner_1_last = models.CharField(max_length=30, null=True)
    owner_2_first = models.CharField(max_length=30, null=True)
    owner_2_last = models.CharField(max_length=30, null=True)
    mail_addr = models.CharField(max_length=200, null=True)
    mail_house_number = models.CharField(max_length=13, null=True)
    mail_direction = models.CharField(max_length=2, null=True)
    mail_street_name = models.CharField(max_length=30, null=True)
    mail_mode = models.CharField(max_length=5, null=True)
    mail_quadrant = models.CharField(max_length=2, null=True)
    mail_city = models.CharField(max_length=40, null=True)
    mail_state = models.CharField(max_length=2, null=True)
    mail_zip = models.CharField(max_length=5, null=True)
    mail_plus_4 = models.CharField(max_length=4, null=True)
    mail_unit_prefix = models.CharField(max_length=10, null=True)
    mail_unit_number = models.CharField(max_length=6, null=True)
    owner_occupied = models.CharField(max_length=1, null=True)
    use_code_muni = models.CharField(max_length=10, null=True)
    use_code_muni_desc = models.CharField(max_length=45, null=True)
    use_code_std_lps = models.CharField(max_length=100, null=True)
    use_code_std_desc_lps = models.CharField(max_length=200, null=True)
    use_code_std_ctgr_lps = models.CharField(max_length=5, null=True)
    use_code_std_ctgr_desc_lps = models.CharField(max_length=100, null=True)
    assessee_owner_name_1 = models.CharField(max_length=80, null=True)
    assessee_owner_name_2 = models.CharField(max_length=60, null=True)
    asmt_val_transfer = models.CharField(max_length=10, null=True)
    zoning = models.CharField(max_length=10, null=True)
    lot_size_area = models.FloatField(null=True)
    lot_size_area_unit = models.CharField(max_length=2, null=True)
    lot_size_area_orgn = models.CharField(max_length=14, null=True)
    yr_blt = models.CharField(max_length=255, null=True)
    building_sqft = models.IntegerField(null=True)
    stories_number = models.CharField(max_length=10, null=True)
    units_number = models.IntegerField(null=True)
    bedrooms = models.IntegerField(null=True)
    total_baths = models.IntegerField(null=True)
    garage_carport_type = models.CharField(max_length=3, null=True)
    garage_code_desc = models.CharField(max_length=50, null=True)
    parking_spaces = models.IntegerField(null=True)
    pool_indicator = models.CharField(max_length=1, null=True)
    construction_code_desc = models.CharField(max_length=50, null=True)
    price_per_sqft = models.DecimalField(max_digits=20, decimal_places=2, null=True)
    last_sale_date_transfer = models.CharField(max_length=255, null=True)
    last_market_sale_date_transfer = models.CharField(max_length=255, null=True)
    last_market_sale_val_transfer = models.DecimalField(max_digits=20, decimal_places=2, null=True)
    prior_sale_date_transfer = models.CharField(max_length=255, null=True)
    assessee_owner_2_indicator = models.CharField(max_length=1, null=True)
    assessee_owner_1_name_type = models.CharField(max_length=1, null=True)
    assessee_owner_2_name_type = models.CharField(max_length=1, null=True)
    yr_blt_effect = models.CharField(max_length=255, null=True)
    lot_width = models.IntegerField(null=True)
    lot_depth = models.IntegerField(null=True)
    location_id = models.CharField(max_length=50, null=True)
    address_id = models.CharField(max_length=50, null=True)
    parcel_dmp_id = models.CharField(max_length=21, null=True)
    shape = models.MultiPolygonField(srid=4269, null=True)
    last_market_sale_seller_name = models.CharField(max_length=255, null=True)
    last_market_sale_seller_1_first_mid = models.CharField(max_length=255, null=True)
    last_market_sale_seller_1_last = models.CharField(max_length=255, null=True)
    last_market_sale_seller_1_code = models.CharField(max_length=255, null=True)
    last_market_sale_seller_2_first_mid = models.CharField(max_length=255, null=True)
    last_market_sale_seller_2_last = models.CharField(max_length=255, null=True)
    owner_occupied = models.CharField(max_length=255, null=True)

    objects = models.Manager()

    @property
    def random_parcel(self):
        count = self.objects.all().count()
        random_index = randint(0, count - 1)
        return self.all()[random_index]

    def es_to_dict(self):
        """
        Convert the Parcel object to a dictionary.
        """
        data = {}
        for field in self.es_fields():
            field_value = getattr(self, field, None)
            data[field] = field_value
        return data

    @classmethod
    def es_fields(cls):
        fields_to_exclude = ['territory', 'shape']

        all_fields = [field.name for field in cls._meta.fields]

        for field_to_exclude in fields_to_exclude:
            if field_to_exclude in all_fields:
                all_fields.remove(field_to_exclude)
        return all_fields


class UnmatchedParcel(models.Model):
    territory = models.ForeignKey(Territory, on_delete=models.SET_NULL, null=True)
    fips_code = models.CharField(max_length=5, null=True)
    parcel_apn = models.CharField(max_length=50, null=True)
    taxapn = models.CharField(max_length=40, null=True)
    site_addr = models.CharField(max_length=200, null=True)
    site_house_number = models.CharField(max_length=13, null=True)
    site_direction = models.CharField(max_length=2, null=True)
    site_street_name = models.CharField(max_length=30, null=True)
    site_mode = models.CharField(max_length=5, null=True)
    site_carrier_code = models.CharField(max_length=4, null=True)
    site_quadrant = models.CharField(max_length=2, null=True)
    site_unit_prefix = models.CharField(max_length=10, null=True)
    site_unit_number = models.CharField(max_length=8, null=True)
    site_city = models.CharField(max_length=40, null=True)
    site_county = models.CharField(max_length=40, null=True)
    site_state = models.CharField(max_length=2, null=True)
    site_zip = models.CharField(max_length=5, null=True)
    site_plus_4 = models.CharField(max_length=4, null=True)
    _x_coord = models.FloatField(null=True)
    _y_coord = models.FloatField(null=True)
    addr_score = models.IntegerField(null=True)
    owner_name = models.CharField(max_length=166, null=True)
    owner_name_1 = models.CharField(max_length=166, null=True)
    owner_name_2 = models.CharField(max_length=166, null=True)
    owner_1_first = models.CharField(max_length=30, null=True)
    owner_1_last = models.CharField(max_length=30, null=True)
    owner_2_first = models.CharField(max_length=30, null=True)
    owner_2_last = models.CharField(max_length=30, null=True)
    mail_addr = models.CharField(max_length=200, null=True)
    mail_house_number = models.CharField(max_length=13, null=True)
    mail_direction = models.CharField(max_length=2, null=True)
    mail_street_name = models.CharField(max_length=30, null=True)
    mail_mode = models.CharField(max_length=5, null=True)
    mail_quadrant = models.CharField(max_length=2, null=True)
    mail_city = models.CharField(max_length=40, null=True)
    mail_state = models.CharField(max_length=2, null=True)
    mail_zip = models.CharField(max_length=5, null=True)
    mail_plus_4 = models.CharField(max_length=4, null=True)
    mail_unit_prefix = models.CharField(max_length=10, null=True)
    mail_unit_number = models.CharField(max_length=6, null=True)
    owner_occupied = models.CharField(max_length=1, null=True)
    use_code_muni = models.CharField(max_length=10, null=True)
    use_code_muni_desc = models.CharField(max_length=45, null=True)
    use_code_std_lps = models.CharField(max_length=100, null=True)
    use_code_std_desc_lps = models.CharField(max_length=200, null=True)
    use_code_std_ctgr_lps = models.CharField(max_length=5, null=True)
    use_code_std_ctgr_desc_lps = models.CharField(max_length=100, null=True)
    assessee_owner_name_1 = models.CharField(max_length=80, null=True)
    assessee_owner_name_2 = models.CharField(max_length=60, null=True)
    asmt_val_transfer = models.CharField(max_length=10, null=True)
    zoning = models.CharField(max_length=10, null=True)
    lot_size_area = models.FloatField(null=True)
    lot_size_area_unit = models.CharField(max_length=2, null=True)
    lot_size_area_orgn = models.CharField(max_length=14, null=True)
    yr_blt = models.CharField(max_length=255, null=True)
    building_sqft = models.IntegerField(null=True)
    stories_number = models.CharField(max_length=10, null=True)
    units_number = models.IntegerField(null=True)
    bedrooms = models.IntegerField(null=True)
    total_baths = models.IntegerField(null=True)
    garage_carport_type = models.CharField(max_length=3, null=True)
    garage_code_desc = models.CharField(max_length=50, null=True)
    parking_spaces = models.IntegerField(null=True)
    pool_indicator = models.CharField(max_length=1, null=True)
    construction_code_desc = models.CharField(max_length=50, null=True)
    price_per_sqft = models.DecimalField(max_digits=20, decimal_places=2, null=True)
    last_sale_date_transfer = models.CharField(max_length=255, null=True)
    last_market_sale_date_transfer = models.CharField(max_length=255, null=True)
    last_market_sale_val_transfer = models.DecimalField(max_digits=20, decimal_places=2, null=True)
    prior_sale_date_transfer = models.CharField(max_length=255, null=True)
    assessee_owner_2_indicator = models.CharField(max_length=1, null=True)
    assessee_owner_1_name_type = models.CharField(max_length=1, null=True)
    assessee_owner_2_name_type = models.CharField(max_length=1, null=True)
    yr_blt_effect = models.CharField(max_length=255, null=True)
    lot_width = models.IntegerField(null=True)
    lot_depth = models.IntegerField(null=True)
    location_id = models.CharField(max_length=50, null=True)
    address_id = models.CharField(max_length=50, null=True)
    parcel_dmp_id = models.CharField(max_length=21, null=True)
    shape = models.MultiPolygonField(srid=4269, null=True)
    last_market_sale_seller_name = models.CharField(max_length=50, null=True)
    last_market_sale_seller_1_first_mid = models.CharField(max_length=50, null=True)
    last_market_sale_seller_1_last = models.CharField(max_length=50, null=True)
    last_market_sale_seller_1_code = models.CharField(max_length=50, null=True)
    last_market_sale_seller_2_first_mid = models.CharField(max_length=50, null=True)
    last_market_sale_seller_2_last = models.CharField(max_length=50, null=True)
    owner_occupied = models.CharField(max_length=50, null=True)


class ParcelUpdate(Parcel):
    pass
