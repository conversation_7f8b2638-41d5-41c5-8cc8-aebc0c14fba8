from django_elasticsearch_dsl import Document
from django_elasticsearch_dsl.registries import registry
from farm.models import Parcel


@registry.register_document
class ParcelIndex(Document):
    class Index:
        name = 'parcel_farm'
        settings = {'number_of_shards': 1,
                    'number_of_replicas': 0}

    class Django:
        model = Parcel
        fields = Parcel.es_fields()


@registry.register_document
class InvestorsIndex(Document):
    class Index:
        name = 'investors_index'
        settings = {'number_of_shards': 1,
                    'number_of_replicas': 0}

    class Django:
        model = Parcel
        fields = Parcel.es_fields()
