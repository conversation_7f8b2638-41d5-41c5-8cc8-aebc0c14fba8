# Generated by Django 4.2.1 on 2024-11-11 11:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("farm", "0008_alter_territory_subscription_item"),
    ]

    operations = [
        migrations.AddField(
            model_name="parcel",
            name="last_market_sale_seller_1_code",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="parcel",
            name="last_market_sale_seller_1_first_mid",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="parcel",
            name="last_market_sale_seller_1_last",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="parcel",
            name="last_market_sale_seller_2_first_mid",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="parcel",
            name="last_market_sale_seller_2_last",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="parcel",
            name="last_market_sale_seller_name",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="unmatchedparcel",
            name="last_market_sale_seller_1_code",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="unmatchedparcel",
            name="last_market_sale_seller_1_first_mid",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="unmatchedparcel",
            name="last_market_sale_seller_1_last",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="unmatchedparcel",
            name="last_market_sale_seller_2_first_mid",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="unmatchedparcel",
            name="last_market_sale_seller_2_last",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="unmatchedparcel",
            name="last_market_sale_seller_name",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="parcel",
            name="owner_occupied",
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name="unmatchedparcel",
            name="owner_occupied",
            field=models.CharField(max_length=50, null=True),
        ),
    ]
