# Generated by Django 3.2.15 on 2022-09-05 13:47

import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('register', '0022_alter_stripewebhookevent_processing_errors'),
    ]

    operations = [
        migrations.CreateModel(
            name='Territory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('state', models.Char<PERSON>ield(max_length=255)),
                ('county', models.CharField(max_length=255)),
                ('city', models.CharField(max_length=255)),
                ('agent', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='register.agent')),
            ],
        ),
        migrations.CreateModel(
            name='UnmatchedParcel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fips_code', models.CharField(max_length=5, null=True)),
                ('parcel_apn', models.CharField(max_length=50, null=True)),
                ('taxapn', models.CharField(max_length=40, null=True)),
                ('site_addr', models.CharField(max_length=200, null=True)),
                ('site_house_number', models.CharField(max_length=13, null=True)),
                ('site_direction', models.CharField(max_length=2, null=True)),
                ('site_street_name', models.CharField(max_length=30, null=True)),
                ('site_mode', models.CharField(max_length=5, null=True)),
                ('site_carrier_code', models.CharField(max_length=4, null=True)),
                ('site_quadrant', models.CharField(max_length=2, null=True)),
                ('site_unit_prefix', models.CharField(max_length=10, null=True)),
                ('site_unit_number', models.CharField(max_length=8, null=True)),
                ('site_city', models.CharField(max_length=40, null=True)),
                ('site_county', models.CharField(max_length=40, null=True)),
                ('site_state', models.CharField(max_length=2, null=True)),
                ('site_zip', models.CharField(max_length=5, null=True)),
                ('site_plus_4', models.CharField(max_length=4, null=True)),
                ('_x_coord', models.FloatField(null=True)),
                ('_y_coord', models.FloatField(null=True)),
                ('addr_score', models.IntegerField(null=True)),
                ('owner_name', models.CharField(max_length=166, null=True)),
                ('owner_name_1', models.CharField(max_length=166, null=True)),
                ('owner_name_2', models.CharField(max_length=166, null=True)),
                ('owner_1_first', models.CharField(max_length=30, null=True)),
                ('owner_1_last', models.CharField(max_length=30, null=True)),
                ('owner_2_first', models.CharField(max_length=30, null=True)),
                ('owner_2_last', models.CharField(max_length=30, null=True)),
                ('mail_addr', models.CharField(max_length=200, null=True)),
                ('mail_house_number', models.CharField(max_length=13, null=True)),
                ('mail_direction', models.CharField(max_length=2, null=True)),
                ('mail_street_name', models.CharField(max_length=30, null=True)),
                ('mail_mode', models.CharField(max_length=5, null=True)),
                ('mail_quadrant', models.CharField(max_length=2, null=True)),
                ('mail_city', models.CharField(max_length=40, null=True)),
                ('mail_state', models.CharField(max_length=2, null=True)),
                ('mail_zip', models.CharField(max_length=5, null=True)),
                ('mail_plus_4', models.CharField(max_length=4, null=True)),
                ('mail_unit_prefix', models.CharField(max_length=10, null=True)),
                ('mail_unit_number', models.CharField(max_length=6, null=True)),
                ('owner_occupied', models.CharField(max_length=1, null=True)),
                ('use_code_muni', models.CharField(max_length=10, null=True)),
                ('use_code_muni_desc', models.CharField(max_length=45, null=True)),
                ('use_code_std_lps', models.CharField(max_length=100, null=True)),
                ('use_code_std_desc_lps', models.CharField(max_length=200, null=True)),
                ('use_code_std_ctgr_lps', models.CharField(max_length=5, null=True)),
                ('use_code_std_ctgr_desc_lps', models.CharField(max_length=100, null=True)),
                ('assessee_owner_name_1', models.CharField(max_length=80, null=True)),
                ('assessee_owner_name_2', models.CharField(max_length=60, null=True)),
                ('asmt_val_transfer', models.CharField(max_length=10, null=True)),
                ('zoning', models.CharField(max_length=10, null=True)),
                ('lot_size_area', models.FloatField(null=True)),
                ('lot_size_area_unit', models.CharField(max_length=2, null=True)),
                ('lot_size_area_orgn', models.CharField(max_length=14, null=True)),
                ('yr_blt', models.CharField(max_length=255, null=True)),
                ('building_sqft', models.IntegerField(null=True)),
                ('stories_number', models.CharField(max_length=10, null=True)),
                ('units_number', models.IntegerField(null=True)),
                ('bedrooms', models.IntegerField(null=True)),
                ('total_baths', models.IntegerField(null=True)),
                ('garage_carport_type', models.CharField(max_length=3, null=True)),
                ('garage_code_desc', models.CharField(max_length=50, null=True)),
                ('parking_spaces', models.IntegerField(null=True)),
                ('pool_indicator', models.CharField(max_length=1, null=True)),
                ('construction_code_desc', models.CharField(max_length=50, null=True)),
                ('price_per_sqft', models.DecimalField(decimal_places=2, max_digits=20, null=True)),
                ('last_sale_date_transfer', models.CharField(max_length=255, null=True)),
                ('last_market_sale_date_transfer', models.CharField(max_length=255, null=True)),
                ('last_market_sale_val_transfer', models.DecimalField(decimal_places=2, max_digits=20, null=True)),
                ('prior_sale_date_transfer', models.CharField(max_length=255, null=True)),
                ('assessee_owner_2_indicator', models.CharField(max_length=1, null=True)),
                ('assessee_owner_1_name_type', models.CharField(max_length=1, null=True)),
                ('assessee_owner_2_name_type', models.CharField(max_length=1, null=True)),
                ('yr_blt_effect', models.CharField(max_length=255, null=True)),
                ('lot_width', models.IntegerField(null=True)),
                ('lot_depth', models.IntegerField(null=True)),
                ('location_id', models.CharField(max_length=50, null=True)),
                ('address_id', models.CharField(max_length=50, null=True)),
                ('parcel_dmp_id', models.CharField(max_length=21, null=True)),
                ('shape', django.contrib.gis.db.models.fields.MultiPolygonField(null=True, srid=4269)),
                ('territory', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='farm.territory')),
            ],
        ),
        migrations.CreateModel(
            name='Parcel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fips_code', models.CharField(max_length=5, null=True)),
                ('parcel_apn', models.CharField(max_length=50, null=True)),
                ('taxapn', models.CharField(max_length=40, null=True)),
                ('site_addr', models.CharField(max_length=200, null=True)),
                ('site_house_number', models.CharField(max_length=13, null=True)),
                ('site_direction', models.CharField(max_length=2, null=True)),
                ('site_street_name', models.CharField(max_length=30, null=True)),
                ('site_mode', models.CharField(max_length=5, null=True)),
                ('site_carrier_code', models.CharField(max_length=4, null=True)),
                ('site_quadrant', models.CharField(max_length=2, null=True)),
                ('site_unit_prefix', models.CharField(max_length=10, null=True)),
                ('site_unit_number', models.CharField(max_length=8, null=True)),
                ('site_city', models.CharField(max_length=40, null=True)),
                ('site_county', models.CharField(max_length=40, null=True)),
                ('site_state', models.CharField(max_length=2, null=True)),
                ('site_zip', models.CharField(max_length=5, null=True)),
                ('site_plus_4', models.CharField(max_length=4, null=True)),
                ('_x_coord', models.FloatField(null=True)),
                ('_y_coord', models.FloatField(null=True)),
                ('addr_score', models.IntegerField(null=True)),
                ('owner_name', models.CharField(max_length=166, null=True)),
                ('owner_name_1', models.CharField(max_length=166, null=True)),
                ('owner_name_2', models.CharField(max_length=166, null=True)),
                ('owner_1_first', models.CharField(max_length=30, null=True)),
                ('owner_1_last', models.CharField(max_length=30, null=True)),
                ('owner_2_first', models.CharField(max_length=30, null=True)),
                ('owner_2_last', models.CharField(max_length=30, null=True)),
                ('mail_addr', models.CharField(max_length=200, null=True)),
                ('mail_house_number', models.CharField(max_length=13, null=True)),
                ('mail_direction', models.CharField(max_length=2, null=True)),
                ('mail_street_name', models.CharField(max_length=30, null=True)),
                ('mail_mode', models.CharField(max_length=5, null=True)),
                ('mail_quadrant', models.CharField(max_length=2, null=True)),
                ('mail_city', models.CharField(max_length=40, null=True)),
                ('mail_state', models.CharField(max_length=2, null=True)),
                ('mail_zip', models.CharField(max_length=5, null=True)),
                ('mail_plus_4', models.CharField(max_length=4, null=True)),
                ('mail_unit_prefix', models.CharField(max_length=10, null=True)),
                ('mail_unit_number', models.CharField(max_length=6, null=True)),
                ('owner_occupied', models.CharField(max_length=1, null=True)),
                ('use_code_muni', models.CharField(max_length=10, null=True)),
                ('use_code_muni_desc', models.CharField(max_length=45, null=True)),
                ('use_code_std_lps', models.CharField(max_length=100, null=True)),
                ('use_code_std_desc_lps', models.CharField(max_length=200, null=True)),
                ('use_code_std_ctgr_lps', models.CharField(max_length=5, null=True)),
                ('use_code_std_ctgr_desc_lps', models.CharField(max_length=100, null=True)),
                ('assessee_owner_name_1', models.CharField(max_length=80, null=True)),
                ('assessee_owner_name_2', models.CharField(max_length=60, null=True)),
                ('asmt_val_transfer', models.CharField(max_length=10, null=True)),
                ('zoning', models.CharField(max_length=10, null=True)),
                ('lot_size_area', models.FloatField(null=True)),
                ('lot_size_area_unit', models.CharField(max_length=2, null=True)),
                ('lot_size_area_orgn', models.CharField(max_length=14, null=True)),
                ('yr_blt', models.CharField(max_length=255, null=True)),
                ('building_sqft', models.IntegerField(null=True)),
                ('stories_number', models.CharField(max_length=10, null=True)),
                ('units_number', models.IntegerField(null=True)),
                ('bedrooms', models.IntegerField(null=True)),
                ('total_baths', models.IntegerField(null=True)),
                ('garage_carport_type', models.CharField(max_length=3, null=True)),
                ('garage_code_desc', models.CharField(max_length=50, null=True)),
                ('parking_spaces', models.IntegerField(null=True)),
                ('pool_indicator', models.CharField(max_length=1, null=True)),
                ('construction_code_desc', models.CharField(max_length=50, null=True)),
                ('price_per_sqft', models.DecimalField(decimal_places=2, max_digits=20, null=True)),
                ('last_sale_date_transfer', models.CharField(max_length=255, null=True)),
                ('last_market_sale_date_transfer', models.CharField(max_length=255, null=True)),
                ('last_market_sale_val_transfer', models.DecimalField(decimal_places=2, max_digits=20, null=True)),
                ('prior_sale_date_transfer', models.CharField(max_length=255, null=True)),
                ('assessee_owner_2_indicator', models.CharField(max_length=1, null=True)),
                ('assessee_owner_1_name_type', models.CharField(max_length=1, null=True)),
                ('assessee_owner_2_name_type', models.CharField(max_length=1, null=True)),
                ('yr_blt_effect', models.CharField(max_length=255, null=True)),
                ('lot_width', models.IntegerField(null=True)),
                ('lot_depth', models.IntegerField(null=True)),
                ('location_id', models.CharField(max_length=50, null=True)),
                ('address_id', models.CharField(max_length=50, null=True)),
                ('parcel_dmp_id', models.CharField(max_length=21, null=True)),
                ('shape', django.contrib.gis.db.models.fields.MultiPolygonField(null=True, srid=4269)),
                ('territory', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='parcels', to='farm.territory')),
            ],
        ),
    ]
