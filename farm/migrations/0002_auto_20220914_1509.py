# Generated by Django 3.2.15 on 2022-09-14 15:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('farm', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Addon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('package', models.CharField(choices=[('postcard mailer', 'postcard mailer'), ('online ad', 'online ad'), ('seller farm calling', 'seller farm calling')], default='postcard mailer', max_length=255)),
                ('stripe_price_id', models.CharField(max_length=255)),
            ],
        ),
        migrations.AddField(
            model_name='territory',
            name='addons',
            field=models.ManyToManyField(to='farm.Addon'),
        ),
    ]
