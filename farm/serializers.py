from random import randint

from rest_framework import serializers
from rest_framework_gis.serializers import GeoFeatureModelSerializer

from register.serializers import DetailedStripeSubscriptionItemSerializer
from .models import Parcel, Territory


class ParcelSerializer(GeoFeatureModelSerializer):
    class Meta:
        model = Parcel
        geo_field = "shape"
        fields = "__all__"


class SimpleParcelSerializer(GeoFeatureModelSerializer):
    class Meta:
        model = Parcel
        geo_field = "shape"
        fields = [
            "id",
            "site_addr",
            "owner_name",
            "site_zip",
            "_x_coord",
            "_y_coord",
            "shape",
        ]


class ParcelCsvSerializer(serializers.ModelSerializer):
    class Meta:
        model = Parcel
        fields = [
            "owner_name_1",
            "owner_name_2",
            "site_addr",
            "site_city",
            "site_county",
            "site_zip",
        ]


class TerritoryListSerializer(serializers.ModelSerializer):
    subscription_item = DetailedStripeSubscriptionItemSerializer(required=False)

    class Meta:
        model = Territory
        fields = "__all__"
        read_only_fields = [
            "id",
            "state",
            "county",
            "city",
            "subscription_item",
            "subscription",
        ]


class TerritorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Territory
        fields = "__all__"


class TerritoryListSerializerSecond(serializers.ModelSerializer):
    random_parcel = serializers.SerializerMethodField()
    territory = serializers.SerializerMethodField()

    class Meta:
        model = Parcel
        fields = ["territory", "random_parcel"]

    def get_territory(self, obj):
        try:
            get_territory = Territory.objects.get(id=obj["territory"])
            serializer = TerritoryListSerializer(get_territory, many=False)
            return serializer.data
        except Exception:
            return None

    def get_random_parcel(self, obj):
        queryset = Parcel.objects
        count = queryset.filter(territory_id=obj["territory"]).count()
        random_index = randint(0, count - 1)
        random_record = queryset.filter(territory_id=obj["territory"]).values(
            "site_addr",
            "site_house_number",
            "site_street_name",
            "site_zip",
            "bedrooms",
            "total_baths",
            "site_addr",
            "site_house_number",
            "last_sale_date_transfer",
            "last_market_sale_date_transfer",
            "last_market_sale_val_transfer",
            "prior_sale_date_transfer",
            "_x_coord",
            "_y_coord",
        )[random_index]
        return random_record


class TerritoryDetailSerializer(serializers.ModelSerializer):
    parcels = SimpleParcelSerializer(many=True, read_only=True)

    class Meta:
        model = Territory
        fields = ["id", "name", "state", "county", "city", "agent", "parcels"]
        read_only_fields = ["id", "name", "state", "county", "city"]
