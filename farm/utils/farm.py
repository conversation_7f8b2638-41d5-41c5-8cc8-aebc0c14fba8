import random
import string
import pandas as pd
import logging

from ..models import Territory, Parcel

logger = logging.getLogger("aws_logger")


def get_suffix(length):
    # choose from all lowercase letter
    letters = string.ascii_lowercase
    result_str = "".join(random.choice(letters) for i in range(length))
    return result_str


def fetch_farm_data(territory_id):
    try:
        territory = Territory.objects.get(id=territory_id)
        parcel_dict = Parcel.objects.filter(territory_id=territory.id).values(
            "owner_name_1", "owner_name_2", "mail_addr", "site_addr"
        )
        parcel_df = pd.DataFrame(parcel_dict)
        parcel_df.rename(
            columns={
                "owner_name_1": "Owner Name 1",
                "owner_name_1": "Owner Name 2",
                "site_addr": "Site Address",
                "mail_addr": "Mailing Address",
            }
        )
        suffix = get_suffix(8)
        file_name = f"tmp/{territory.name}-farm_data-{suffix}"
        parcel_df.to_excel(f"{file_name}", index=False)
        logger.info(f"Farm details attachment created successfully - {file_name}")
        return file_name

    except Exception as e:
        logger.error(f"Farm data fetch failed: {str(e)}")
        return None
