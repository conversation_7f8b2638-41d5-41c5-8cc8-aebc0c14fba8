from django.core.management.base import BaseCommand
from elasticsearch.helpers import bulk

from farm.models import Parcel
from bpo_listings.es_search import es  # Import the Elasticsearch instance


class Command(BaseCommand):
    help = "Load data from the Parcel model into the Elasticsearch index"

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS("Starting the process..."))

        index_name = "testme"

        # Check if the index exists
        if not es.indices.exists(index=index_name):
            self.stdout.write(self.style.ERROR(f'Index "{index_name}" does not exist! Create it first.'))
            return

        self.stdout.write(self.style.SUCCESS(f"The index is: {index_name}"))

        # Query the Parcel model
        parcel_queryset = Parcel.objects.exclude(site_addr__exact="").exclude(site_addr__isnull=True)[:10]
        count = parcel_queryset.count()
        self.stdout.write(self.style.SUCCESS(f"Count of parcels to index: {count}"))

        actions = []
        for index_number, parcel in enumerate(parcel_queryset, start=1):
            self.stdout.write(self.style.SUCCESS(f"Iterated Number: {index_number}"))
            action = {
                "_op_type": "index",
                "_index": index_name,
                "_id": parcel.id,
                "_source": parcel.es_to_dict(),
            }
            actions.append(action)

        self.stdout.write(self.style.SUCCESS(f"Number of actions to index: {len(actions)}"))

        # Perform the bulk indexing
        success, failed = bulk(client=es, actions=actions)

        self.stdout.write(self.style.SUCCESS("Data loaded into Elasticsearch index successfully."))
        self.stdout.write(self.style.SUCCESS(f"SUCCESS: {success}"))
        self.stdout.write(self.style.SUCCESS(f"FAILED: {failed}"))
