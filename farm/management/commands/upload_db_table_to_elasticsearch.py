import os

from django.core.management.base import BaseCommand
from django.db import connections
from elasticsearch import Elasticsearch, helpers

ELASTIC_PASSWORD = os.getenv("ELASTIC_PASSWORD")
ELASTIC_USERNAME = os.getenv("ELASTIC_USERNAME")
ELASTIC_URL = os.getenv("ELASTIC_URL")
ELASTIC_PORT = os.getenv("ELASTIC_PORT")

# ... (Elasticsearch configuration)
es = Elasticsearch(
    hosts=[f"{ELASTIC_URL}:{ELASTIC_PORT}"],
    http_auth=(f"{ELASTIC_USERNAME}", f"{ELASTIC_PASSWORD}"),
    use_ssl=False,
    verify_certs=False,
)


class Command(BaseCommand):
    help = "Fetches data in chunks and updates Elasticsearch"

    def handle(self, *args, **options):
        cursor = connections['db_name_from_settings'].cursor()
        offset = 0
        chunk_size = 100000
        max_id = 11000000
        error_chunks = []  # List to store chunk ranges with errors

        while offset < max_id:
            try:
                # ... (Query and action generation)
                sql_statement = """
                SELECT
                  id,
                  territory_id,
                  fips_code,
                  parcel_apn,
                  taxapn,
                  site_addr,
                  site_house_number,
                  site_direction,
                  site_street_name,
                  site_mode,
                  site_carrier_code,
                  site_quadrant,
                  site_unit_prefix,
                  site_unit_number,
                  site_city,
                  site_county,
                  site_state,
                  site_zip,
                  site_plus_4,
                  _x_coord,
                  _y_coord,
                  addr_score,
                  owner_name,
                  owner_name_1,
                  owner_name_2,
                  owner_1_first,
                  owner_1_last,
                  owner_2_first,
                  owner_2_last,
                  mail_addr,
                  mail_house_number,
                  mail_direction,
                  mail_street_name,
                  mail_mode,
                  mail_quadrant,
                  mail_city,
                  mail_state,
                  mail_zip,
                  mail_plus_4,
                  mail_unit_prefix,
                  mail_unit_number,
                  owner_occupied,
                  use_code_muni,
                  use_code_muni_desc,
                  use_code_std_lps,
                  use_code_std_desc_lps,
                  use_code_std_ctgr_lps,
                  use_code_std_ctgr_desc_lps,
                  assessee_owner_name_1,
                  assessee_owner_name_2,
                  asmt_val_transfer,
                  zoning,
                  lot_size_area,
                  lot_size_area_unit,
                  lot_size_area_orgn,
                  yr_blt,
                  building_sqft,
                  stories_number,
                  units_number,
                  bedrooms,
                  total_baths,
                  garage_carport_type,
                  garage_code_desc,
                  parking_spaces,
                  pool_indicator,
                  construction_code_desc,
                  price_per_sqft,
                  last_sale_date_transfer,
                  last_market_sale_date_transfer,
                  last_market_sale_val_transfer,
                  prior_sale_date_transfer,
                  assessee_owner_2_indicator,
                  assessee_owner_1_name_type,
                  assessee_owner_2_name_type,
                  yr_blt_effect,
                  lot_width,
                  lot_depth,
                  location_id,
                  address_id,
                  last_market_sale_seller_name,
                  last_market_sale_seller_1_first_mid,
                  last_market_sale_seller_1_last,
                  last_market_sale_seller_1_code,
                  last_market_sale_seller_2_first_mid,
                  last_market_sale_seller_2_last,
                  parcel_dmp_id
                FROM farm_parcel
                ORDER BY id
                LIMIT %s OFFSET %s
                """

                # Use this SQL statement in your code
                cursor.execute(sql_statement, [chunk_size, offset])

                rows = cursor.fetchall()
                column_names = [col[0] for col in cursor.description]
                key_value_rows = [dict(zip(column_names, row)) for row in rows]

                actions = [
                    {
                        "_index": "parcel_farm",
                        "_id": row.get("id"),
                        "_source": row,
                        "doc_as_upsert": True,
                    }
                    for row in key_value_rows
                ]

                helpers.bulk(es, actions)

                print(f"Updated chunk {offset}-{offset + chunk_size - 1}")
                offset += chunk_size

            except Exception as e:
                error_chunks.append((offset, offset + chunk_size - 1))  # Log error chunk
                print(f"Error for {offset, offset + chunk_size - 1 }")
                print(f"Error occurred: {e.args}")
                offset += chunk_size  # Move to the next chunk

        if error_chunks:
            print("Chunks with errors:", error_chunks)  # Print list of error chunks
        else:
            print("All chunks updated successfully!")
