from django.core.management.base import BaseCommand
from farm.models import Parcel, Territory


SITE_STATE = "CA"


class Command(BaseCommand):
    help = "Segments parcels into territories"

    # Yield successive n-sized
    # chunks from dataset

    def divide_parcels(self, dataset, n):
        # looping till length dataset
        for i in range(0, len(dataset), n):
            yield dataset[i : i + n]

    def create_territory_for_parcels(self, parcels, county, city):
        territory = Territory.objects.create(name="Territory #")
        territory.state = SITE_STATE
        territory.county = county
        territory.city = city
        territory.name = territory.name + str(territory.id)
        territory.save()

        for parcel in parcels:
            parcel.territory_id = territory.id
            parcel.save(update_fields=["territory_id"])

    def handle(self, *args, **options):
        print("Creating farm territories...")
        current_county = ""
        current_city = ""
        parcel_cities = []
        grouped_parcels = []
        chunk_size = 300
        count = 0

        parcel_counties = (
            Parcel.objects.only("site_county")
            .distinct("site_county")
            .order_by("site_county")
        )
        for county_pack in parcel_counties:
            current_county = county_pack.site_county
            parcel_cities = (
                Parcel.objects.only("site_city")
                .distinct("site_city")
                .order_by("site_city")
            )
            for city_pack in parcel_cities:
                current_city = city_pack.site_city
                parcels = (
                    Parcel.objects.filter(
                        site_state=SITE_STATE,
                        site_county=current_county,
                        site_city=current_city,
                    )
                    .exclude(site_city__isnull=True)
                    .exclude(site_city__exact="")
                    .exclude(territory_id__isnull=False)
                    .only("id")
                    .order_by("site_zip")
                )

                # grouped_parcels = list(self.divide_parcels(parcels, n=300))
                grouped_parcels = (
                    parcels[i : i + chunk_size]
                    for i in range(0, len(parcels), chunk_size)
                )

                for chunk in grouped_parcels:
                    if len(chunk) < chunk_size:
                        break
                    self.create_territory_for_parcels(
                        parcels=chunk, county=current_county, city=current_city
                    )
                    count = count + 1
                    print(
                        f"Territory number {count} in {current_county}, {current_city}"
                    )
        print("Session completed")
