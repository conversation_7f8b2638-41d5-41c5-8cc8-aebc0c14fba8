import os
from bpohomes_backend.settings import BASE_DIR
from django.core.management.base import BaseCommand
import csv
from farm.models import Territory


csv_file_path = os.path.join(BASE_DIR, "investors/data", "data.csv")


class Command(BaseCommand):
    help = "Count farms in a given number of cities in excel file"

    def handle(self, *args, **options):
        print("Starting command.........")
        count = 0
        
        with open(csv_file_path, "r") as file:
            reader = csv.DictReader(file)
            for row in reader:
                city_count = Territory.objects.filter(city__icontains=row.get("name")).count()
                count += city_count
                print(f"{row.get('name')} : {city_count}")
                print(f"Total count is {count}")
                print("=======================================")

        print(f"Done: Total count is  {count}")
