from django.core.management.base import BaseCommand
from elasticsearch_dsl import Index
from farm.document import ParcelIndex, InvestorsIndex


class Command(BaseCommand):
    help = "Create Elasticsearch index for Parcel model"

    def add_arguments(self, parser):
        parser.add_argument(
            "index_name",
            nargs="+",
            type=str,
            help="The name of the Elasticsearch index to upload " "files to",
        )

    def handle(self, *args, **kwargs):
        index_name = kwargs["index_name"][0]
        try:
            # Create an index object
            index = Index(index_name)

            # Check if the index already exists
            if index.exists():
                self.stdout.write(
                    self.style.SUCCESS(f'Index "{index_name}" already exists.')
                )
            else:
                # Bind the ParcelIndex to this index and create it
                ParcelIndex.Index.name = index_name  # Set custom index name
                ParcelIndex.init()

                # Optionally create the InvestorsIndex (use a different name if needed)
                InvestorsIndex.init()

                self.stdout.write(
                    self.style.SUCCESS(f'Index "{index_name}" created successfully.')
                )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"An error occurred: {str(e)}"))
