from elasticsearch_dsl import Search
from elasticsearch_dsl.connections import connections
from django.core.management.base import BaseCommand
from elasticsearch import exceptions as es_exceptions


class Command(BaseCommand):
    help = 'List all records in the Elasticsearch index'

    def add_arguments(self, parser):
        parser.add_argument('index_name', nargs='+', type=str, help='The name of the Elasticsearch index to list '
                                                                    'records from')

    def handle(self, *args, **options):
        index_name = options['index_name'][0]
        try:
            self.stdout.write(self.style.SUCCESS(f'This is the connection: {connections.get_connection()}'))
            search = Search(index=index_name)

            response = search.execute()
            for ind, hit in enumerate(response):
                document_data = hit.to_dict()
                self.stdout.write(self.style.SUCCESS(f"{document_data, ind}"))

        except es_exceptions.NotFoundError:
            self.stdout.write(self.style.ERROR(f'Index "{index_name}" does not exist! Create it first.'))
