from django.core.management.base import BaseCommand
from elasticsearch.helpers import bulk

from farm.models import Parcel
from bpo_listings.es_search import es  # Import the Elasticsearch instance


class Command(BaseCommand):
    help = "Load data from the Parcel model into the Elasticsearch index"

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS("Starting the process..."))

        index_name = "parcel_farm"
        batch_size = 10000  # Define batch size
        total_parcels = Parcel.objects.exclude(site_addr__exact="").exclude(site_addr__isnull=True).count()

        # Check if the index exists
        if not es.indices.exists(index=index_name):
            self.stdout.write(self.style.ERROR(f'Index "{index_name}" does not exist! Create it first.'))
            return

        self.stdout.write(self.style.SUCCESS(f"The index is: {index_name}"))
        self.stdout.write(self.style.SUCCESS(f"Total parcels to process: {total_parcels}"))

        start = 0
        while start < total_parcels:
            # Fetch parcels in the current batch
            end = min(start + batch_size, total_parcels)
            self.stdout.write(self.style.SUCCESS(f"Processing batch {start + 1} to {end}..."))

            parcel_queryset = (
                Parcel.objects.exclude(site_addr__exact="")
                .exclude(site_addr__isnull=True)
                .order_by("id")[start:end]
            )

            # Prepare actions for the bulk operation
            actions = [
                {
                    "_op_type": "index",
                    "_index": index_name,
                    "_id": parcel.id,
                    "_source": parcel.es_to_dict(),
                }
                for parcel in parcel_queryset
            ]

            # Perform the bulk indexing
            success, failed = bulk(client=es, actions=actions)
            self.stdout.write(self.style.SUCCESS(f"Batch {start + 1} to {end} indexed successfully."))
            self.stdout.write(self.style.SUCCESS(f"SUCCESS: {success} | FAILED: {failed}"))

            start += batch_size

        self.stdout.write(self.style.SUCCESS("All data has been loaded into Elasticsearch index successfully."))
