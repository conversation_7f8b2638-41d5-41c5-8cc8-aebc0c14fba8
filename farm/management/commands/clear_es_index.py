from elasticsearch import Elasticsearch
from elasticsearch_dsl import Search, Index
from elasticsearch_dsl.connections import connections
from django.core.management.base import BaseCommand
from elasticsearch import exceptions as es_exceptions


class Command(BaseCommand):
    help = 'Clear all records from the Elasticsearch index'

    def add_arguments(self, parser):
        parser.add_argument('index_name', nargs='+', type=str, help='The name of the Elasticsearch index to clear')

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS(f'This is the connection: {connections.get_connection()}'))
        index_name = options['index_name'][0]
        try:
            client = Elasticsearch()
            # s = Search(using=client, index=index_name)
            # d = [hit.to_dict() for hit in s.scan()]
            # print(len(d))

            ind = Index(index_name)
            ind.delete()
            self.stdout.write(self.style.SUCCESS(f'All records in index "{index_name}" have been deleted.'))
        except es_exceptions.NotFoundError:
            self.stdout.write(self.style.ERROR(f'Index "{index_name}" does not exist! Create it first.'))
