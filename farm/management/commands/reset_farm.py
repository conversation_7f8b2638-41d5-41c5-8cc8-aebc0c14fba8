from django.core.management.base import BaseCommand
from elasticsearch.helpers import bulk
from bpo_listings.es_search import es  # Import the Elasticsearch instance
from farm.models import Parcel


class Command(BaseCommand):
    help = "Reset the parcel_farm index and reload data from the Parcel model"

    def handle(self, *args, **kwargs):
        index_name = "parcel_farm"

        self.stdout.write(self.style.SUCCESS("Starting the reset process..."))

        # Delete the existing index if it exists
        if es.indices.exists(index=index_name):
            self.stdout.write(self.style.WARNING(f'Deleting existing index "{index_name}"...'))
            es.indices.delete(index=index_name)
            self.stdout.write(self.style.SUCCESS(f'Index "{index_name}" deleted successfully.'))

        # Recreate the index
        self.stdout.write(self.style.SUCCESS(f'Creating index "{index_name}"...'))
        es.indices.create(index=index_name, body={
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 1
            },
            "mappings": {
                "properties": {
                    # Define your mappings here based on Parcel model fields
                    "site_addr": {"type": "text"},
                    "name": {"type": "text"},
                    "location": {"type": "geo_point"},  # Example of a geo_point field
                    "created_at": {"type": "date"}
                }
            }
        })
        self.stdout.write(self.style.SUCCESS(f'Index "{index_name}" created successfully.'))

        # Query the Parcel model
        self.stdout.write(self.style.SUCCESS("Loading data from the Parcel model..."))
        parcel_queryset = Parcel.objects.exclude(site_addr__exact="").exclude(site_addr__isnull=True)
        actions = [
            {
                "_op_type": "index",
                "_index": index_name,
                "_id": parcel.id,
                "_source": parcel.es_to_dict(),
            }
            for parcel in parcel_queryset
        ]

        # Perform the bulk indexing
        success, failed = bulk(client=es, actions=actions)

        self.stdout.write(self.style.SUCCESS("Data loaded into Elasticsearch index successfully."))
        self.stdout.write(self.style.SUCCESS(f"SUCCESS: {success}"))
        self.stdout.write(self.style.SUCCESS(f"FAILED: {failed}"))
