import csv
import sys
import os

from elasticsearch.helpers.errors import BulkIndexError
from elasticsearch_dsl import Index, connections
from elasticsearch.helpers import bulk
from django.core.management.base import BaseCommand
from farm.models import Parcel

maxInt = sys.maxsize

while True:
    # decrease the maxInt value by factor 10
    # as long as the OverflowError occurs.

    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt / 10)


class Command(BaseCommand):
    help = "Upload CSV files to Elasticsearch"

    def add_arguments(self, parser):
        parser.add_argument(
            "index_name",
            nargs="+",
            type=str,
            help="The name of the Elasticsearch index to upload " "files to",
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(f"Starting to upload csv data to elasticsearch...")
        )

        # Folder containing CSV files
        csv_folder = "/home/<USER>/farm/data"
        # csv_folder = '/home/<USER>/Documents/Jotella/file'

        # Elasticsearch index name
        index_name = options["index_name"][0]
        if not Index(index_name).exists():
            self.stdout.write(
                self.style.ERROR(
                    f'Index "{index_name}" does not exist! Create it first.'
                )
            )
            return
        self.stdout.write(self.style.SUCCESS(f"The index is: {index_name}"))

        # def upload_csv(csv_file_path):
        #     actions = []
        #     try:
        #         with open(csv_file_path, "r") as csv_file:
        #             next(csv_file)
        #             reader = csv.DictReader(
        #                 csv_file, fieldnames=[field for field in Parcel.es_fields()]
        #             )
        #
        #             for row in reader:
        #                 for key, value in row.items():
        #                     if value == "":
        #                         row[key] = None
        #                 action = {
        #                     "_op_type": "index",
        #                     "_index": index_name,
        #                     "_id": row.get("id", None),
        #                     "_source": row,
        #                 }
        #                 actions.append(action)
        #     except FileNotFoundError:
        #         self.stdout.write(
        #             self.style.ERROR(f'File "{csv_file_path}" not found.')
        #         )
        #         return
        #
        #     try:
        #         self.stdout.write(
        #             self.style.SUCCESS(
        #                 f"Uploading data from {csv_file_path} to Elasticsearch..."
        #             )
        #         )
        #         success, failed = bulk(
        #             client=connections.get_connection(), actions=actions
        #         )
        #         self.stdout.write(self.style.SUCCESS(f"SUCCESS: {success}"))
        #         self.stdout.write(self.style.SUCCESS(f"FAILED: {failed}"))
        #         self.stdout.write(
        #             self.style.SUCCESS(f"Upload complete for {csv_file_path}")
        #         )
        #     except BulkIndexError as e:
        #         error_count = len(e.errors)
        #         self.stdout.write(
        #             self.style.ERROR(f"{error_count} document(s) failed to index.")
        #         )
        #         for error in e.errors:
        #             self.stdout.write(self.style.ERROR(f"Error: {error}"))
        #     except Exception as e:
        #         self.stdout.write(self.style.ERROR(f"Major Error: {e.args}"))

        # def upload_csv(csv_file_path, chunk_size=100000):
        #     actions = []
        #     try:
        #         with open(csv_file_path, "r") as csv_file:
        #             next(csv_file)
        #             reader = csv.DictReader(
        #                 csv_file, fieldnames=[field for field in Parcel.es_fields()]
        #             )
        #
        #             chunk = []
        #             for row in reader:
        #                 try:
        #                     for key, value in row.items():
        #                         if value == "":
        #                             row[key] = None
        #
        #                     action = {
        #                         "_op_type": "index",
        #                         "_index": index_name,
        #                         "_id": row.get("id", None),
        #                         "_source": row,
        #                     }
        #                     chunk.append(action)
        #
        #                     if len(chunk) == chunk_size:
        #                         # Perform bulk upload for the current chunk
        #                         success, failed = bulk(
        #                             client=connections.get_connection(), actions=chunk
        #                         )
        #                         self.stdout.write(
        #                             self.style.SUCCESS(f"SUCCESS: {success}")
        #                         )
        #                         self.stdout.write(
        #                             self.style.SUCCESS(f"FAILED: {failed}")
        #                         )
        #                         actions.extend(chunk)
        #                         chunk = []
        #                 except Exception as e:
        #                     self.stdout.write(
        #                         self.style.ERROR(f"Error processing chunk: {e}")
        #                     )
        #
        #             # Upload any remaining actions
        #             if chunk:
        #                 try:
        #                     success, failed = bulk(
        #                         client=connections.get_connection(), actions=chunk
        #                     )
        #                     self.stdout.write(self.style.SUCCESS(f"SUCCESS: {success}"))
        #                     self.stdout.write(self.style.SUCCESS(f"FAILED: {failed}"))
        #                     actions.extend(chunk)
        #                 except BulkIndexError as e:
        #                     error_count = len(e.errors)
        #                     self.stdout.write(
        #                         self.style.ERROR(
        #                             f"{error_count} document(s) failed to index."
        #                         )
        #                     )
        #                     for error in e.errors:
        #                         self.stdout.write(self.style.ERROR(f"Error: {error}"))
        #
        #         self.stdout.write(
        #             self.style.SUCCESS(f"Upload complete for {csv_file_path}")
        #         )
        #     except FileNotFoundError:
        #         self.stdout.write(
        #             self.style.ERROR(f'File "{csv_file_path}" not found.')
        #         )
        #     except Exception as e:
        #         self.stdout.write(self.style.ERROR(f"Major Error: {e.args}"))

        def upload_csv(csv_file_path):
            actions = []
            client = connections.get_connection()
            try:
                with open(csv_file_path, "r") as csv_file:
                    next(csv_file)
                    reader = csv.DictReader(
                        csv_file, fieldnames=[field for field in Parcel.es_fields()]
                    )

                    for row in reader:
                        try:
                            for key, value in row.items():
                                if value == "":
                                    row[key] = None

                            # action = {
                            #     "_op_type": "index",
                            #     "_index": index_name,
                            #     "_id": row.get("id", None),
                            #     "_source": row,
                            # }
                            response = client.index(
                                index=index_name,
                                id=row.get("id", None),
                                body=row,
                                op_type="create",
                            )
                        except Exception as e:
                            self.stdout.write(
                                self.style.ERROR(
                                    f"document(s) failed to index."
                                )
                            )
                            self.stdout.write(self.style.ERROR(f"Error: {e}"))

                self.stdout.write(
                    self.style.SUCCESS(f"Upload complete for {csv_file_path}")
                )
            except FileNotFoundError:
                self.stdout.write(
                    self.style.ERROR(f'File "{csv_file_path}" not found.')
                )
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Major Error: {e.args}"))

        def upload_all_csvs():
            for filename in os.listdir(csv_folder):
                if filename.endswith(".csv"):
                    csv_file_path = os.path.join(csv_folder, filename)
                    upload_csv(csv_file_path)

            self.stdout.write(
                self.style.SUCCESS(
                    "All CSV files in the folder have been uploaded to Elasticsearch."
                )
            )

        upload_all_csvs()
