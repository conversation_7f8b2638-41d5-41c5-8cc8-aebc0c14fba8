from django.contrib import admin
from django.db.models import Count
from django.utils.html import format_html, urlencode
from django.urls import reverse
from import_export import resources
from . import models

# Register your models here.


admin.site.register(models.UnmatchedParcel)


@admin.register(models.Parcel)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['id', 'territory', 'territory_id']


@admin.register(models.Territory)
class PaymendtAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', ]