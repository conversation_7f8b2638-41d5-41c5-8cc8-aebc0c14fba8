import logging
from django.core.mail import BadHeaderError
from templated_mail.mail import BaseEmailMessage

logger = logging.getLogger("aws_logger")


def send_farm_purchase_email(
    agent_name, agent_email, package_grade, territory_name, territory_info
):
    header = "Farm Purchase Notice"
    recipient = "<EMAIL>"

    context = {
        "header": header,
        "agent_name": agent_name,
        "agent_email": agent_email,
        "package_grade": package_grade,
        "territory_name": territory_name,
    }

    try:
        message = BaseEmailMessage(
            template_name="emails/farm_purchase.html", context=context
        )
        message.attach_file(territory_info, "text/csv")
        message.send([recipient])
        logger.info("Farm purchase email successfully sent!")
    except BadHeaderError:
        logger.error("Sending farm purchase email failed - Bad header error")
    except Exception as e:
        logger.error(f"Sending farm purchase email failed - {str(e)}")


def send_admin_farm_or_premuim_email(client_name, client_email, agent_name):
    header = "Welcome to BPO Homes"

    context = {"header": header, "client_name": client_name, "agent_name": agent_name}

    recipient = client_email
    try:
        message = BaseEmailMessage(
            template_name="emails/client_response.html", context=context
        )
        message.send([recipient])
    except BadHeaderError:
        pass
