from django.urls import path
from . import views


urlpatterns = [
    # path('parcels/', views.ParcelAPIView.as_view()), 
    # path('parcels/<int:id>/', views.ParcelAPIDetailView.as_view()),
    path('territories/', views.TerritoryAPIView.as_view()), 
    path('territories_second_search/', views.TerritoryAPIViewSecond.as_view()), 
    path('territories/<int:id>/', views.TerritoryDetailAPIView.as_view()),
    path('exportcsv/', views.GetParcelsInTerritory.as_view()),
    path('get-territory/', views.GetTerritoryByAgent.as_view()),
    path('investors/', views.GetInvestorsSearch.as_view(), name="get-farm-investors"),

]
