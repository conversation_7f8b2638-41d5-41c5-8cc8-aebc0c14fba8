from __future__ import absolute_import, unicode_literals

from celery import shared_task
from celery.utils.log import get_task_logger
from .models import Territory
from .utils.farm import fetch_farm_data

from .email import send_farm_purchase_email, send_admin_farm_or_premuim_email

logger = get_task_logger(__name__)


@shared_task()
def send_farm_purchase_email_task(agent_name, agent_email, package_grade, territory_id):
    logger.info("Initializing farm purchase email.")
    territory = Territory.objects.get(id=territory_id)
    territory_info = fetch_farm_data(territory_id=territory.id)

    return send_farm_purchase_email(
        agent_name=agent_name,
        agent_email=agent_email,
        package_grade=package_grade,
        territory_name=territory.name,
        territory_info=territory_info,
    )


@shared_task()
def send_admin_farm_or_premuim_email_task(agent_name, client_name, client_email):
    logger.info("Sending farm premium email")
    return send_admin_farm_or_premuim_email(
        client_name=client_name,
        client_email=client_email,
        agent_name=agent_name,
    )
