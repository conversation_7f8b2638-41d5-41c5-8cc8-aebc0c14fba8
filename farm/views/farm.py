import abc

from elasticsearch_dsl import Q
from rest_framework import generics, mixins, status
from rest_framework.pagination import PageNumberPagination
from rest_framework_simplejwt.authentication import JWTAuthentication
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q as DjangoQ
from farm.document import ParcelIndex
from farm.models import Parcel, Territory
from farm.serializers import (
    ParcelSerializer,
    TerritoryListSerializer,
    TerritoryDetailSerializer,
    TerritoryListSerializerSecond,
    ParcelCsvSerializer,
    TerritorySerializer,
)
from farm.filters import TerritoryFilter


class ParcelDetailAPIView(mixins.UpdateModelMixin, generics.RetrieveAPIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)
    serializer_class = ParcelSerializer
    queryset = Parcel.objects.all()
    lookup_field = "id"

    def get_queryset(self):
        qs = Parcel.objects.all()[:10]
        query = self.request.GET.get("q")
        if query is not None:
            qs = qs.filter(site_county__iexact=query)
        return qs

    def put(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)

    def patch(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)


class ParcelAPIView(mixins.CreateModelMixin, generics.ListAPIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)
    serializer_class = ParcelSerializer

    def get_queryset(self):
        qs = Parcel.objects.all()[:10]
        query = self.request.GET.get("q")
        if query is not None:
            qs = qs.filter(city__iexact=query)
        return qs

    def post(self, request, *args, **kwargs):
        return self.create(request, *args, **kwargs)


class TerritoryDetailAPIView(mixins.UpdateModelMixin, generics.RetrieveAPIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)
    serializer_class = TerritoryDetailSerializer
    lookup_field = "id"

    def get_queryset(self):
        if self.request.method == "GET":
            qs = Territory.objects.select_related("agent")
            return qs
        qs = Territory.objects.prefetch_related("parcels")
        query = self.request.GET.get("q")
        if query is not None:
            qs = qs.filter(city__iexact=query)
        return qs

    def patch(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)


class TerritoryAPIView(generics.ListAPIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)
    filter_backends = [DjangoFilterBackend]
    filterset_class = TerritoryFilter
    serializer_class = TerritoryListSerializer

    def get_queryset(self):
        qs = Territory.objects.select_related("subscription_item").select_related(
            "subscription_item__parent_subscription"
        )
        query = self.request.GET.get("agent_id")
        if query is not None:
            qs = qs.filter(agent__exact=query)
        else:
            qs = Territory.objects.available()
        return qs


class TerritoryAPIViewSecond(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        county = self.request.GET.get("county", None)
        city = self.request.GET.get("city", None)
        zip_code = self.request.GET.get("zip_code", None)
        state = self.request.GET.get("state", None)

        if all(v is None for v in [county, city, zip_code]):
            return Response({"message": "Enter at least one field"})
        
        # Build dynamic query using Q objects
        query = DjangoQ(territory__agent__isnull=True)

        if county:
            query &= DjangoQ(territory__county__icontains=county)
        if city:
            query &= DjangoQ(territory__city__icontains=city)
        if zip_code:
            query &= DjangoQ(site_zip=zip_code)
        if state:
            query &= DjangoQ(territory__state__icontains=state)


        qs = (
            Parcel.objects.filter(query)
            .select_related("territory__subscription_item", "territory__subscription_item__parent_subscription")
            .values("territory")
            .distinct()
        )

        serializer = TerritoryListSerializerSecond(qs, many=True)

        return Response(serializer.data)


class GetParcelsInTerritory(APIView):
    permission_classes = (IsAuthenticated,)
    authentication_classes = (JWTAuthentication,)

    def get(self, *args, **kwargs):
        territory_id = self.request.GET.get("territory_id", None)

        if territory_id:
            get_parcels = Parcel.objects.filter(territory_id=territory_id)

            serializer = ParcelCsvSerializer(get_parcels, many=True)

            return Response(serializer.data)

        return Response({"message": "Enter territory id"})


class GetTerritoryByAgent(APIView):
    permission_classes = []

    def get(self, request):
        agent_id = self.request.GET.get("agent_id", None)
        sub_id = self.request.GET.get("sub_id", None)
        territory_id = self.request.GET.get("territory_id", None)

        if agent_id:
            get_territory = Territory.objects.filter(agent_id=agent_id)
        if sub_id:
            get_territory = Territory.objects.filter(subscription_item_id=sub_id)
        if territory_id:
            get_territory = Territory.objects.filter(id=territory_id)

        serializer = TerritorySerializer(get_territory, many=True)

        return Response(serializer.data)


class CustomPageNumberPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


class PaginatedElasticSearchAPIView(generics.ListAPIView):
    serializer_class = None
    document_class = None
    pagination_class = CustomPageNumberPagination

    @abc.abstractmethod
    def generate_q_expression(self, address, zip_code, suggestion):
        """This method should be overridden
        and return a Q() expression."""

    def get(self, request, *args, **kwargs):
        address = self.request.GET.get("address", None)
        zip_code = self.request.GET.get("zip_code", None)
        suggestion = self.request.GET.get("suggestion", "")
        try:
            q = self.generate_q_expression(address, zip_code, suggestion)
            search = self.document_class.search().query(q[0])
            search = search.params(size=q[1])
            response = search.execute()

            page = self.paginate_queryset(response.hits)
            serializer = self.serializer_class(page, many=True, context={"request": self.request})
            return self.get_paginated_response(serializer.data)

        except Exception as e:
            return Response(e.args, status=status.HTTP_404_NOT_FOUND)


class GetInvestorsSearch(PaginatedElasticSearchAPIView):
    serializer_class = ParcelSerializer
    document_class = ParcelIndex

    def generate_q_expression(self, address, zip_code, suggestion):

        if address:
            return Q(
                'match', site_addr=address), 1

        if zip_code:
            return Q(
                'term', site_zip=zip_code
            ), 100

        return Q('match', site_addr=suggestion), 10

# 502 MACE BLVD STE 15